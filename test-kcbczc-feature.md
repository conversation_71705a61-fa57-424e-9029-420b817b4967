# 科创板成长层权限开通 - 科创板未开通提示功能

## 功能说明
在科创板成长层权限开通业务的账户选择页面中，当账户的 `holderName` 为"科创板未开通"时，会显示特殊的提示信息，并提供跳转到科创板权限开通业务的功能。

## 实现内容

### 1. 模板修改
在 `src/views/busComp/selectAcctList.vue` 中添加了条件渲染的提示区域：

```vue
<!-- 科创板成长层权限开通业务：科创板未开通提示 -->
<div 
  v-if="type === 'kcbczc' && item.holderName === '科创板未开通'"
  class="kcb_unopened_tip"
>
  <p class="tip_text">该账户不存在科创板权限，开通后可办理。</p>
  <p class="tip_link" @click="goToKcbOpen">前往开通 >></p>
</div>
```

### 2. 方法添加
添加了跳转到科创板权限开通业务的方法：

```javascript
/**
 * 跳转到科创板权限开通业务
 */
goToKcbOpen() {
  this.$router.push({
    name: 'business',
    query: {
      type: 'kcbkt'
    }
  })
},
```

### 3. 样式设计
添加了美观的提示样式：

```css
/* 科创板未开通提示样式 */
.kcb_unopened_tip {
  margin-top: 8px;
  padding: 10px;
  background-color: #fff7e6;
  border: 1px solid #ffd591;
  border-radius: 4px;
  font-size: 12px;
}

.kcb_unopened_tip .tip_text {
  color: #fa8c16;
  margin: 0 0 5px 0;
  line-height: 1.4;
}

.kcb_unopened_tip .tip_link {
  color: #1890ff;
  margin: 0;
  cursor: pointer;
  text-decoration: underline;
  line-height: 1.4;
}

.kcb_unopened_tip .tip_link:hover {
  color: #40a9ff;
}
```

## 触发条件
- 业务类型为 `kcbczc`（科创板成长层权限开通）
- 账户的 `holderName` 字段值为 "科创板未开通"

## 功能效果
1. 在符合条件的账户下方显示橙色背景的提示框
2. 提示文字："该账户不存在科创板权限，开通后可办理。"
3. 可点击的链接："前往开通 >>"
4. 点击链接后跳转到科创板权限开通业务页面（type=kcbkt）

## 测试建议
1. 在科创板成长层权限开通业务中，确保有账户的 `holderName` 为 "科创板未开通"
2. 验证提示信息正确显示
3. 验证点击"前往开通"链接能正确跳转到科创板权限开通业务
4. 验证样式显示正常，符合UI设计要求
