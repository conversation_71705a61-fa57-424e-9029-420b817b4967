<!-- 转取转存明细 -->
<template>
  <div class="page_main">
    <headComponent>
      <div class="header_inner spel">
        <a class="icon_back" @click.stop="pageBack"></a>
        <h1 class="title text-center">明细</h1>
      </div>
    </headComponent>
    <div class="date_ctbox">
      <div class="item">
        <h-datetime
          class="ui input"
          title="起始日期"
          type="date"
          v-model.trim="startDate"
          start-year="2000"
          :end-date="chooseEndDate"
        ></h-datetime>
      </div>
      <span class="line"></span>
      <div class="item">
        <h-datetime
          class="ui input"
          title="结束日期"
          type="date"
          v-model.trim="endDate"
          start-year="2000"
          :end-date="chooseEndDate"
        ></h-datetime>
      </div>
    </div>
    <article class="content">
      <scroll
        :height="scrollheight"
        :pullupActive="!noMoreData"
        @pullingDown="pullDownFun"
        @pullingUp="pullupFun"
        ref="scrollList"
        :tiptxt="tiptext"
      >
        <div class="bk_record">
          <ul>
            <li v-for="(it, index) in list" :key="index">
              <template v-if="it.sourceFlag=='0'">
                <em class="type out">转取</em>
                <h5>-{{it.occurBalance|formatMoney}}</h5>
                <p>银行转取 - 成功</p>
                <span class="time">{{it.initDate|formatDate('yyyy.MM.dd')}}</span>
              </template>
              <template v-else>
                <em class="type in">转存</em>
                <h5>-{{it.occurBalance|formatMoney}}</h5>
                <p>银行转存 - 成功</p>
                <span class="time">{{it.initDate|formatDate('yyyy.MM.dd')}}</span>
              </template>
            </li>
          </ul>
        </div>
      </scroll>
    </article>
  </div>
</template>

<script>
import headComponent from '@/components/headComponent'
import scroll from '@/components/scroll'
import { queryTransferList } from '@/service/comServiceNew'
export default {
  components: {
    headComponent,
    scroll
  },
  data () {
    return {
      chooseEndDate: new Date().format('yyyy-MM-dd'), // 可选起始日期的限制到今天
      ygtUserInfo: $h.getSession('ygtUserInfo', {decrypt: false}),
      list: [], // 流水列表
      scrollheight: '100%',
      pulldown: true,
      pullup: true,
      curPage: 1,
      numPerPage: 10,
      totalPages: 0,
      noMoreData: false,
      tiptext: '没有更多内容啦',
      startDate: '',
      endDate: ''
    }
  },
  created () {
    this.scrollheight = window.innerHeight - 84
    this.startDate = new Date().format('yyyy-MM-dd')
    this.endDate = new Date().format('yyyy-MM-dd')
  },
  mounted () {
    window.phoneBackBtnCallBack = this.pageBack
    this.queryTransferList()
  },
  watch: {
    startDate: function (newVal, oldVal) {
      this.curPage = 1
      this.queryTransferList()
    },
    endDate: function (newVal, oldVal) {
      this.curPage = 1
      this.queryTransferList()
    }
  },
  methods: {
    pullDownFun () {
      console.log('down')
      this.curPage = 1
      this.noMoreData = true
      this.queryTransferList()
    },
    pullupFun () {
      console.log('up')
      this.curPage++
      this.queryTransferList()
    },

    // 查转账流水
    queryTransferList () {
      let _this = this
      queryTransferList({
        clientId: this.ygtUserInfo.clientId,
        fundAccount: $h.getSession('loginType') == '2' ? this.ygtUserInfo.fundAccountXy : this.ygtUserInfo.fundAccount,
        currentPage: this.curPage,
        startDate: this.startDate,
        endDate: this.endDate
      }).then(
        res => {
          if (res.error_no === '0') {
            let _results = res.results
              ? res.results[0]
              : res.funcPtransextBanktransferQuery[0]
            _this.totalPages = _results.totalPages
            _this.curPage = _results.currentPage
            if (_results.currentPage == 1 && _results.data) {
              this.list = JSON.parse(_results.data)
            } else if (_results.currentPage > 1 && _results.data != '') {
              this.list = this.list.concat(JSON.parse(_results.data))
            }
            if (_this.curPage >= _this.totalPages) {
              _this.noMoreData = true
            }
            _this.$refs.scrollList.refresh()
          } else {
            _hvueToast({
              icon: 'error',
              mes: res.error_info
            })
          }
        },
        err => {
          console.log(err)
        }
      )
    },
    // 返回
    pageBack () {
      this.$router.back()
    }
  }
}
</script>
