<template>
  <div class="page_main">
    <template v-if="showPage == 0">
      <headComponent header-title="证券销户">
        <div class="header_inner">
          <a class="icon_back" @click.stop="pageBack"></a>
          <h1 class="title text-center">证券销户</h1>
          <a class="icon_text" @click.stop="showPage = 1">销户结果</a>
        </div>
      </headComponent>
      <article class="content">
        <div class="xh_home_zbbox">
          <div class="title">
            <h5>销户申请</h5>
            <p>请保持预留手机通畅，并做好以下准备</p>
          </div>
          <ul class="xh_zblist">
            <li>
              <i class="icon"><img src="@/assets/images/xh_zbic01.png"/></i
              ><span>{{ idCardTypeName }}</span>
            </li>
            <li>
              <i class="icon"><img src="@/assets/images/xh_zbic02.png"/></i
              ><span>WIFI或4G</span>
            </li>
            <li>
              <i class="icon"><img src="@/assets/images/xh_zbic03.png"/></i
              ><span>必须本人</span>
            </li>
          </ul>
        </div>
        <div class="xh_home_tipbox">
          <div class="title">
            <h5>注意事项</h5>
          </div>
          <ul class="xh_tiplist">
            <li><p>非网上开户的账户需临柜办理该业务</p></li>
            <li><p>销户申请提交后请密切关注审核结果</p></li>
            <li><p>审核期间请勿操作账户</p></li>
            <li><p>请确保所销账户无在途业务</p></li>
          </ul>
        </div>
      </article>
      <div class="bottom_btn">
        <div class="ce_btn">
          <a
            v-throttle
            class="ui button block rounded"
            @click.stop="toCloseAccount"
            >开始销户</a
          >
        </div>
      </div>
    </template>
    <template v-else-if="showPage == 1">
      <headComponent header-title="手机验证"></headComponent>
      <article class="content">
        <h5 class="com_title">请进行手机短信验证查看销户结果</h5>
        <div class="input_form mobile_form">
          <div class="ui field text">
            <label class="ui label">手机号</label>
            <input
              :maxlength="inputModel.mobile.maxlength"
              v-model.trim="inputModel.mobile.value"
              type="text"
              class="ui input"
              placeholder="请输入手机号"
            />
          </div>
          <div class="ui field text">
            <label class="ui label">图形验证码</label>
            <input
              :maxlength="inputModel.imgCode.maxlength"
              v-model.trim="inputModel.imgCode.value"
              type="text"
              class="ui input"
              placeholder="请输入图形验证码"
            />
            <a class="code_img" @click.stop="getImgCode">
              <img :src="codeImgUrl" />
            </a>
          </div>
          <div class="ui field text">
            <label class="ui label">短信验证码</label>
            <input
              :maxlength="inputModel.msgCode.maxlength"
              v-model.trim="inputModel.msgCode.value"
              type="text"
              class="ui input"
              placeholder="请输入6位短信验证码"
            />
            <smsTimer v-model="startFlag" @sendSms="verifyImgCode"></smsTimer>
          </div>
        </div>
        <div class="ce_btn mt20">
          <a
            v-throttle
            class="ui button block rounded"
            @click.stop="verifyInput"
            >下一步</a
          >
        </div>
      </article>
    </template>
    <template v-else-if="showPage == 2">
      <queryXhyyResult :page-param="yyxhPageParam"></queryXhyyResult>
    </template>
    <template
      v-else-if="
        showPage == 3 &&
          businessFlowInfoParam.length &&
          businessResultParam.length
      "
    >
      <businessFlowInfo :page-param="businessFlowInfoParam"></businessFlowInfo>
      <businessResult :page-param="businessResultParam"></businessResult>
    </template>
  </div>
</template>
<script>
import headComponent from '@/components/headComponent';
import queryXhyyResult from '@/views/busComp/queryXhyyResult';
import smsTimer from '@/components/smsTimer';

import ErrorCode from '@/flow/ErrorCodeConstants';
import { closeYgt } from '@/common/sso';
import { checkInput, queryDictionary } from '@/common/util';
import {
  sendMobileMsg,
  getImgCode,
  verifyImgCode,
  getCloseAccountResult,
  getXhyyResult,
  queryUserType,
  userLogin
} from '@/service/comServiceNew';

import businessFlowInfo from '@/views/busComp/businessFlowInfo'; // 销户结果账号列表
import businessResult from '@/views/busComp/businessResult';
import { ecbEncrypt } from 'thinkive-hvue/plugin/sm/sm4';
import { checkHMTCardType } from '../../common/util';

export default {
  components: {
    headComponent,
    smsTimer,
    queryXhyyResult,
    businessFlowInfo,
    businessResult
  },
  data() {
    return {
      ygtUserInfo: $h.getSession('ygtUserInfo', { decrypt: false }),
      businessCode: 'xh',
      businessName: '销户预约手机号码验证',
      smsNo: '', // 修改手机号短信类型数据字典
      showPage: 0,
      startFlag: false,
      sendStatu: 1, // 1: 显示发送按钮 2: 显示倒计时 3:显示重新发送
      inputModel: {
        mobile: {
          name: '手机号',
          value: '',
          maxlength: '11',
          minlength: '11',
          format: 'phone'
        },
        imgCode: {
          name: '图形验证码',
          value: '',
          maxlength: '4',
          minlength: '4',
          format: 'enNum'
        },
        msgCode: {
          name: '短信验证码',
          value: '',
          maxlength: '6',
          minlength: '6',
          format: 'num'
        }
      },
      codeImgUrl: '', // 图片验证码图片url
      codeImgKey: '',
      yyxhPageParam: {},
      businessFlowInfoParam: {},
      businessResultParam: {}
    };
  },
  computed: {
    idCardTypeName() {
      return checkHMTCardType() ? this.ygtUserInfo.identityType === 'I' ? '外国人永久居留证' : '港澳台居民来往内地通行证' : '二代身份证';
    }
  },
  mounted() {
    // 查询短信类型数据字典
    queryDictionary(
      { type: 'ismp.sms_type', value: this.businessName },
      (data) => {
        this.smsNo = data.key;
      }
    );
    this.inputModel.imgCode.value = '';
    this.inputModel.msgCode.value = '';
    this.getImgCode(); // 获取验证码
    window.phoneBackBtnCallBack = this.pageBack;
  },
  methods: {
    // 获取图片验证码
    getImgCode() {
      getImgCode({}, {}).then(
        (res) => {
          if (res.error_no === '0') {
            let results = res.results
              ? res.results[0]
              : res.generateVerifyCode[0];
            this.codeImgUrl = results.imageCode;
            this.codeImgKey = results.mobileKey;
            this.inputModel.imgCode.value = '';
          } else {
            _hvueToast({
              icon: 'error',
              mes: res.error_info
            });
          }
        },
        (err) => {
          _hvueToast({ mes: err });
        }
      );
    },
    // 校验图片验证码
    verifyImgCode() {
      verifyImgCode(
        {
          mobileKey: this.codeImgKey,
          imageCode: this.inputModel.imgCode.value
        },
        {}
      ).then(
        (res) => {
          if (res.error_no === '0') {
            this.sendMsg();
            // this.getCloseAccountResult()
          } else {
            _hvueToast({
              icon: 'error',
              mes: res.error_info
            });
            this.getImgCode();
          }
        },
        (err) => {
          console.log(err);
        }
      );
    },
    // 发送验证码
    sendMsg() {
      let _mobile = this.inputModel.mobile;
      let flag = checkInput(_mobile);
      if (flag != '') {
        return;
      }
      // let _imgCode = this.inputModel.imgCode
      // flag = checkInput(_imgCode)
      // if (flag != '') {
      //   return
      // }
      let ygtUserInfo = $h.getSession('ygtUserInfo', { decrypt: false }) || {};
      let userId = ygtUserInfo.userId || '';
      sendMobileMsg({
        flow_name: this.businessCode,
        mobile: _mobile.value,
        smsNo: this.smsNo,
        businessCode: this.businessCode,
        userId: userId
      })
        .then((data) => {
          if (data.error_no === '0') {
            this.startFlag = true;
            this.sendStatu = 2;
          } else {
            _hvueToast({ mes: data.error_info });
          }
        })
        .catch((e) => {
          _hvueToast({ mes: e.message });
        });
    },
    verifyInput() {
      let inputs = Object.values(this.inputModel);
      let flag = '';
      for (let s = 0; s < inputs.length; s++) {
        let el = inputs[s];
        flag = checkInput(el);
        if (flag != '') {
          break;
        }
      }
      if (flag == '') {
        this.getCloseAccountResult();
        // this.verifyImgCode()
      }
    },
    // 检测验证码
    getCloseAccountResult() {
      let ygtUserInfo = $h.getSession('ygtUserInfo', { decrypt: false }) || {};
      let userId = ygtUserInfo.userId || '';
      let submitParam = {
        mobile: this.inputModel.mobile.value,
        verifyCode: this.inputModel.msgCode.value,
        smsNo: this.smsNo,
        businessCode: this.businessCode,
        userId: userId
      };
      let _this = this;
      getCloseAccountResult(submitParam)
        .then((data) => {
          if (data.error_no === '0') {
            // 展示结果
            let businessFlowInfo = data.businessFlowInfo
              ? data.businessFlowInfo
              : [];
            let businessResult = data.businessResult ? data.businessResult : [];

            if (!!businessFlowInfo.length && !!businessResult.length) {
              let isLogin = $h.getSession('isLogin');
              if (isLogin) {
                // 开始预约流程
                this.$router.push({
                  name: 'business',
                  query: {
                    type: 'xh'
                  }
                });
                return;
              }
              $h.setSession('mobileQueryXhResult', '1'); //通过手机号查询销户结果
              _this.showPage = 3;
              _this.$parent.flow = {
                formStatus: businessFlowInfo[0].formStatus,
                flowName: 'xh'
              };
              _this.businessFlowInfoParam = businessFlowInfo;
              _this.businessResultParam = businessResult;
            } else {
              _hvueAlert({
                mes: '未查询到办理结果'
              });
            }
          } else if (data.error_no === '-7400322') {
            // 抱歉，查询不到您的销户结果，您可以返回提示页点击开始销户按钮进行销户
            this.dealErrorCodeEvent(data);
          } else {
            _hvueToast({ mes: data.error_info });
          }
        })
        .catch((e) => {
          _hvueToast({ mes: e.message });
        });
    },
    /**
     * 处理错误码信息
     */
    dealErrorCodeEvent(data) {
      let errorNo = data.error_no;
      let matchFlag = false;
      for (let index = 0; index < ErrorCode.length; index++) {
        const element = ErrorCode[index];
        if (errorNo === element.code) {
          matchFlag = true; // 标记匹配到错误码
          // 跳转到公共的错误信息页面
          element.title = this.$route.meta.title;
          this.$router.push({
            name: 'error',
            params: element
          });
        }
      }
      // 未匹配到错误码文件
      if (!matchFlag) {
        // 提示错误信息
        _hvueAlert({
          title: '错误提示',
          mes: data.error_info
        });
      }
    },

    toCloseAccount() {
      $h.clearSession('mobileQueryXhResult');
      let ygtUserInfo = $h.getSession('ygtUserInfo', { decrypt: false }) || {};
      if (ygtUserInfo && ygtUserInfo.userId) {
        this.queryUserType(ygtUserInfo.userId);
      } else if ($h.getSession('fromTHS')) {
        let param = {
          funcNo: '50041',
          key: 'fxcAccountInfo'
        };
        let resultVo = $h.callMessageNative(param);
        let isLoginResult = {};
        if (resultVo) {
          isLoginResult = resultVo.results[0].value;
          // 兼容ios返回结果为字符串
          if (typeof isLoginResult === 'string' && isLoginResult !== '') {
            isLoginResult = JSON.parse(isLoginResult);
          }
        }
        if (
          isLoginResult &&
          isLoginResult['stock_account'] &&
          isLoginResult['type'] == '0'
        ) {
          this.login(isLoginResult);
        } else {
          closeYgt(0, '1A', 1, 0, this.$router);
        }
      } else {
        this.$router.push({
          name: 'login'
        });
      }
    },

    login(common_login_state) {
      // 获取统一登录token传给后段
      let param2 = {
        funcNo: '50041',
        key: 'temp_token_ismp'
      };
      let resultVo2 = $h.callMessageNative(param2);
      let temp_token_ismp = resultVo2.results[0].value;
      const sm4Key = $hvue.config.sm4Key;
      const newPassword = ecbEncrypt(
        sm4Key.substring(8, sm4Key.length - 8),
        common_login_state['password']
      );
      let reqParams = {
        account: common_login_state['stock_account'],
        password: newPassword,
        tempTokenIsmp: temp_token_ismp
      };
      if (common_login_state['isRsa']) {
        reqParams.isRsa = common_login_state['isRsa'];
        reqParams.password = common_login_state['password'];
      }
      // 调用网厅登录
      userLogin(reqParams).then(
        (res) => {
          if (res.error_no === '0') {
            // 保存用户信息
            let userInfo = res.userInfo[0];
            userInfo.riskLevelDesc =
              res.custTypeCheck !== undefined
                ? res.custTypeCheck[0].riskLevelDesc
                : '';
            let ygtUserInfo =
              $h.getSession('ygtUserInfo', { decrypt: false }) || {};
            if (ygtUserInfo.fundAccountXy) {
              userInfo.fundAccountXy = ygtUserInfo.fundAccountXy;
            } else {
              userInfo.fundAccountXy = '';
            }
            $h.setSession('isLogin', true);
            $h.setSession('ygtUserInfo', userInfo, { encrypt: false });
            this.queryUserType(userInfo.userId);
          } else {
            _hvueToast({
              icon: 'error',
              mes: res.error_info
            });
          }
        },
        (err) => {
          console.log(err);
        }
      );
    },
    // 检查用户类型    #result[custTypeCheck][0][accountType] == '1'   code="-8800003" message="机构用户不能办理此业务，请临柜办理"
    queryUserType(userId) {
      queryUserType({ userId: userId }, {}).then(
        (res) => {
          if (res.error_no === '0') {
            if (res.custTypeCheck[0].accountType == '1') {
              this.dealErrorCodeEvent({ error_no: '-8800003' });
              return;
            }
            this.querryYyxhResult(userId);
          } else {
            _hvueToast({
              icon: 'error',
              mes: res.error_info
            });
          }
        },
        (err) => {
          _hvueToast({ mes: err });
        }
      );
    },
    // 查询预约销户的结果
    querryYyxhResult(userId) {
      getXhyyResult({ userId: userId })
        .then((data) => {
          if (data.error_no === '0') {
            let _results = data.results ? data.results : data.queryXhyyResult;
            if (
              _results[0].state === '1' ||
              _results[0].state === '2' ||
              _results[0].state === '3'
            ) {
              // 展示结果
              this.showPage = 2;
              this.yyxhPageParam = _results;
            } else {
              // 开始预约流程
              this.$router.push({
                name: 'business',
                query: {
                  type: 'yyxh'
                }
              });
            }
          } else if (data.error_no === '-7400322') {
            // 抱歉，查询不到您的销户结果，您可以返回提示页点击开始销户按钮进行销户
            // 开始预约流程
            this.$router.push({
              name: 'business',
              query: {
                type: 'yyxh'
              }
            });
          } else {
            _hvueToast({ mes: data.error_info });
          }
        })
        .catch((e) => {
          _hvueToast({ mes: e.message });
        });
    },
    pageBack() {
      if (this.showPage == 0) {
        this.$router.push({
          name: 'index'
        });
      } else {
        this.showPage = 0;
      }
    }
  }
};
</script>

<style scoped>
.bottom_btn {
  margin-bottom: 0.1rem;
}
</style>
