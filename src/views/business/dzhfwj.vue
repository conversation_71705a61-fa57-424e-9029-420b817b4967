<template>
  <div>
    <headComponent v-if="!showProtocolParam.isShow" headerTitle="自助问卷回访"></headComponent>
    <article class="content" v-if="!showProtocolParam.isShow">
      <div class="rule_ctbox">
        <scroll
          :height="scrollheight"
          :pullupActive="!noMoreData"
          @pullingDown="pullDownFun"
          @pullingUp="pullupFun"
          ref="scrollProduct"
          :tiptxt="tiptext"
        >
          <ul v-if="questionnaireList.length">
            <li v-for="(item,index) in questionnaireList" :key="index">
              <a @click.prevent="showProtocolClick(item)">《{{item.name}}》</a>
            </li>
          </ul>
          <p class="cond_tips" v-else>
            空空如也，您已完成全部问卷，感谢您的参与！
          </p>
        </scroll>
      </div>
    </article>
    <question-list
        v-if="showProtocolParam.isShow"
        v-model="showProtocolParam.isShow"
        :subjectName="showProtocolParam.subjectName"
        :pageParam="showProtocolParam.questionList"
        :subjectNo="showProtocolParam.subjectNo"
        btnDesc="返回"
        @submitAns="submitAns"
        @back="back"
    ></question-list>
  </div>
</template>
<script>
import headComponent from '@/components/headComponent'
import showProtocol from '@/components/showProtocol' // 协议详情
import questionList from '@/components/questionList' // 问卷内容
import scroll from '@/components/scroll'
import {
  queryWjhfList,
  queryWjhfContent,
  submitWjhf
} from '@/service/comServiceNew'
export default {
  components: {
    headComponent,
    scroll,
    showProtocol,
    questionList
  },
  data () {
    return {
      questionnaireList: $h.getSession('wjhf_questionnaireList') ? JSON.parse($h.getSession('wjhf_questionnaireList')) : [], // 产品列表
      dzhtList: [], // 电子合同列表
      showSelect: false, // 是否展示时间排序选择列表
      showProtocolParam: {
        isShow: false, // 是否展示
        subjectNo: '', // 问卷编号
        subjectName: '', // 问卷名称
        questionList: '' // 协议内容
      }, // 协议详情组件参数
      // 下拉加载组件参数
      scrollheight: '100%',
      pulldown: true,
      pullup: true,
      curPage: 1,
      numPerPage: 10,
      totalPages: 0,
      noMoreData: false,
      tiptext: '没有更多内容'
    }
  },
  created () {
    this.scrollheight = window.innerHeight - 44;
  },
  mounted () {
    window.phoneBackBtnCallBack = this.pageBack
  },
  methods: {
    pullDownFun () {
      console.log('down')
      this.curPage = 1
      this.noMoreData = true
      this.queryWjhfList()
    },
    pullupFun () {
      console.log('up')
      this.curPage++
      this.queryWjhfList()
    },
    queryWjhfList () {
      queryWjhfList({
        fundAccount: $h.getSession('ygtUserInfo', {decrypt: false}).fundAccount,
        clientId: $h.getSession('ygtUserInfo', {decrypt: false}).clientId,
        userId: $h.getSession('ygtUserInfo', {decrypt: false}).userId,
        name: $h.getSession('ygtUserInfo', {decrypt: false}).name,
        branchNo: $h.getSession('ygtUserInfo', {decrypt: false}).branchNo,
      }).then(
        res => {
          if (res.error_no === '0') {
            let _results = res.questionnaireList;
            this.questionnaireList = _results;
            _hvueToast({
              icon: 'ok',
              mes: '问卷提交成功'
            })
          } else {
            this.noMoreData = true
            _hvueToast({
              icon: 'error',
              mes: res.error_info
            })
          }
        },
        err => {
          console.log(err)
        }
      )
    },
    queryWjhfContent (item) {
      queryWjhfContent({
        clientId: $h.getSession('ygtUserInfo', {decrypt: false}).clientId,
        subjectNo: item.subjectNo,
        userId: $h.getSession('ygtUserInfo', {decrypt: false}).userId,
      }).then(
        res => {
          if (res.error_no === '0') {
            let questionList = res.questionList || [],
                dataRow = res.dataRow ? res.dataRow[0] : {};
            this.showProtocolParam.isShow = true
            this.showProtocolParam.subjectName = dataRow.subjectName
            this.showProtocolParam.questionList = questionList
            this.showProtocolParam.subjectNo = dataRow.subjectNo
          } else {
            this.noMoreData = true
            _hvueToast({
              icon: 'error',
              mes: res.error_info
            })
          }
        },
        err => {
          console.log(err)
        }
      )
    },
    showProtocolClick (item) {
      // 展示协议详情
      this.queryWjhfContent(item)
    },
    back () {
      this.showProtocolParam.isShow = false
    },
    submitAns (param) {
      // 提交问卷
      submitWjhf({
        clientId: $h.getSession('ygtUserInfo', {decrypt: false}).clientId,
        paperType: param.paperType, //问卷类型
        paperAnswer: param.paperAnswer, //问卷答案串
        userId: $h.getSession('ygtUserInfo', {decrypt: false}).userId,
      }).then(
          res => {
            if (res.error_no === '0') {
              this.back()
              this.queryWjhfList()
            } else {
              this.noMoreData = true
              _hvueToast({
                icon: 'error',
                mes: res.error_info
              })
            }
          },
          err => {
            console.log(err)
          }
      )
      console.log(param)
      // this.showProtocolParam.isShow = false
    },
    pageBack () {
      // if (this.showPage == 0) {
        this.$router.push({
          name: 'index'
        })
      // } else {
      //   this.showPage = 0
      // }
    }
  }
}
</script>
