<template>
  <div class="page_main">
    <template v-if="showPage === 0">
      <headComponent :headerTitle="'介绍页'"></headComponent>
      <article class="content">
        <div class="h5_home_page">
          <div class="h5_banner_box">
            <div class="pic"><img src="@/assets/images/bus_ban_bg.png" /></div>
            <div class="tit">
              <h2>一站式销户</h2>
            </div>
          </div>
          <div class="bus_home_must">
            <h5 class="title">
              <span><em>注意事项</em></span>
            </h5>
            <div class="list">
              <p>账户销户业务适用于身份证开户客户</p>
              <p>销户类型分为注销股东账户、注销全部账户</p>
              <p>销户申请提交后将在两个工作日内反馈结果</p>
              <p>销户申请提交后审核期间请勿操作账户</p>
              <p>
                请确保申请注销的交易账户无持仓、无在途、无未了结业务（如新股申购、未到账红股等），且当天无委托交易
              </p>
              <p>请保持预留手机畅通，及时接听我司电话</p>
            </div>
          </div>
          <div class="bus_home_tips">
            <h5>温馨提示</h5>
            <p>
              为证券账户销户提供便利。即在投资者已了结相关业务、清空证券资产、注销相关权限后，券商应当为投资者办理证券账户注销业务提供便利。对于非现场开户的投资者，我们将提供网上销户的非现场销户服务。
            </p>
          </div>
        </div>
      </article>
      <footer class="footer">
        <a class="ui button block rounded" @click.stop="toCloseAccount">立即办理</a>
      </footer>
    </template>
    <template v-else-if="showPage == 1">
      <queryXhyyResult :page-param="yyxhPageParam"></queryXhyyResult>
    </template>
  </div>
</template>

<script>
import headComponent from '@/components/headComponent'
import { getXhyyResult, queryUserType, queryUserFlowStatus } from '@/service/comServiceNew'
import queryXhyyResult from '@/views/busComp/queryXhyyResult'

export default {
  components: {
    headComponent,
    queryXhyyResult
  },
  data() {
    return {
      ygtUserInfo: $h.getSession('ygtUserInfo', { decrypt: false }),
      businessCode: 'xh',
      showPage: 0,
      yyxhPageParam: {}
    }
  },
  methods: {
    pageBack() {
      if (this.showPage == 0) {
        this.$router.push({
          name: 'index'
        })
      } else {
        this.showPage = 0
      }
    },
    toCloseAccount() {
      let ygtUserInfo = $h.getSession('ygtUserInfo', { decrypt: false }) || {}
      if (ygtUserInfo && ygtUserInfo.userId) {
        this.queryUserType(ygtUserInfo.userId)
      } else {
        this.$router.push({
          name: 'login'
        })
      }
    },
    // 检查用户类型    #result[custTypeCheck][0][accountType] == '1'   code="-8800003" message="机构用户不能办理此业务，请临柜办理"
    queryUserType(userId) {
      queryUserType({ userId: userId }, {}).then(
        res => {
          if (res.error_no === '0') {
            if (res.custTypeCheck[0].accountType == '1') {
              this.dealErrorCodeEvent({ error_no: '-8800003' })
              return
            }
            this.queryYyxhResult(userId)
          } else {
            _hvueToast({
              icon: 'error',
              mes: res.error_info
            })
          }
        },
        err => {
          _hvueToast({ mes: err })
        }
      )
    },
    async queryResultDetail(res) {
      let ygtUserInfo = $h.getSession('ygtUserInfo', { decrypt: false }) || {}
      let formStatusResult = await queryUserFlowStatus({
        userId: ygtUserInfo.userId,
        businessCode: 'xh',
      })
      if (formStatusResult.error_no === '0') {
        let formStatus = formStatusResult.results[0].formStatus
        if (formStatus !== '0') {
          this.$router.push({
            name: 'business',
            query: {
              type: 'xh'
            }
          })
        } else {
          // 展示结果
          this.showPage = 1
          this.yyxhPageParam = res
        }
      }
    },
    // 查询预约销户的结果
    queryYyxhResult(userId) {
      getXhyyResult({ userId: userId })
        .then(data => {
          if (data.error_no === '0') {
            let _results = data.results ? data.results : data.queryXhyyResult
            if (
              _results[0].state === '1' ||
              _results[0].state === '2' ||
              _results[0].state === '3'
            ) {
              this.queryResultDetail(_results)
            } else {
              // 开始预约流程
              this.$router.push({
                name: 'business',
                query: {
                  type: 'yyxh'
                }
              })
            }
          } else if (data.error_no === '-7400322') {
            // 抱歉，查询不到您的销户结果，您可以返回提示页点击开始销户按钮进行销户
            // 开始预约流程
            this.$router.push({
              name: 'business',
              query: {
                type: 'yyxh'
              }
            })
          } else {
            _hvueToast({ mes: data.error_info })
          }
        })
        .catch(e => {
          _hvueToast({ mes: e.message })
        })
    },
    /**
     * 处理错误码信息
     */
    dealErrorCodeEvent(data) {
      let errorNo = data.error_no
      let matchFlag = false
      for (let index = 0; index < ErrorCode.length; index++) {
        const element = ErrorCode[index]
        if (errorNo === element.code) {
          matchFlag = true // 标记匹配到错误码
          // 跳转到公共的错误信息页面
          element.title = this.$route.meta.title
          this.$router.push({
            name: 'error',
            params: element
          })
        }
      }
      // 未匹配到错误码文件
      if (!matchFlag) {
        // 提示错误信息
        _hvueAlert({
          title: '错误提示',
          mes: data.error_info
        })
      }
    }
  }
}
</script>
