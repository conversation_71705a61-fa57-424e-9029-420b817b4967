<template>
  <div class="page_main">
    <template>
      <headComponent :headerTitle="'科创板成长层介绍'"></headComponent>
      <article class="content">
        <div class="h5_home_page">
          <div class="h5_banner_box">
            <div class="pic"><img src="@/assets/images/bus_ban_bg.png" /></div>
            <div class="tit">
              <h2>开通科创板成长层</h2>
            </div>
          </div>
          <div class="bus_home_must">
            <h5 class="title">
              <span><em>申请条件</em></span>
            </h5>
            <div class="list">
              <p>无不良诚信记录</p>
              <p>风险测评等级需满足C4及以上</p>
              <p>存在状态正常的沪A账户、沪市信用账户</p>
              <p>已开通科创板权限</p>
            </div>
          </div>
          <div class="bus_home_must">
            <h5 class="title">
              <span><em>温馨提示</em></span>
            </h5>
            <div class="list">
              <p>
                科创板基础层&成长层具有比较高的风险属性，参与交易前请您提前了解交易市场的风险及业务知识，认真评估是否可承受市场所带来的较高风险。
              </p>
            </div>
          </div>
        </div>
      </article>
      <footer class="footer">
        <a class="ui button block rounded" @click.stop="startBusinessFlow">立即办理</a>
      </footer>
    </template>
  </div>
</template>

<script>
import headComponent from '@/components/headComponent'

export default {
  name: 'KcbczcIntro',
  components: {
    headComponent
  },
  data() {
    return {
      ygtUserInfo: $h.getSession('ygtUserInfo', { decrypt: false }),
      businessCode: 'kcbczc'
    }
  },
  mounted() {
    // 设置页面返回回调
    window.phoneBackBtnCallBack = this.pageBack
  },
  methods: {
    /**
     * 页面返回处理
     */
    pageBack() {
      this.$router.push({
        name: 'index'
      })
    },

    /**
     * 开始业务流程
     */
    startBusinessFlow() {
      this.$router.push({
        name: 'business',
        query: {
          type: this.businessCode
        }
      })
    }
  }
}
</script>

<style scoped>
.result-page {
  padding: 20px;
}

.result-page .content {
  text-align: center;
  padding: 40px 20px;
}
</style>
