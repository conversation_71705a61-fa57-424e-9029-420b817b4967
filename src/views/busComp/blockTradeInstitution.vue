<template>
  <div class="blockTrade">
    <headComponent headerTitle="大宗交易" v-if="showHead"></headComponent>
    <div v-show="showUserInfo">
      <div class="ste_cont">
        <ul>
          <li class="spel">
            <span class="tit">机构全称</span>
            <div class="cont">
              <span>{{ ygtUserInfo.name }}</span>
            </div>
          </li>
          <li class="spel">
            <span class="tit">营业执照号码</span>
            <div class="cont">
              <span>{{
                  ygtUserInfo.identityNum
                }}</span>
            </div>
          </li>
          <li class="spel">
            <span class="tit">授权办理人姓名</span>
            <div class="cont">
              <span>{{ ygtUserInfo.relationName }}</span>
            </div>
          </li>
          <li class="spel">
            <span class="tit">授权办理人证件号码</span>
            <div class="cont">
              <span>{{ ygtUserInfo.relationIdNo | formatIdno }}</span>
            </div>
          </li>
        </ul>
      </div>
      <div class="ste_cont">
        <ul>
          <li>
            <span class="tit">证券账号</span>
            <div
                class="cont"
                @click.stop="boxClick('trdaccount', '证券账号')"
            >
                <span :class="{ default: !selData.trdaccount.value }">
                  {{ selData.trdaccount.value || selData.trdaccount.placeholder}}
                </span>
            </div>
            <span class="lk"></span>
          </li>
        </ul>
      </div>
      <div class="ste_cont">
        <ul>
          <li>
            <span class="tit">大宗可卖额度</span>
            <div class="cont" @click.stop="boxClick('quota')">
              <span :class="{ default: selData.quota.value === '' }">
                {{ selData.quota.value || selData.quota.placeholder}}
              </span>
            </div>
            <span class="lk"></span>
          </li>
          <li>
            <span class="tit">营业部代码</span>
            <div class="cont" @click.stop="boxClick('branchCode')">
              <span :class="{ default: selData.branchCode.value === '' }">
                {{ selData.branchCode.value || selData.branchCode.placeholder}}
              </span>
            </div>
            <span class="lk"></span>
          </li>
          <li>
            <span class="tit">约定号</span>
            <div class="cont" @click.stop="boxClick('contract')">
              <span :class="{ default: selData.contract.value === '' }">
                {{ selData.contract.value || selData.contract.placeholder}}
              </span>
            </div>
            <span class="lk"></span>
          </li>
        </ul>
      </div>
      <div class="ste_cont">
        <ul>
          <li>
            <span class="tit">交易单元</span>
            <div class="cont" @click.stop="boxClick('trading')">
              <span :class="{ default: selData.trading.value === '' }">
                {{ selData.trading.value || selData.trading.placeholder}}
              </span>
            </div>
            <span class="lk"></span>
          </li>
          <li>
            <span class="tit">对方交易单元</span>
            <div class="cont" @click.stop="boxClick('otherTrading')">
              <span :class="{ default: selData.otherTrading.value === '' }">
                {{ selData.otherTrading.value || selData.otherTrading.placeholder}}
              </span>
            </div>
            <span class="lk"></span>
          </li>
        </ul>
      </div>
      <div class="ste_cont">
        <ul>
          <li>
            <span class="tit">证券名称</span>
            <div class="cont" @click.stop="boxClick('securityName')">
              <span :class="{ default: selData.securityName.value === '' }">
                {{ selData.securityName.value || selData.securityName.placeholder}}
              </span>
            </div>
            <span class="lk"></span>
          </li>
          <li>
            <span class="tit">买卖方向</span>
            <div class="cont" @click.stop="boxClick('direction')">
              <span :class="{ default: selData.direction.value === '' }">
                {{ selData.direction.value || selData.direction.placeholder}}
              </span>
            </div>
            <span class="lk"></span>
          </li>
          <li>
            <span class="tit">证券代码</span>
            <div class="cont" @click.stop="boxClick('securityCode')">
              <span :class="{ default: selData.securityCode.value === '' }">
                {{ selData.securityCode.value || selData.securityCode.placeholder}}
              </span>
            </div>
            <span class="lk"></span>
          </li>
          <li>
            <span class="tit">价格</span>
            <div class="cont" @click.stop="boxClick('price')">
              <span :class="{ default: selData.price.value === '' }">
                {{ (selData.price.value && selData.price.value + '（元）')  || selData.price.placeholder}}
              </span>
            </div>
            <span class="lk"></span>
          </li>
          <li>
            <span class="tit">数量</span>
            <div class="cont" @click.stop="boxClick('amount')">
              <span :class="{ default: selData.amount.value === '' }">
                {{ (selData.amount.value && selData.amount.value + ' (张/股)') || selData.amount.placeholder}}
              </span>
            </div>
            <span class="lk"></span>
          </li>
        </ul>
      </div>
      <div class="ste_cont">
        <div class="padding-left-15">
          <span class="tit">申报类型</span>
        </div>
         <sel-radio-box
             :isShow="true"
             :type="'declaretype'"
             :category="'ismp.declaretype'"
             :defaultStr="selData.declaretype.id"
             :selectAble="selData.declaretype.selectAble"
             @selRadioCallback="selRadioCallback"
         ></sel-radio-box>
      </div>
      <div class="ste_cont">
        <div class="padding-left-15">
          <span class="tit">股东性质</span>
        </div>
        <sel-radio-box
            :isShow="true"
            :type="'shareholder'"
            :category="'ismp.shareholder'"
            :defaultStr="selData.shareholder.id"
            :selectAble="selData.shareholder.selectAble"
            @selRadioCallback="selRadioCallback"
        ></sel-radio-box>
      </div>
      <div class="ste_cont">
        <div class="padding-left-15">
          <span class="tit">股份性质</span>
        </div>
        <sel-radio-box
            :isShow="true"
            :type="'stocknature'"
            :category="'ismp.stocknature'"
            :defaultStr="selData.stocknature.id"
            :selectAble="selData.stocknature.selectAble"
            @selRadioCallback= "selRadioCallback"
        ></sel-radio-box>
      </div>
      <div class="ste_cont">
        <div class="padding-left-15">
          <span class="tit">股份来源</span>
        </div>
        <sel-radio-box
            :isShow="true"
            :type="'stockSource'"
            :category="'ismp.stockSource'"
            :defaultStr="selData.stockSource.id"
            :selectAble="selData.stockSource.selectAble"
            @selRadioCallback= "selRadioCallback"
        ></sel-radio-box>
      </div>
      <div class="right_formbox">
        <div class="input_item text">
          <span class="tit">是否受限股份转让</span>
          <div class="ct">
            <div class="radio_list">
              <span :key="key" v-for="(item, key) in selData.share.item"
                    class="p_icon_radio"
                    :class="{'checked': item.value === selData.share.value}"
                    :data-value="item.value"
                    @click.stop="checkedRadio('share', $event)"
              >{{item.name}}</span>
            </div>
          </div>
        </div>
        <span>
          选择“受限股份转让”是指此次大宗交易的待成交证券属于大股东、特定股东、董监高所持有的已解除限售进入流通状态，但受到证监会《上市公司股东、董监高减持股份的若干规定》（减持新规）等政策限制的股份
        </span>
      </div>
      <div class="right_formbox">
        <div class="input_item text">
          <span class="tit">是否集中竞价方式取得</span>
          <div class="ct">
            <div class="radio_list">
              <span :key="key" v-for="(item, key) in selData.bidFlag.item"
                    class="p_icon_radio"
                    :class="{'checked': item.value === selData.bidFlag.value}"
                    :data-value="item.value"
                    @click.stop="checkedRadio('bidFlag', $event)"
              >{{item.name}}</span>
            </div>
          </div>
        </div>
      </div>

      <div class="rule_check" v-if="!showHead">
        <span class="icon_check" :class="{'checked': checked}" @click.s.stop="toggleCheck()"></span>
        <label>本人承诺上述信息真实准确，本次交易是我真实意愿表达</label>
      </div>
    </div>

    <inputBox
        v-if="inputBox.show"
        v-model="inputBox.show"
        :title="inputBox.title"
        :checkType="inputBox.checkType"
        :maxLength="inputBox.maxLength"
        :value="inputBox.value"
        :placeholder="inputBox.placeholder"
        @selCallback="selCallback"
    ></inputBox>
    <selBox
        v-if="selBox.show"
        v-model="selBox.show"
        :title="selBox.title"
        :defaultStr="selBox.defaultStr"
        :category="selBox.category"
        :initData="selBox.initData"
        @selCallback="selCallback"
    ></selBox>
    <sel-stock-box
        v-if="selStockBox.show"
        v-model="selStockBox.show"
        :title="selStockBox.title"
        :defaultStr="selStockBox.defaultStr"
        :category="selStockBox.category"
        :initData="selStockBox.initData"
        :idString="'stockAccount'"
        :isMarket="selStockBox.isMarket"
        @selCallback="selCallback"
    ></sel-stock-box>

  </div>
</template>

<script>
import headComponent from '@/components/headComponent'
import selBox from '@/components/selBox' // 选择器
import selStockBox from '@/components/selStockBox' // 选择器
import selRadioBox from '@/components/selRadioBox' // tab
import inputBox from '@/components/inputBox' // 输入器
import {
  queryRecordList,
  queryStockList,
} from '@/service/comServiceNew'
import {queryDictionary} from '@/common/util'

export default {
  props: ['pageParam'],
  components: {
    selRadioBox,
    selBox,
    selStockBox,
    inputBox,
    headComponent,
  },
  data() {
    return {
      ygtUserInfo: $h.getSession('ygtUserInfo', {decrypt: false}),
      serivalId: '',
      showHead: false,
      activeKey: 0,
      checked: false,
      submitParam: {},
      selData: {
        market: {
          name: '交易市场',
          value: '请选择交易市场',
          type: 'ismp.market',
          selected: 0,
          id: '',
        },
        trdaccount: {
          name: '证券账户',
          value: '',
          placeholder: '请选择股东账户',
          type: 'trdaccount',
          selected: 0,
          id: '',
        },
        share: {
          name: '是否受限转让',
          value: '',
          item: [{
            value: '1',
            name: '是',
          },
          {
            value: '0',
            name: '否',
          }],
          maxLength: 1,
        },
        bidFlag: {
          name: '是否集中竞价',
          value: '',
          item: [{
            value: '1',
            name: '是',
          },
            {
              value: '0',
              name: '否',
            }],
          maxLength: 1,
        },
        quota: {
          title: '可卖额度',
          placeholder: '请输入大宗可卖额度（选填）',
          value: '',
          checkType: 'quota',
          maxLength: 12,
        },
        branchCode: {
          title: '营业部代码',
          placeholder: '请输入营业部代码',
          value: '',
          checkType: 'branchCode',
          maxLength: 12,
        },
        contract: {
          title: '约定号',
          placeholder: '请输入约定号',
          value: '',
          checkType: 'contract',
          maxLength: 12,
        },
        trading: {
          title: '交易单元',
          placeholder: '请输入交易单元',
          value: '',
          checkType: 'trading',
          maxLength: 12,
        },
        otherTrading: {
          title: '对方交易单元',
          placeholder: '请输入对方交易单元',
          value: '',
          checkType: 'trading',
          maxLength: 12,
        },
        securityName: {
          title: '证券名称',
          placeholder: '请输入证券名称',
          value: '',
          checkType: 'isCnEnNum',
          maxLength: 12,
        },
        direction: {
          name: '买卖方向',
          value: '',
          placeholder: '请选择买卖方向',
          type: 'ismp.direction',
          selected: 0,
          id: '',
        },
        securityCode: {
          title: '证券代码',
          placeholder: '请输入证券代码',
          value: '',
          checkType: 'securityCode',
          maxLength: 12,
        },
        price: {
          title: '价格',
          placeholder: '请输入价格（元）',
          value: '',
          checkType: 'price',
          maxLength: 12,
        },
        amount: {
          title: '数量',
          placeholder: '请输入数量（张/股）',
          value: '',
          checkType: 'amount',
          maxLength: 12,
        },
        declaretype: {
          name: '申报类型',
          value: '请选择',
          type: 'ismp.declaretype',
          selected: 0,
          id: '',
          selectAble: true,
        },
        shareholder: {
          name: '股东性质',
          value: '请选择',
          type: 'ismp.shareholder',
          selected: 0,
          id: '',
          selectAble: true,
        },
        stocknature: {
          name: '股份性质',
          value: '请选择',
          type: 'ismp.stocknature',
          selected: 0,
          id: '',
          selectAble: true,
        },
        stockSource: {
          name: '股份来源',
          value: '请选择',
          type: 'ismp.stockSource',
          selected: 0,
          id: '',
          selectAble: true,
        },
      },
      selBox: {
        show: false,
        title: '',
        category: '',
        defaultStr: '',
        initData: [],
      },
      selStockBox: {
        show: false,
        isMarket: true,
        title: '',
        category: '',
        defaultStr: '',
        initData: [],
      },
      inputBox: {
        show: false,
        title: '',
        placeholder: '',
        value: '',
        checkType: '',
        maxLength: '',
      },
      inputType: ['quota', 'branchCode', 'contract', 'trading', 'otherTrading', 'securityCode', 'securityName', 'price', 'amount'],
      selectType: ['direction', 'market'], // 买卖方向
      selectStockType: ['trdaccount'], // 证券账号
      selectRadioType: ['declaretype', 'shareholder', 'stocknature', 'stockSource'],
    };
  },
  created () {
    this.serivalId = this.$route.query.serivalId || $h.getSession('result_serivalId') || '';
    if (this.$route.query.serivalId) {
      this.showHead = true;
    }
    $h.clearSession('scrollTop');
  },
  activated () {
    if (this.pageParam.length) {
      this.ygtUserInfo = Object.assign({}, this.ygtUserInfo, this.pageParam[0])
    }

    // this.$store.commit("updateBusinessNextBtnStatus", false); // 隐藏下一步按钮
  },
  computed: {
    showUserInfo () {
      if (!this.selBox.show && !this.inputBox.show && !this.selStockBox.show) {
        this.$store.commit('updateBusinessNextBtnStatus', true) // 显示下一步按钮
        return true
      } else {
        $h.setSession('scrollTop', document.querySelector('.content').scrollTop)
        return false
      }
    },
  },
  watch: {
    'serivalId': {
      handler (newVal) {
        if (newVal) {
          this.queryRecordList('', newVal);
        }
      },
      immediate: true,
    },
    showUserInfo (val) {
      if (val) {
        this.scrolltop = $h.getSession('scrollTop') || 0;
        this.$nextTick(() => {
          document.querySelector('.content').scrollTo(0, this.scrolltop)
        })
      }
    }
  },
  methods: {
    // 查询数据
    async queryRecordList (queryFlag = '', serivalId = '') {
      if(!serivalId) return;
      let _selData = this.selData;

      let results = await queryRecordList({
        serivalId: serivalId,
        businessCode: this.$route.query.type,
        clientId: $h.getSession('ygtUserInfo', {decrypt: false}).clientId,
      });

      let _results = results.results ? results.results : results.DataSet,
          dzjyList = _results[0].dzjyList;
      dzjyList = dzjyList ? JSON.parse(dzjyList) : {};

      let keys = Object.keys(dzjyList)

      keys.forEach(key => {
        if (key === 'share' || key === 'bidFlag'){
          Object.assign(_selData[key], {
            value: dzjyList[key],
            selectAble: !this.showHead,
          })
        }

        if (key === 'contactName') {
          this.$set(this.ygtUserInfo, 'relationName', dzjyList[key])
        }
        if (key === 'contactIdNo') {
          this.$set(this.ygtUserInfo, 'relationIdNo', dzjyList[key])
        }

        if (this.inputType.includes(key)) {
          Object.assign(_selData[key], {
            value: dzjyList[key],
            selectAble: !this.showHead,
          })
        }
        if (this.selectRadioType.includes(key)) {
          Object.assign(_selData[key], {
            id: dzjyList[key],
            defaultStr: dzjyList[key],
            selectAble: !this.showHead,
          })
        }

        if (this.selectType.includes(key)) {
            queryDictionary(
                {type: 'ismp.direction', key: dzjyList[key]},
                (d) => {
                  Object.assign(_selData[key], {
                    value: d.value,
                    id: d.key,
                    selected: d.index,
                    selectAble: !this.showHead,
                  })
                },
            )
        }
      })

      let that = this;
      let res = await queryStockList({
        userId: $h.getSession('ygtUserInfo', {decrypt: false}).userId,
        isQueryZD: '0',
      })
      res = res.results ? res.results : res.DataSet;
      res.map((item, key) => {
        if (dzjyList['trdaccount'].includes(item.stockAccount)) {
          Object.assign(_selData['trdaccount'], {
            value: `${item.stkbdName} ${item.stockAccount}`,
            selectAble: !this.showHead,
            selected: key,
            id: item.stockAccount,
          })
          that.selData['market'].id = item.market;
        }
      })
    },
    // 选择单选框
    checkedRadio () {
      if (this.selData[arguments[0]].selectAble === false) {
        return
      }
      this.selData[arguments[0]].value = arguments[1].currentTarget.dataset.value;
    },
    // 打开选择器和输入组件
    boxClick (type, title) {
      if (this.selData[type].selectAble === false) {
        return
      }
      this.$store.commit('updateBusinessNextBtnStatus', false) // 隐藏下一步按钮
      this.writeIndex = type
      // 打开select选择组件
      if (this.selectType.includes(type)) {
        let _type = this.selData[type].type
        // 学历职业，打开选择器
        this.selBox.title = title
        this.selBox.category = _type
        this.selBox.show = true
        this.selBox.defaultStr = this.selData[type].id;
      } else if (this.selectStockType.includes(type)) {
        let _type = this.selData[type].type
        // 学历职业，打开选择器
        this.selStockBox.title = title
        this.selStockBox.category = _type
        this.selStockBox.show = true
        this.selStockBox.defaultStr = this.selData[type].id;
      } else if (this.inputType.includes(type)) { // 打开input输入组件
        // 打开大宗额度，营业部代码，约定号、交易单元、对方交易单、证券名称
        this.inputBox.show = true
        this.inputBox.title = this.selData[type].title
        this.inputBox.placeholder = this.selData[type].placeholder
        this.inputBox.checkType = this.selData[type].checkType
        this.inputBox.value = this.selData[type].value
        this.inputBox.maxLength = this.selData[type].maxLength
        this.inputBox.tips = this.selData[type].tips
      }
    },
    // 选择器和输入器组件回调方法
    selCallback (d) {
      this.$store.commit('updateBusinessNextBtnStatus', true) // 显示下一步按钮
      let type = this.writeIndex
      if (this.selectType.includes(type)) {
        let value = d.data.value;
        // 学历职业返回
        Object.assign(this.selData[this.writeIndex], {
          value: value,
          id: d.data.key,
          selected: d.index,
        })
      } else if (this.selectStockType.includes(type)) {
        let value = `${d.data.stkbdName} ${d.data.stockAccount}`;
        Object.assign(this.selData[this.writeIndex], {
          value: value,
          id: `${d.data.stockAccount}`,
          selected: d.index,
        })
        this.selData['market'].id = d.data.market;
      } else if (this.inputType.includes(type)) {
        // 打开大宗额度，营业部代码，约定号、交易单元、对方交易单、证券名称
        Object.assign(this.selData[this.writeIndex], {
          value: d.value,
        })
      }
    },
    // 单选组件回调
    selRadioCallback (d) {
        if (this.selData[d.type].selectAble === false) {
          return
        }
        let value = d.data.value;
        Object.assign(this.selData[d.type], {
          value: value,
          id: d.data.key,
          selected: d.index,
        })
    },
    // 勾选复选框
    toggleCheck () {
      this.checked = !this.checked;
    },
    // 提交前的校验
    checkSubmit () {
      let keys = Object.keys(this.selData),
          that = this;

      try {
        keys.forEach( key => {
          if (
              that.selData[key].value && !this.selData[key].value.includes('请选择') ||
              that.selData[key].id ||
              key === 'quota'
          ){
            if (
                that.selectType.includes(key) ||
                that.selectStockType.includes(key) ||
                that.selectRadioType.includes(key)
            ) {
              that.submitParam[key] = this.selData[key].id;
            } else {
              that.submitParam[key] = this.selData[key].value
            }
          } else {
            throw Error(`${that.selData[key].title || that.selData[key].name}不能为空`)
          }
        })
      } catch (e) {
        let err = e.toString().replace('Error:', '')
        _hvueToast({
          mes: err,
        })
        return false;
      }
      return true;
    },
    doCheck () {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        if (this.checked && this.checkSubmit()) {
          // 可以下一步
          resolve();
        }
      });
    },
    /**
     * 请求参数打包
     */
    reqParamPackage() {
      this.submitParam['organName'] = this.ygtUserInfo.name; // 机构名称
      this.submitParam['licenseCode'] = this.ygtUserInfo.identityNum; // 营业执照
      this.submitParam['contactName'] = this.ygtUserInfo.relationName; // 办理人姓名
      this.submitParam['contactIdNo'] = this.ygtUserInfo.relationIdNo; // 办理人证件号

      this.submitParam['mobile'] = this.ygtUserInfo.mobile;
      this.submitParam['telephone'] = this.ygtUserInfo.telephone;
      this.submitParam['type'] = this.selData.declaretype.id;
      this.submitParam['stock'] = this.selData.stocknature.id;

      // 业务请求参数封装
      let params = {
        queryValue: 'dzjy_jg',
        keyWords: JSON.stringify(this.submitParam),
      }; // 提交需要的参数
      return params;
    },
    putFormData() {
      let formData = {
        dzjyList: this.submitParam
      }; // 需要保存的表单数据
      return formData;
    },
    pageBack() {
      this.$bus.emit('isActiveTabkey', this.$route.query.key) // 通过vuebus调用
      this.$router.go(-1)
    },
  }
};
</script>
<style>
.blockTrade{
  overflow: auto;
}
</style>
