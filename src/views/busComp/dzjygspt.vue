<template>
  <div>
    <ul class="type_selelist">
      <li :key="key" v-for="(item, key) in typeList" @click.stop="nextStep(item.type)">
        <p>{{item.name}}</p>
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  props: ["pageParam"],
  data() {
    return {
      businessCode: "",
      typeList: [
        {
          type: "dzjy",
          name: "大宗交易申请",
          info: "资金密码用于银证转账校验"
        },
        {
          type: "gsptzqzr",
          name: "固收平台债券转让交易申请",
        },
      ]
    };
  },
  activated() {
    this.$store.commit("updateBusinessNextBtnStatus", false); // 隐藏下一步按钮
  },
  methods: {
    nextStep(type) {
      this.businessCode = type;
      this.$parent.emitNextEvent();
    },
    doCheck() {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        // 可以下一步
        resolve();
      });
    },
    /**
     * 请求参数打包
     */
    reqParamPackage() {
      // 业务请求参数封装
      let params = {
        businessCode: this.businessCode
      }; // 提交需要的参数
      return params;
    },
    putFormData() {
      let formData = {}; // 需要保存的表单数据
      return formData;
    }
  }
};
</script>
