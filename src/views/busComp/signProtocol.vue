<!-- 协议签署组件，单独的协议步骤，直接展示协议内容进行签署，用到的业务：电子签名约定书、市价委托权限开通 -->
<template>
  <div v-cloak>
    <div class="xy_box" :style="{'padding-bottom':divHeight + 'px'}">
      <div class="title">《{{name}}》</div>
      <div class="xy_cont" v-html="content"></div>
    </div>
    <div ref="bottom_check" class="bottom_check">
      <div class="rule_check">
        <span
          @click.stop="signProtocolCheck = !signProtocolCheck"
          class="icon_check"
          :class="{ checked: signProtocolCheck }"
        ></span>
        <label>已阅读并同意以上协议</label>
      </div>
      <div class="ce_btn">
        <a v-throttle class="ui button block rounded" @click.stop="nextStep">开始签署</a>
      </div>
    </div>
  </div>
</template>

<script>
import { getProtocolById } from '@/service/comServiceNew'
export default {
  props: ['pageParam'],
  data () {
    return {
      name: '',
      content: '',
      signProtocolCheck: false,
      divHeight: '0'
    }
  },
  activated () {
    this.divHeight = this.$refs.bottom_check.clientHeight
    if (this.pageParam.length < 1) {
      _hvueAlert({ mes: '未查询到协议', title: '提示' })
    }
    this.$store.commit('updateBusinessNextBtnStatus', false) // 隐藏下一步按钮
    this.getProtocol()
  },
  methods: {
    getProtocol () {
      let param = {
        agreementId: this.pageParam[0].agreementId,
        userId: $h.getSession('ygtUserInfo', {decrypt: false}).userId
      }
      getProtocolById(param)
        .then(data => {
          if (data.error_no === '0') {
            let results = data.results || data.DataSet
            this.name = results[0].agreeBrief
            this.content = results[0].agreeContent
          } else {
            _hvueToast({ mes: data.error_info })
          }
        })
        .catch(e => {
          _hvueToast({ mes: e.message })
        })
    },
    checkSubmit () {
      if (!this.signProtocolCheck) {
        _hvueToast({ mes: '您还未勾选同意协议' })
        return false
      }
      return true
    },
    // 调用父组件的下一步事件
    nextStep () {
      this.$parent.emitNextEvent()
    },
    doCheck () {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        if (this.checkSubmit()) {
          // 可以下一步
          resolve()
        }
      })
    },
    /**
     * 请求参数打包
     */
    reqParamPackage () {
      // 业务请求参数封装
      let params = {
        agreementId: this.pageParam[0].agreementId
      }
      return params
    },
    putFormData () {
      let formData = {
        signProtocol: { agreementId: this.pageParam[0].agreementId }
      }
      return formData
    }
  }
}
</script>
<style scoped>
.bottom_check {
  position: fixed;
  bottom: 0;
  border-top: 0.05rem solid #f9f9f9;
  z-index: 99999;
  background: white;
  width: 100%;
}
</style>
