<!--适当性匹配-->
<template>
  <div>
    <div class="approp_box">
      <div class="approp_result">
        <h5>
          <i class="icon">
            <img :src="this.pageParam[0].matchingFlag == '0' ? matchImg : notMatchImg" />
          </i>
          您的适当性评估{{this.pageParam[0].matchingFlag == '0' ? '匹配' : '不匹配'}}
        </h5>
      </div>
      <div class="approp_info">
        <ul>
          <li>
            <span class="tit">个人风险承受能力等级</span>
            <p>{{pageParam[0].riskLevelDesc}}</p>
          </li>
          <li>
            <span class="tit">拟接受服务</span>
            <p>{{pageParam[0].businessName}}</p>
          </li>
          <li>
            <span class="tit">服务风险等级</span>
            <p>{{pageParam[0].proRiskLevelDesc}}</p>
          </li>
          <li>
            <span class="tit">投资品种</span>
            <p>{{pageParam[0].investTypeDesc}}</p>
          </li>
        </ul>
      </div>
      <div class="approp_tips">
        <p>
          本人在此确认自身风险承受能力等级该金融服务风险等级
          <span
            class="ablue"
          >{{this.pageParam[0].matchingFlag == '0' ? '匹配' : '不匹配'}}</span> 。本人投资该项产品或接受该项服务的决定，系本人独立、自主、真实的意思表示，与贵公司及相关从业人员无关。
        </p>
      </div>
    </div>
  </div>
</template>
<script>
import matchImg from '@/assets/images/appro_ok.png'
import notMatchImg from '@/assets/images/appro_error.png'
export default {
  props: ['pageParam'],
  data () {
    return {
      matchImg: matchImg,
      notMatchImg: notMatchImg,
      isChecked: false
    }
  },
  computed: {
    isMatch: () => {
      console.log(this.pageParam)
    }
  },
  activated () {
    // 隐藏business.vue的下一步按钮
    this.$store.commit('updateBusinessNextBtnStatus', false) // 隐藏下一步按钮
    setTimeout(() => {
      this.$bus.emit('sdxParam', this.pageParam[0]) // 通过vuebus调用
    }, 200)
  },
  methods: {
    doCheck () {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        // 可以下一步
        resolve()
      })
    },
    /**
     * 请求参数打包
     */
    reqParamPackage () {
      // 业务请求参数封装
      let params = {}
      return params
    },
    putFormData () {
      let formData = {
        sdxCheck: {
          matchingFlag: this.pageParam[0].matchingFlag,
          riskLevelDesc: this.pageParam[0].riskLevelDesc,
          riskScore: parseInt(this.pageParam[0].paperScore),
          proRiskLevelDesc: this.pageParam[0].proRiskLevelDesc // 服务风险等级
        }
      }
      return formData
    },
    toIndex () {
      this.$router.push({ name: 'index', params: {} })
    }
  }
}
</script>
