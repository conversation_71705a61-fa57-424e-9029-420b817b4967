/*
 * @Author: lyt 转托管信息确认
 * @Date: 2021-9-8
 * @Last Modified by: Way
 * @Last Modified time: 2021-9-8
 */
<template>
    <div>
        <headComponent headerTitle="转托管信息确认"></headComponent>
        <div class="tips">
            <span>
                请您确认以下信息：
            </span>
        </div>
        <ztg-info-confirm :isShow="showZtgInfoC" :dataList="dataList"></ztg-info-confirm>
        <div class="ce_btn mt20">
            <a
                class="ui button block rounded"
                v-throttle
                @click.stop="nextStep"
            >
                下一步
            </a>

        </div>
    </div>
</template>

<script>
import headComponent from '@/components/headComponent' // 头部
import ztgInfoConfirm from "../../components/ztgInfoConfirm";

export default {
  props: {
    pageParam: {
      type: Array,
    },
  },
  components: {
    headComponent,
      ztgInfoConfirm
  },
  data () {
    return {
      userInfoModify: {}, // 提交表单的参数
      ygtUserInfo: $h.getSession('ygtUserInfo', {decrypt: false}),
        showZtgInfoC: false, // 展示转托管信息确认
        dataList: [], //确认的转托管信息
    }
  },
  created () {
    // 隐藏头部
    // this.$store.commit('updateIsShowHead', false)
    // 更改下一步按钮的文字
      this.$store.commit('updateBusinessNextBtnStatus', true) // 显示下一步按钮
      this.showZtgInfoC = false;
      this.dataList = this.pageParam || this.$route.query.dataList
  },
  methods: {
    checkSubmit () {
      let _selData = this.selData
      if (!_selData.brokerName.value) {
        _hvueToast({
          mes: '请完善转入券商',
        })
        return false
      }
        if (!_selData.brokerNumber.value) {
            _hvueToast({
                mes: '请完善转入券商席位号',
            })
            return false
        }

        this.dataList = this.ztgInfo.map(item => {
            let data = JSON.parse(item.data);
            data[0]['zqName'] = _selData.brokerName.value;
            data[0]['seatNo'] = _selData.brokerNumber.value
            return data[0];
        })

        console.log(this.dataList)
      return true
    },
    doCheck () {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
          resolve()
      })
    },
    /**
         * 请求参数打包
         */
    reqParamPackage () {
      // 业务请求参数封装
      let params = this.userInfoModify
      return params
    },
    /**
         * 表单参数打包
         */
    putFormData () {
      let formData = {
        userInfoModify: this.userInfoModify,
      }
      return formData
    },
    back () {
      this.$router.go(-1)
    },
  },
}
</script>
<style scoped>
    .tips{
        padding: 0.1rem;
        color: #999999;
    }
</style>
