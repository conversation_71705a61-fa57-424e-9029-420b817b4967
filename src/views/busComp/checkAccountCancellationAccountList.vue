/* * 销户账号条件检测--接口版 * @Author: Way * @Date: 2020-08-22 12:50:29 *
@Last Modified by: Way * @Last Modified time: 2020-08-27 10:52:19 */
<template>
  <div>
    <headComponent header-title="销户条件判断"></headComponent>
    <div v-show="showPage == 0" class="cond_box">
      <ul>
        <li
          v-show="pageParam[0].accountCancellationClientList != undefined"
          :class="clientFlag == '1' ? 'error' : 'ok'"
        >
          <div class="tit">
            <p>客户号</p>
            <span v-if="clientFlag == '1'" class="tips"
              >账户未符合条件，点击查看详情</span
            >
            <span v-else class="tips">账户已符合条件</span>
            <a class="link" @click.stop="showAccount('0')"></a>
          </div>
        </li>
        <li
          v-show="pageParam[0].accountCancellationAssetList != undefined"
          :class="assetFlag == '1' ? 'error' : 'ok'"
        >
          <div class="tit">
            <p>资金账户</p>
            <span v-if="assetFlag == '1'" class="tips"
              >账户未符合条件，点击查看详情</span
            >
            <span v-else class="tips">资金账户已全部符合条件</span>
            <a class="link" @click.stop="showAccount('1')"></a>
          </div>
        </li>
        <li
          v-show="pageParam[0].accountCancellationStockAccountList != undefined"
          :class="stockFlag == '1' ? 'error' : 'ok'"
        >
          <div class="tit">
            <p>证券账户</p>
            <span v-if="stockFlag == '1'" class="tips"
              >账户未符合条件，点击查看详情</span
            >
            <span v-else class="tips">证券账户已全部符合条件</span>
            <a class="link" @click.stop="showAccount('2')"></a>
          </div>
        </li>
        <li
          v-show="pageParam[0].accountCancellationOtcAccountList != undefined"
          :class="otcFlag == '1' ? 'error' : 'ok'"
        >
          <div class="tit">
            <p>OTC账户</p>
            <span v-if="otcFlag == '1'" class="tips"
              >账户未符合条件，点击查看详情</span
            >
            <span v-else class="tips">OTC账户已全部符合条件</span>
            <a class="link" @click.stop="showAccount('4')"></a>
          </div>
        </li>
        <li
          v-show="pageParam[0].accountCancellationFundAccountList != undefined"
          :class="fundFlag == '1' ? 'error' : 'ok'"
        >
          <div class="tit">
            <p>基金账户</p>
            <span v-if="fundFlag == '1'" class="tips"
              >账户未符合条件，点击查看详情</span
            >
            <span v-else class="tips">基金账户已全部符合条件</span>
            <a class="link" @click.stop="showAccount('3')"></a>
          </div>
        </li>
      </ul>
    </div>
    <div v-show="showPage == 0 && !totalFlag" class="cond_tips">
      <p>抱歉，您不满足销户条件，无法办理。</p>
    </div>

    <div v-show="showPage == 1" class="xh_must_box">
      <h5 class="title">
        <span>{{ accountTypeName }}</span>
      </h5>
      <div class="xh_must_list">
        <div
          v-for="(d, index) in showAccountList"
          :key="index"
          class="xh_must_item"
        >
          <div class="tit">
            <p>{{ d.marketName ? d.marketName + '：' : '' }}{{ d.account }}</p>
            <em>{{ d.accountName }}</em>

            <span v-if="d.isAccountCancellationFlag == '0'" class="status ok"
              >通过</span
            >
            <span v-else class="status error">未通过</span>
          </div>

          <ul v-if="d.isAccountCancellationFlag == '1'" class="list">
            <li v-for="(a, i) in d.msg" :key="i" class="error">
              <p>{{ a }}</p>
            </li>
          </ul>
        </div>
      </div>
    </div>
    <div
      v-show="
        clientFlag == '1' ||
          assetFlag == '1' ||
          stockFlag == '1' ||
          otcFlag == '1' ||
          fundFlag == '1'
      "
      class="ce_btn mt20"
    >
      <a
        v-throttle
        class="ui button block rounded border"
        @click.stop.prevent="pageBack"
        >重新选择</a
      >
    </div>
  </div>
</template>
<script>
import { closeFlow } from '@/service/comServiceNew';
import headComponent from '@/components/headComponent'; // 头部
import { queryDictionary } from '@/common/util';

export default {
  components: {
    headComponent
  },
  props: ['pageParam'],
  data() {
    return {
      showPage: 0,

      clientFlag: '',
      assetFlag: '',
      stockFlag: '',
      otcFlag: '',
      fundFlag: '',

      accountCancellationClientList: [], // 客户号
      accountCancellationAssetList: [], // 资金账号
      accountCancellationStockAccountList: [], // 股东户
      accountCancellationFundAccountList: [], // 基金户
      accountCancellationOtcAccountList: [], // otc

      accountTypeName: '',
      showAccountList: []
    };
  },
  computed: {
    totalFlag() {
      if (
        this.clientFlag == '1' ||
        this.assetFlag == '1' ||
        this.stockFlag == '1' ||
        this.otcFlag == '1' ||
        this.fundFlag == '1'
      ) {
        // 有为false的情况不满足
        return false;
      }
      return true;
    },
    //是否审核驳回
    isFlowReject() {
      return this.$parent.flow.currentStepInfo.flow_reject === '1';
    }
  },
  activated() {
    /*if(!$h.getSession('chooseAccountXH') && this.$route.query.type === 'xh'){
      this.$parent.rollback('chooseAccount')
    }*/
    this.$store.commit('updateIsShowHead', false); // 隐藏head
    // 转换结果集格式
    this.accountCancellationClientList = this.pageParam[0]
      .accountCancellationClientList
      ? JSON.parse(this.pageParam[0].accountCancellationClientList)
      : [];
    this.accountCancellationAssetList = this.pageParam[0]
      .accountCancellationAssetList
      ? JSON.parse(this.pageParam[0].accountCancellationAssetList)
      : [];
    this.accountCancellationStockAccountList = this.pageParam[0]
      .accountCancellationStockAccountList
      ? JSON.parse(this.pageParam[0].accountCancellationStockAccountList)
      : [];
    this.accountCancellationFundAccountList = this.pageParam[0]
      .accountCancellationFundAccountList
      ? JSON.parse(this.pageParam[0].accountCancellationFundAccountList)
      : [];
    this.accountCancellationOtcAccountList = this.pageParam[0]
      .accountCancellationOtcAccountList
      ? JSON.parse(this.pageParam[0].accountCancellationOtcAccountList)
      : [];
    // 重置是否满足条件
    this.clientFlag = '';
    this.assetFlag = '';
    this.stockFlag = '';
    this.otcFlag = '';
    this.fundFlag = '';

    // 获取是否满足条件
    this.clientFlag =
      this.pageParam[0].accountCancellationClientList != undefined
        ? this.accountCancellationClientList[0].isAccountCancellationFlag
        : '';
    for (let i = 0; i < this.accountCancellationAssetList.length; i++) {
      let el = this.accountCancellationAssetList[i];
      if (el.isAccountCancellationFlag === '1') {
        // 不满足
        this.assetFlag = '1';
        el.msg = el.msg.split('|');
        this.$set(this.accountCancellationAssetList, i, el);
      }
    }
    for (let i = 0; i < this.accountCancellationStockAccountList.length; i++) {
      let el = this.accountCancellationStockAccountList[i];
      if (el.isAccountCancellationFlag === '1') {
        // 不满足
        this.stockFlag = '1';
        el.msg = el.msg.split('|');
        this.$set(this.accountCancellationStockAccountList, i, el);
      }
    }

    for (let i = 0; i < this.accountCancellationOtcAccountList.length; i++) {
      let el = this.accountCancellationOtcAccountList[i];
      if (el.isAccountCancellationFlag === '1') {
        // 不满足
        this.otcFlag = '1';
        el.msg = el.msg.split('|');
        this.$set(this.accountCancellationOtcAccountList, i, el);
      }
    }

    for (let i = 0; i < this.accountCancellationFundAccountList.length; i++) {
      let el = this.accountCancellationFundAccountList[i];
      if (el.isAccountCancellationFlag === '1') {
        // 不满足
        this.fundFlag = '1';
        el.msg = el.msg.split('|');
        this.$set(this.accountCancellationFundAccountList, i, el);
      }
    }
    if (
      this.clientFlag == '1' ||
      this.assetFlag == '1' ||
      this.stockFlag == '1' ||
      this.otcFlag == '1' ||
      this.fundFlag == '1'
    ) {
      // 有为1的情况不满足
      this.$store.commit('updateNextBtnText', '返回首页');
      this.$store.commit('updateBusinessNextBtnStatus', false) // 隐藏下一步按钮
      return;
    }
    this.$store.commit('updateNextBtnText', '下一步');
  },
  methods: {
    // 展示账户明细
    showAccount(accountType) {
      let _this = this;
      this.showPage = 1;
      queryDictionary(
        { type: 'ismp.xh.chooseAccountType', key: accountType },
        function(d) {
          _this.accountTypeName = d.value;
        }
      );

      switch (accountType) {
        case '0':
          this.showAccountList = this.accountCancellationClientList;
          break;
        case '1':
          this.showAccountList = this.accountCancellationAssetList;
          break;
        case '2':
          this.showAccountList = this.accountCancellationStockAccountList;
          break;
        case '3':
          this.showAccountList = this.accountCancellationFundAccountList;
          break;
        case '4':
          this.showAccountList = this.accountCancellationOtcAccountList;
          break;
        default:
          break;
      }
    },
    doCheck() {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        // 开发环境不做拦截 || process.env.NODE_ENV == "development"
        if (this.totalFlag) {
          // 可以下一步
          resolve();
        } else {
          // 返回业务办理首页;
          this.$router.push({ name: 'index' });
        }
      });
    },
    /**
     * 请求参数打包
     */
    reqParamPackage() {
      // 业务请求参数封装
      let params = {
        param1: '',
        param2: ''
      };
      return params;
    },
    putFormData() {
      let formData = {};
      return formData;
    },
    pageBack() {
      if (this.showPage == 1) {
        this.showPage = 0;
      } else if (this.isFlowReject) {
        _hvueAlert({
          mes:
            '该流程无法返回，请按要求满足销户条件后点击继续办理，或点击放弃办理重新提交销户流程。'
        });
      } else {
        this.$parent.rollback('chooseAccount');
      }
    },
    endBusiness() {
      closeFlow({
        userId: $h.getSession('ygtUserInfo', { decrypt: false }).userId,
        businessCode: this.$route.query.type,
        serivalId: this.$parent.publicParam.serivalId
      }).then((res) => {
        if (res.error_no === '0') {
          this.$router.push({ name: 'index' });
        } else {
          _hvueAlert({
            title: '提示',
            mes: res.error_info
          });
        }
      });
    }
  }
};
</script>
