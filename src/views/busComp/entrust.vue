<template>
  <div>
    <div class="wtclass_main">
      <ul class="wtclass_list">
        <li v-for="(it,index) in entrustList" :key="index">
          <span>{{it.entrustName}}</span>
          <div
            class="ui switch"
            :class=" {checked : it.checkStatus == '1'}"
            @click.stop="modifyState(index,$event,it)"
          >
            <div class="ui switch-inner">
              <div class="ui switch-arrow"></div>
            </div>
          </div>
        </li>
      </ul>
    </div>
    <div v-show="showProtocolPage">
      <ul class="rule_lylist" style=" text-align: center; margin:0 0.15rem;">
        <li v-for="(it,index) in protocolList" :key="index" v-show="it.isShow">
          <a @click.stop="toDetail(it.agreementId,it.agreeName,index)">《{{it.agreeName}}》</a>
        </li>
      </ul>
      <div class="rule_check" style="text-align: center;">
        <span
          class="icon_check"
          @click.stop="signProtocolCheck = !signProtocolCheck"
          :class="{ checked: signProtocolCheck }"
        ></span>
        <label>已阅读并同意签署以上协议</label>
      </div>
    </div>
    <div class="ce_btn mt20">
      <a v-throttle class="ui button block rounded" @click.stop="nextStep">确认修改</a>
    </div>
  </div>
</template>

<script>
import { getProtocolByType } from '@/service/comServiceNew'
export default {
  props: ['pageParam'],
  data () {
    return {
      protocolList: {}, // 协议列表
      entrustList: [],
      oriEntrustWay: '', // 原委托方式
      signProtocolCheck: false,
      showProtocolPage: false, // 是否展示协议签署页面，当开通委托需要签署协议时展示
      submitParams: {}
    }
  },
  activated () {
    if (this.pageParam.length < 1) {
      _hvueAlert({ mes: '未查询到委托方式', title: '提示' })
    }
    this.entrustList = []
    this.$store.commit('updateBusinessNextBtnStatus', false) // 隐藏下一步按钮
    let entrusts = JSON.parse(this.pageParam[0].entrustList)
    for (let i = 0; i < entrusts.length; i++) {
      let el = entrusts[i]
      // 处理初始数据时，把委托方式开通状态赋值给用户勾选状态，以做区分
      el.checkStatus = el.entrustStatus
      this.entrustList.push(el)
    }
    let userEntrust = JSON.parse(this.pageParam[0].userEntrust)
    this.oriEntrustWay = userEntrust[0].entrustWay
  },
  methods: {
    // 调用父组件的下一步事件
    nextStep () {
      this.$parent.emitNextEvent()
    },
    // 修改委托方式
    modifyState (index, ev, it) {
      ev.preventDefault()
      // 重新赋值当前办理的委托方式对象
      if (it.allowOpen == '0' && it.entrustStatus == '0') {
        _hvueToast({ mes: '该委托方式不允许网上办理开通' })
        return
      }
      if (it.allowClose == '0' && it.entrustStatus == '1') {
        _hvueToast({ mes: '该委托方式不允许网上办理关闭' })
        return
      }
      let _proList = this.protocolList
      if (
        it.needAgree == '1' &&
        it.entrustStatus == '0' &&
        it.checkStatus == '0'
      ) {
        // 需要签署协议,展示协议界面
        this.showProtocolPage = true
        if ($h.isEmptyObject(_proList[index])) {
          this.getProtocol(it.agreeType, index)
        } else {
          let el = _proList[index]
          el.isShow = true
        }
      } else if (it.needAgree == '1' && it.checkStatus == '1') {
        // 关闭委托时，不展示协议
        if (!$h.isEmptyObject(_proList[index])) {
          let el = _proList[index]
          el.isShow = false
        }
        this.showProtocolPage = false

        let list2 = Object.values(_proList)
        if (list2.length > 0) {
          for (let j = 0; j < list2.length; j++) {
            if (list2[j].isShow) {
              this.showProtocolPage = true
            }
          }
        }
      }
      it.checkStatus = it.checkStatus == '1' ? '0' : '1'
    },
    checkSubmit () {
      if (this.showProtocolPage && !this.signProtocolCheck) {
        _hvueToast({ mes: '您还未勾选同意协议' })
        return false
      }
      let _this = this
      let list = _this.entrustList
      let entrustWay = [] // 拼接需要改变的委托方式code
      let resultList = [] // 结果页需要展示的数据
      for (let i = 0; i < list.length; i++) {
        let el = list[i]
        // 改变了状态的委托方式
        if (el.checkStatus != el.entrustStatus) {
          entrustWay.push(el.entrustCode)
          el.views = el.entrustName
          el.dealBusinessResultState =
            el.checkStatus == '1' ? '开通成功' : '关闭成功'
          resultList.push(el)
        }
      }
      if (entrustWay.length == 0) {
        _hvueToast({ mes: '委托方式未做改变，无需修改' })
        return false
      }
      let agreeIds = []
      let list2 = Object.values(_this.protocolList)
      if (list2.length > 0) {
        for (let j = 0; j < list2.length; j++) {
          if (list2[j].isShow) {
            agreeIds.push(list2[j].agreementId)
          }
        }
      }
      this.submitParams = {
        oriEntrustWay: this.oriEntrustWay, // 原有已开通的所有委托方式
        entrustWay: entrustWay.join(''), // 现在需要改变的委托方式
        agreementId: agreeIds.join(',') // 协议编号
      }
      return true
    },
    doCheck () {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        if (this.checkSubmit()) {
          // 可以下一步
          resolve()
        }
      })
    },
    /**
     * 请求参数打包
     */
    reqParamPackage () {
      // 业务请求参数封装
      let params = this.submitParams // 提交需要的参数
      return params
    },
    putFormData () {
      let formData = {
        entrust: this.submitParams
      } // 需要保存的表单数据
      return formData
    },
    toDetail (agreementId, agreementName, index) {
      // 查看协议详情
      this.$router.push({
        name: 'agreementDetail',
        query: {
          agreementId: agreementId,
          agreementTitle: agreementName,
          agreementIndex: index,
          type: this.$route.query.type
        }
      })
    },
    getProtocol (agreeType, index) {
      let _this = this
      getProtocolByType(
        {
          userId: $h.getSession('ygtUserInfo', {decrypt: false}).userId,
          queryType: '0',
          queryValue: agreeType
        },
        {}
      ).then(
        res => {
          if (res.error_no === '0') {
            let results = res.results || res.DataSet
            for (let i = 0; i < results.length; i++) {
              let el = results[i]
              el.isShow = true
              el.isRead = false
              this.$set(this.protocolList, index, el)
            }
          } else {
            _hvueToast({
              icon: 'error',
              mes: res.error_info
            })
          }
        },
        err => {
          console.log(err)
        }
      )
    },
    /** ********************************************子组件公共方法定义***end************************************* */
    // 提交后对结果进行处理的方法
    handleResultFunc (res) {
      // 处理完成后，继续执行下一步
      this.$parent.emitNextEvent()
    }
  }
}
</script>
