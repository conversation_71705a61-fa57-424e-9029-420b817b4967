<!--两融预约开户身份信息确认-->
<template>
  <div>
    <div class="com_infobox">
      <ul>
        <li>
          <span class="tit">姓名</span>
          <p>{{initData.name}}</p>
        </li>
        <li>
          <span class="tit">客户号</span>
          <p>{{initData.clientId}}</p>
        </li>
        <li>
          <span class="tit">证件类型</span>
          <p>{{initData.identityType == '00' ? '身份证' : '非身份证'}}</p>
        </li>
        <li>
          <span class="tit">证件号码</span>
          <p>{{initData.identityNum}}</p>
        </li>
      </ul>
    </div>
    <div class="input_form">
      <div class="ui field text">
        <label class="ui label">手机号</label>
        <input type="text" class="ui input" readonly="readonly" v-model="initData.mobile" />
        <!-- <a class="txt_close" @click.stop="initData.mobile=''" v-show="initData.mobile!=''"></a> -->
      </div>
      <div class="ui field text">
        <label class="ui label">验证码</label>
        <input type="number" class="ui input" placeholder="请输入6位短信验证码" v-model="verifyCode" />
        <a class="txt_close" @click.stop="verifyCode=''" v-show="verifyCode!=''"></a>
        <smsTimer v-model="startFlag" @sendSms="getVerifyCode"></smsTimer>
      </div>
    </div>
    <p class="bot_tips mt10">
      若您的手机号码已变更，请先
      <a  @click.stop="toModifyMobile" class="link">修改手机号码</a>
    </p>
  </div>
</template>
<script>
import smsTimer from '../../components/smsTimer'
import { queryDictionary } from '@/common/util'
import { sendMobileMsg } from '@/service/comServiceNew'
export default {
  props: ['pageParam'],
  components: {
    smsTimer
  },
  data () {
    return {
      initData: {},
      startFlag: false, // 是否开始倒计时
      verifyCode: '', // 用户输入的验证码
      smsCodeMaxLength: 6,
      smsNo: '', // 短信类型数据字典
      sendSmsFlag: '' // 是否已经发送了验证码
    }
  },
  watch: {
    verifyCode () {
      if (this.verifyCode.length > this.smsCodeMaxLength) {
        this.verifyCode = String(this.verifyCode).slice(
          0,
          this.smsCodeMaxLength
        )
      }
    }
  },
  activated () {
    this.initData = this.pageParam[0] || $h.getSession('ygtUserInfo', {decrypt: false})
  },
  mounted () {
    // 查询短信类型数据字典
    queryDictionary(
      { type: 'ismp.sms_type', value: this.$parent.businessName },
      data => {
        this.smsNo = data.key
      }
    )
  },
  methods: {
    /** ************子组件公共方法定义****** */
    doCheck () {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        // 判断是否已经发送了验证码
        if (!this.sendSmsFlag) {
          _hvueToast({ mes: '请先发送验证码' })
          return false
        }
        // 检查是否填写了验证码
        if (this.verifyCode == '') {
          _hvueToast({ mes: '请输入验证码' })
          return false
        }
        if (this.verifyCode.length != 6) {
          _hvueToast({ mes: '请输入正确位数的验证码' })
          return false
        }
        resolve()
      })
    },
    /**
     * 请求参数打包
     */
    reqParamPackage () {
      // 业务请求参数封装
      let params = {
        mobile: this.mobile,
        verifyCode: this.verifyCode,
        smsNo: this.smsNo,
        businessCode: this.$route.query.type
      }
      return params
    },
    putFormData () {
      let formData = {}
      return formData
    },
    /** ************子组件公共方法定义****** */
    getVerifyCode () {
      // 获取短信验证码
      var params = {
        flow_name: this.$route.query.type,
        smsNo: this.smsNo,
        businessCode: this.$route.query.type,
        userId: $h.getSession('ygtUserInfo', {decrypt: false}).userId,
        mobile: this.initData.mobile
      }
      sendMobileMsg(params).then(res => {
        if (res.error_no == '0') {
          // 短信验证码发送成功，开始倒计时
          console.log(this)
          this.startFlag = true
          this.sendSmsFlag = true
        } else {
          _hvueAlert({ title: '提示', mes: res.error_info })
        }
      })
    },
    toModifyMobile () {
      this.$store.commit('updateBusinessNextBtnStatus', true) // 显示下一步按钮
      this.$router.push({
        name: 'business',
        query: { type: 'xgsjh', name: '修改手机号', isBreakPoint: '0' }
      })
    },
    deactivated () {
      console.log('deactivated')
      this.startFlag = false
    }
  }
}
</script>
