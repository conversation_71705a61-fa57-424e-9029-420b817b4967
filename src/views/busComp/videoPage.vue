<!-- 视频见证组件 -->
<template>
  <div v-cloak>
    <!--头部 -->
    <div v-show="pageStatus != 3">
      <div class="video_zbbox">
        <h5>
          接下来将由我司工作人员与您进行视频连线认证
          <br />开始前请做好以下准备：
        </h5>
        <ul>
          <li>
            <i class="icon">
              <img src="@/assets/images/vd_icon01.png" />
            </i>
            <p>保持光线充足</p>
          </li>
          <li>
            <i class="icon">
              <img src="@/assets/images/vd_icon02.png" />
            </i>
            <p>周边环境安静</p>
          </li>
          <li>
            <i class="icon">
              <img src="@/assets/images/vd_icon03.png" />
            </i>
            <p>WIFI或4G网络环境</p>
          </li>
          <li>
            <i class="icon">
              <img src="@/assets/images/vd_icon04.png" />
            </i>
            <p>视频认证时间</p>
            <em>工作日 9:00-16:00</em>
          </li>
        </ul>
      </div>
      <div class="ce_btn mt20">
        <a class="ui button block rounded" @click.stop="setIdInRedis">开始视频</a>
      </div>
    </div>

    <div v-show="pageStatus == 3">
      <div class="reject_info">
        <div class="item">
          <h5>{{rejectTpye}}</h5>
          <p>{{rejectReason}}</p>
        </div>
      </div>
      <div class="ce_btn">
        <a
          v-throttle
          class="ui button block rounded"
          v-show="rejectStatus==4 || rejectStatus ==3 || rejectStatus ==2"
          @click.stop="rejectVideoQuery"
        >重新上传证件</a>
        <a v-throttle class="ui button block rounded" v-show="rejectStatus==1" @click.stop="setIdInRedis">重新视频</a>
      </div>
    </div>
    <!-- 同花顺视频 -->
    <witness-status
        v-if="showThsgb"
        :showWitnessStatus="showThsgb"
        :serivalId="serivalId"
        :businessCode="businessCode"
        :bizType="bizType"
        :chooseAccount="chooseAccount"
        @cancelQueueCB="cancelQueueCB"
    ></witness-status>
  </div>
</template>

<script>
import { setSidInRedis } from '@/service/comServiceNew'
import '@/nativeShell/nativeCallH5'
import { callMessageNative } from 'thinkive-hvue'
import { queryDictionary } from '@/common/util'
import witnessStatus from '@/views//busComp/witnessStatus'

export default {
  props: ['pageParam'],
  name: 'videoPage',
  components: {
    witnessStatus
  },
  data () {
    return {
      category: 'ismp.biz_type',
      rejectInfo: {
        flag: false,
        slideDown: true,
        dataList: []
      },
      pageStatus: 0, // 视频页面 0:准备页面 1：排队中 2:视频见证通过 3:视频失败
      rejectReason: '',
      rejectStatus: 0, // 视频失败状态 1：视频见证未通过 2：身份证不合规 3:身份证正面 4：身份证反面 5：公安校验不通过
      rejectTpye: '', // 视频失败状态 1：视频见证未通过 2：身份证不合规 3:身份证正面 4：身份证反面 5：公安校验不通过
      anychatH5AppId: '',
      ocrUserInfo: $h.getSession('ocrUserInfo') || {},
      bizType: '',
      businessCode: this.$route.query.type,
      serivalId: this.$parent.publicParam.serivalId,
      rejectStepInfo: [],
      chooseAccount: '',
      fromTHSGB: $h.getSession('fromTHSGB'), // 渠道同花顺公版
      showThsgb: false
    }
  },
  activated () {
    window.videoCallBack = this.videoCallBack
    window.phoneBackBtnCallBack = this.pageBack
    this.rejectInfo = {
      flag: false,
      slideDown: true,
      dataList: []
    }
    this.pageStatus = 0 // 视频页面 0:准备页面 1：排队中 2:视频见证通过 3:视频失败
    this.rejectReason = ''
    this.rejectStatus = 0 // 视频失败状态 1：视频见证未通过 2：身份证不合规 3:身份证正面 4：身份证反面 5：公安校验不通过
    this.rejectTpye = '' // 视频失败状态 1：视频见证未通过 2：身份证不合规 3:身份证正面 4：身份证反面 5：公安校验不通过
    this.anychatH5AppId = ''
    this.ocrUserInfo = $h.getSession('ocrUserInfo') || {}
    this.$store.commit('updateBusinessNextBtnStatus', false) // 隐藏下一步按钮
    // 查询视频biz_type数据字典
    queryDictionary({ type: this.category, value: this.businessCode }, data => {
      this.bizType = data.key
    })
  },
  methods: {
    // 视频结束后原生回调H5方法
    videoCallBack (msg) {
      console.log(msg)
      let videoFlag
      if (this.fromTHSGB) {
        if (msg == 'ths:10001' || msg == 'ths:10002' || msg == 'ths:10003') {
          _hvueToast({ mes: '视频见证意外中断，请重新开始' })
          this.cancelQueueCB()
          return
        } else {
          videoFlag = JSON.parse(msg)
          this.cancelQueueCB()
        }
      } else {
        msg['videoFlag'] = decodeURI(msg['videoFlag'])
        // 兼容iOS，主动挂断时会进回调返回20000，不做驳回展示的处理
        if (msg['videoFlag'] === '20000') {
          return
        }
        videoFlag = JSON.parse(msg['videoFlag'])
      }

      let msgNo = videoFlag.msgNo
      let msgInfo = videoFlag.msgInfo

      if (msgNo == '0') {
        this.pageStatus = 2;
        setTimeout(() => {
          this.nextStep();
        }, 500)
      } else {
        this.pageStatus = 3
        this.rejectStatus = 1
        this.rejectTpye = '视频认证未通过'
        // 身份正面不合规 ：idcardFace
        // 身份证反面不合规：idcardBack
        // 身份证不合规：idcard
        // 视频驳回：witness
        let rejectContent = []
        for (let i = 0; i < msgInfo.length; i++) {
          let el = msgInfo[i]
          rejectContent.push(el.rejectContent)
          let step = el.step
          switch (step) {
            case 'idcardFace':
              this.rejectTpye = '肖像面认证未通过'
              this.rejectStatus = 3
              break
            case 'idcardBack':
              this.rejectTpye = '国徽面认证未通过'
              this.rejectStatus = 4
              break
            case 'idcard':
              this.rejectTpye = '身份证认证未通过'
              this.rejectStatus = 2
              break
            case 'witness':
              this.rejectTpye = '视频认证未通过'
              this.rejectStatus = 1
              break
            default:
              this.rejectStatus = 1
              this.rejectTpye = '视频认证未通过'
              break
          }
        }
        this.rejectReason = rejectContent.join(';')
      }
    },
    setIdInRedis () {
      let param = {
        userId: $h.getSession('ygtUserInfo', {decrypt: false}).userId,
        serivalId: this.$parent.publicParam.serivalId,
        businessCode: this.businessCode
      }
      setSidInRedis(param)
        .then(data => {
          if (data.error_no === '0') {
            this.startVideo()
          } else {
            return Promise.reject(new Error(data.error_info))
          }
        })
        .catch(e => {
          _hvueToast({ mes: e.message })
        })
    },
    startVideo () {
      if (this.fromTHSGB) {
        this.showThsgb = true
        return
      }
      let url = process.env.NODE_ENV == 'development' ? SERVER_URL.YGT_NEW_SERVER : window.location.protocol + '//' + window.location.host + SERVER_URL.YGT_NEW_SERVER
      let param = {
        userId: $h.getSession('ygtUserInfo', {decrypt: false}).userId, // 用户编号
        userName: $h.getSession('ygtUserInfo', {decrypt: false}).name, // 用户名称
        orgId: $h.getSession('ygtUserInfo', {decrypt: false}).branchNo, // 营业部编号
        bizType: this.bizType, // 业务类型
        netWorkStatus: 'WIFI', // 网络状态 (可不填
        url: url + '?', // 连接排队BUS的服务器地址 (ios必须要在url加上?不然无法识别
        moduleName: 'open', // 必须为open
        funcNo: '60005', // 双向视频见证
        // isNewView: "0",
        version: '4.0',
        appId: this.anychatH5AppId,
        userType: '1',
        // 兼容原生和统一视频版本
        isRejectToH5: '1', // 是否返回所有驳回参数
        user_id: $h.getSession('ygtUserInfo', {decrypt: false}).userId,
        user_name: $h.getSession('ygtUserInfo', {decrypt: false}).name,
        branch_id: $h.getSession('ygtUserInfo', {decrypt: false}).branchNo,
        origin: $hvue.platform,
        user_type: '1',
        // jsession_id:$h.getSession("ygtUserInfo").jsessionid,
        videoType: $hvue.config.videoType, // 0:tchat  1:anychat
        business_id: this.$parent.publicParam.serivalId,
        business_code: this.businessCode,
        business_type: this.bizType,
        biz_type: this.bizType,
        client_id: $h.getSession('ygtUserInfo', {decrypt: false}).userId,
        // 兼容大同多站点的传参
        ext_param: JSON.stringify({'serviceTpye': 'service_type_' + $hvue.config.serviceType}),
        videoServerIP: $hvue.config.serviceTypeUrl.split(':')[0], // String	视频服务器ip	N	4.0视频用；h5传了该参数视频ip就用h5传的，不再取排队bus的ip了
        videoServerPort: $hvue.config.serviceTypeUrl.split(':')[1]	// String	视频服务器端口	N	4.0视频用；h5传了该参数视频端口就用h5传的，不再取排队bus的端口了
      }
      console.log(param)
      let result = $h.callMessageNative(param)
      if (result.error_no != 0) {
        _hvueToast({ mes: result.error_info })
      }
    },
    doCheck () {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        // 可以下一步
        resolve()
      })
    },
    /**
     * 请求参数打包
     */
    reqParamPackage () {
      let params = {} // 提交需要的参数
      return params
    },
    putFormData () {
      let formData = {} // 需要保存的表单数据
      return formData
    },
    rejectVideoQuery () {
      let flowReject = this.$parent.flow.currentStepInfo.flow_reject
      if (flowReject === '1') {
        this.$parent.rejectFlow('uploadIdCard,videoPage')
      } else {
        // 驳回后回滚到上传身份证步骤
        this.$parent.rollback('uploadIdCard')
      }
    },
    // 调用父组件的下一步事件
    nextStep () {
      this.$parent.emitNextEvent()
    },
    // 返回
    pageBack () {
      _hvueConfirm({
        mes: '视频认证未完成，是否返回首页？',
        opts: [
          {
            txt: '取消',
            color: false
          },
          {
            txt: '确定',
            color: true,
            callback: () => {
              // 返回首页
              this.$router.push({ name: 'index', params: {} })
            }
          }
        ]
      })
    },
    // 取消排队的回调
    cancelQueueCB () {
      this.showThsgb = false
    }
  },
  deactivated () {
    window.phoneBackBtnCallBack = this.$parent.back// 调用父类返回
  }
}
</script>
