<template>
  <div>
    <headComponent headerTitle="外部证明材料"></headComponent>
    <article class="content">
          <div class="assets_rzbox">
              <h5 class="title">收入证明</h5>
              <p>请提供您近3年年均收入达到40万或近1年收入达到 120万的证明材料，提供以下任意一种材料：</p>
              <dl class="info">
                  <dd>1.单位收入证明（加盖公章）+银行交易明细（加盖 银行业务公章/电子章）</dd>
                  <dd>2.单位收入证明（加盖公章）+个人所得税纳税记录 (有税务凭证号和验证码）</dd>
              </dl>
          </div>
          <div class="file_info_module">
              <h5 class="title">图片</h5>
              <ul class="file_upload_list">
                  <li>
                      <div class="pic"><img src="../../assets/images/sl_img.png"></div>
                      <a class="delete" href="javascript:void(0)"></a>
                  </li>
                  <li>
                      <div class="pic"><img src="../../assets/images/sl_img.png"></div>
                      <a class="delete" href="javascript:void(0)"></a>
                  </li>
                  <li @click="selImgClick('')">
                      <a class="add_btn" href="javascript:void(0)"></a>
                  </li>
              </ul>
              <p class="tips">注：资产证明图片仅用于 XX 证券理财交易，最多可以上传 9 张图片</p>
          </div>
          <div class="file_info_module">
              <h5 class="title">材料示例图</h5>
              <ul class="file_example_list">
                  <li :key="index" v-for="(item, index) in exampleArr">
                      <div class="pic"><img :src="item.imgSrc"></div>
                  </li>
              </ul>
          </div>
      </article>
    <getImgBoxBrowser
        ref="getImgBoxBrowser"
        @getImgCallBack="getImgCallBack"
    ></getImgBoxBrowser>
  </div>
</template>
<script>
import headComponent from '@/components/headComponent'
import getImgBoxBrowser from '@/components/getImg_browser';
export default {
  components: {
    headComponent,
    getImgBoxBrowser,
  },
  props: ['pageParam'],
  data () {
    return {
      imgArr: [
        {
          imgSrc: '../../assets/images/sl_img.png',
        },
        {
          imgSrc: '../../assets/images/sl_img.png',
        },
        {
          imgSrc: '../../assets/images/sl_img.png',
        },
      ], // 图片列表
      exampleArr: [
        {
          imgSrc: '../../../static/images/sl_img.png',
        },
        {
          imgSrc: '../../../static/images/sl_img.png',
        },
        {
          imgSrc: '../../../static/images/sl_img.png',
        },
        {
          imgSrc: '../../../static/images/sl_img.png',
        },
        {
          imgSrc: '../../../static/images/sl_img.png',
        },
      ], // 示例列表
      nextFlag: false,
      ygtUserInfo: $h.getSession('ygtUserInfo', { decrypt: false }),
    };
  },
  created () {
    window.imgCallBack = this.getImgCallBack;
  },
  activated () {

  },
  methods: {
    doCheck () {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        if (this.nextFlag || process.env.NODE_ENV == 'development') {
          // 可以下一步
          resolve();
        } else {
          // 返回业务办理首页;
          this.$router.push({ name: 'index' });
        }
      });
    },
    selImgClick (imgType) {
      this.imgType = imgType;
      this.$refs.getImgBoxBrowser.getImg();
    },
    getImgCallBack (imgInfo) {
      if (this.platform === 'thinkive') {
        // app端 已由原生上传图片
        this.echoOrcInfo(imgInfo.base64, imgInfo.ocrInfo);
      } else {
        let param = {
          user_id: this.ygtUserInfo.userId,
          file_type: 'image',
          file_name: this.ygtUserInfo.userId + '.jpg',
          image_type: this.imgType,
          file_data: encodeURIComponent(imgInfo.base64),
        };
        uploadImg(param)
            .then(data => {
              this.echoOrcInfo(imgInfo.base64, Object.assign(data.results[0], imgInfo.ocrInfo));
            })
            .catch(e => {
              _hvueToast({ mes: e });
            });
      }
    },
    /**
     * 请求参数打包
     */
    reqParamPackage () {
      // 业务请求参数封装
      let params = {
        param1: '',
        param2: '',
      };
      return params;
    },
    putFormData () {
      let formData = {};
      return formData;
    },
  },
};
</script>
