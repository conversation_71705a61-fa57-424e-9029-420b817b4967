<!-- 协议列表组件2- 单独的协议列表，带必读属性 -->
<template>
  <div>
    <div class="agree_main">
      <ul class="rule_list">
        <li :class="item.isRead ? 'read' :'unread'" v-for="(item,index) in currentXyList" :key="index">
          <a @click.prevent="toDetail(item.agreementId,item.agreeName,index)">《{{item.agreeName}}》</a>
        </li>
      </ul>
    </div>
    <p class="bottom_tips">每份协议都需要您点开阅读，全部阅读完成才能进行下一步的流程。{{bottom_tips}}</p>
    <div class="bottom_check">
      <div class="rule_check">
        <span class="icon_check" :class="{checked:isChecked}" @click.stop="readProtocolCheck"></span>
        <label>已阅读并知晓以上协议</label>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: ['pageParam'],
  data () {
    return {
      isChecked: false,
      agreementIds: [],
      bottom_tips: '',
      tips: '',
      sdxParam: {},
      currentXyList: [], // 当前协议列表
    }
  },
  activated () {
    if (this.pageParam.length < 1) {
      _hvueAlert({ mes: '未查询到协议', title: '提示' })
    }
    this.currentXyList = this.pageParam;
    this.agreementIds = [];
    let hasRead = $h.getSession('agreementIds') || ''

      // 两网退市有股转账户不显示综合申请表
      if (this.$route.query.type == 'lwjts' && $h.getSession('hasGzzhFlag')){
        this.currentXyList = this.currentXyList.filter(item => {
            return item.agreeFno !== 'lwjts_zhsqb'
        })
      }

    for (let index = 0; index < this.currentXyList.length; index++) {
      const element = this.currentXyList[index]
      if (hasRead.includes(element.agreementId)) {
        element.isRead = true
      } else {
        element.isRead = false
      }
      this.$set(this.currentXyList, index, element)
      this.agreementIds.push(element.agreementId)
    }
    if (this.$route.query.type == 'lryykh') {
      this.tips =
        '本人已阅读该风险揭示书及特别提示，已了解融资融券业务相关风险'
    }
    if (this.$route.query.type == 'cybkt' && $h.getSession('isOpenCredit') == 1) {
      this.bottom_tips =
        '如您涉及阅读完成《融资融券业务创业板开通风险揭示书》，点选下方“已阅读并知晓以上协议”选择框，则表示您已同意该揭示书内“确认开通融资融券业务信用账户创业板交易权限”的表述内容符合您本人的真实意愿。'
    }

    this.$bus.on('isReadAgreement', agreementIndex => {
      this.isRead(agreementIndex)
    })
  },
  methods: {
    toDetail (agreementId, agreementName, index) {
        if (this.$route.query.type === 'bjstzz' || this.$route.query.type === 'xsbkt') {
            let bjsSignKeys = $h.getSession('bjsSignKeys') || {};
            this.sdxParam.ymt = bjsSignKeys.ymt || '';
            this.sdxParam.stockAccount = bjsSignKeys.stockAccount || '';
        } else if (this.$route.query.type === 'glgxqr') {
          var glgxSignKeys = $h.getSession('glgxSignKeys') || ''
          glgxSignKeys = glgxSignKeys ? JSON.parse(glgxSignKeys) : []
          this.sdxParam = {
            ymt: '',
            stkbd1: '',
            stkbd2: '',
            stkbd3: '',
            stkbd4: '',
            account1: '',
            account2: '',
            account3: '',
            account4: ''
          }
          glgxSignKeys.forEach((item, i) => {
            this.sdxParam['stkbd' + (i + 1)] = item.marketName
            this.sdxParam['account' + (i + 1)] = item.stockAccount
            this.sdxParam.ymt = item.acodeAccount
          })
        }else if(this.$route.query.type === 'tzakt'){
          if(this.$parent.keywords.length){
            this.sdxParam = {
              stockAccount: this.$parent.keywords[0][0].stockAccount,
              ymt: this.$parent.keywords[0][0].ymt,
            }
          }

        }
      // 查看协议详情
      this.$router.push({
        name: 'agreementDetail',
        query: {
          agreementId: agreementId,
          agreementTitle: agreementName,
          tips: this.tips,
          agreementIndex: index,
          type: this.$route.query.type,
          keyWords: JSON.stringify(this.sdxParam)
        }
      })
    },
    isRead (agreementIndex) {
      this.$set(this.currentXyList[agreementIndex], 'isRead', true)
    },
    readProtocolCheck () {
      for (let index = 0; index < this.currentXyList.length; index++) {
        const element = this.currentXyList[index]
        if (!element.isRead) {
          this.isChecked = false
          _hvueToast({ mes: '请先阅读完所有协议' })
          return
        }
      }
      this.isChecked = !this.isChecked
    },
    checkSubmit () {
      for (let index = 0; index < this.currentXyList.length; index++) {
        const element = this.currentXyList[index]
        if (!element.isRead) {
          this.isChecked = false
          _hvueAlert({ title: '温馨提示', mes: '请先阅读完所有协议' })
          return
        }
      }
      if (!this.isChecked) {
        _hvueAlert({ title: '温馨提示', mes: '请先勾选同意按钮' })
        return false
      }
      return true
    },
    doCheck () {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        if (this.checkSubmit()) {
          // 可以下一步
          resolve()
        }
      })
    },
    /**
     * 请求参数打包
     */
    reqParamPackage () {
      // 业务请求参数封装
      let params = {
        agreementId: this.agreementIds.join(','),
        keyWords: JSON.stringify(this.sdxParam),
      }
      return params
    },
    putFormData () {
      let formData = {
        signProtocol: { agreementId: this.agreementIds.join(',') }
      }
      return formData
    },
    deactivated () {
    }
  },
  destroyed () {
    this.$bus.off('isReadAgreement')
    $h.clearSession('isOpenCredit')
  }
}
</script>
