<template>
  <div>
    <article class="content" v-show="!isShow">
      <div class="assets_rzbox" v-html="fillContent.content">
      </div>
      <div class="file_info_module">
        <h5 class="title">图片</h5>
        <ul class="file_upload_list">
          <li :key="index" v-for="(item, index) in imgArr">
            <div class="pic"><img :src="item.imgSrc"></div>
            <a class="delete" href="javascript:void(0)" @click.prevent="deleteImg(index)"></a>
          </li>
          <li @click="selImgClick('')" v-show="imgArr.length < pictrueAmount">
            <a class="add_btn" href="javascript:void(0)"></a>
          </li>
        </ul>
        <p class="tips">注：{{fillContent.name}}图片仅用于大同证券理财交易，最多可以上传 {{ pictrueAmount }} 张图片</p>
      </div>
      <div class="file_info_module">
        <h5 class="title">材料示例图</h5>
        <ul class="file_example_list">
          <li :key="index" v-for="(item, index) in exampleArr" >
            <div class="pic"><img :src="item.imgSrc" @click.prevent="showImg(item)"></div>
          </li>
        </ul>
      </div>
    </article>
    <getImgBoxBrowser
        ref="getImgBoxBrowser"
        @getImgCallBack="getImgCallBack"
    ></getImgBoxBrowser>
    <show-image :is-show="isShow" :image-src="activeImgSrc" :count="activeImgIdx" :length="exampleArr.length" @change="isShowImage"></show-image>
  </div>
</template>
<script>
import getImgBoxBrowser from '@/components/getImg_browser';
import { uploadImg, pictureAmount } from '@/service/comServiceNew'
import showImage from '../common/showImage'
export default {
  components: {
    getImgBoxBrowser,
    showImage,
  },
  props: ['pageParam'],
  data () {
    return {
      imgArr: [], // 图片列表
      exampleArr: [
        {
          id: 1,
          imgSrc: (process.env.NODE_ENV == 'development' ? '' : ROUTER_BASE) +
              '/static/images' + '/jingyan.png',
        },
      ], // 示例列表
      serivalId: this.$parent.publicParam.serivalId,
      flow_current_step_name: this.$parent.flow.currentStepInfo.flow_current_step_name,
      fillContent: {
        name: '经验证明',
        mediaCode: '233',
        content:
            `<h5 class="title">投资经验证明</h5>
            <p>请提供以下任意一种材料即可：</p>
            <dl class="info">
              <dd>1.交易日期在2年以上的场内投资相关材料</dd>
              <dd>2.购买日期在2年以上的其他金融产品，包括但不限于银行、基金、信托等的购买合同或者系统内查询截图，合同需有盖章页</dd>
            </dl>`,
      },
      isShow: false, // 是否展示图片详情
      activeImgIdx: -1, // 当前图片序号
      activeImgSrc: '', // 当前图片地址
      ygtUserInfo: $h.getSession('ygtUserInfo', { decrypt: false }),
      pictrueAmount: 9
    };
  },
  created () {
    window.imgCallBack = this.getImgCallBack;
    this.pictureAmounts()
  },
  activated () {
    if (this.pageParam[0] && this.pageParam[0].zytzzrdOpenType) {
      const profType = this.pageParam[0].zytzzrdOpenType === '1' ? 'B' : 'C'
      this.fillContent.content = `<h5 class="title">投资经验证明</h5>
        <p>申请${profType}类专业投资者要求证券交易经验满${profType === 'B' ? '730' : '365'}天</p>
        <dl class="info">
          <dd>1.中登交易数据：券商柜台查询并出具客户首次股票交易时间证明，并加盖柜台章</dd>
          <dd>2.购买的银行理财、资管产品、私募、信托计划、具有投资性质的保险产品、基金份额、期货权益的证明；出具销售机构加盖印章的产品购买时间证明</dd>
        </dl>`
    }
  },
  methods: {
    doCheck () {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        if (this.imgArr.length) {
            // 可以下一步
            resolve();
        } else {
          _hvueToast({ mes: '请上传证明' })
        }
      });
    },
    selImgClick (imgType) {
      this.imgType = imgType;
      this.$refs.getImgBoxBrowser.getImg();
    },
    getImgCallBack (imgInfo) {
      // h5本地上传base64到bus
      imgInfo.base64 = this.filterBase64Pre(
          imgInfo.base64
      )
      let param = {
        mediaCode: this.fillContent.mediaCode,
        isOcr: '0',
        file_data: encodeURIComponent(imgInfo.base64),
        serivalId: this.serivalId,
        businessCode: this.$route.query.type,
        flow_current_step: this.flow_current_step_name,
      }
      uploadImg(param)
          .then((data) => {
            if (data.error_no === '0') {
              let uploadInfo = data.uploadInfo[0];
              let obj = {
                imgSrc: 'data:image/jpeg;base64,' + imgInfo.base64,
                uploadInfo: uploadInfo,
              }
              this.imgArr.push(obj);
            } else {
              return Promise.reject(new Error(data.error_info))
            }
          })
          .catch((e) => {
            _hvueToast({ mes: e.message })
          })
    },
    // 删除图片
    deleteImg (i) {
      this.imgArr.splice(i, 1);
    },
    // 展示案例图
    showImg (item) {
      this.activeImgIdx = item.id;
      this.activeImgSrc = item.imgSrc;
      this.isShow = true;
    },
    isShowImage (isShow) {
      this.isShow = isShow;
    },
    filterBase64Pre (ndata) {
      let arr = ndata.split('base64,')
      return arr[arr.length - 1]
    },
    pictureAmounts () {
      let _this = this
      let param = {
        enumNo: 'picture_amount'
      }
      pictureAmount(param)
          .then((data) => {
            if (data.error_no === '0') {
              let result = data.DataSet[0] || {}
              $h.setSession('pictrueAmount', result.itemValue)
              _this.pictrueAmount = result.itemValue
            } else {
              return Promise.reject(new Error(data.error_info))
            }
          })
          .catch((e) => {
            _hvueToast({ mes: e.message })
          })
    },
    /**
     * 请求参数打包
     */
    reqParamPackage () {
      // 业务请求参数封装
      let params = {};
      return params;
    },
    putFormData () {
      let uploadInfo = this.imgArr.map(item => item.uploadInfo);
      let formData = {
        uploadExperience: uploadInfo,
      };
      return formData;
    },
  },
};
</script>
