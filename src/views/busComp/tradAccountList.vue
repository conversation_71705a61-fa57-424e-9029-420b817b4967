<!-- 加挂股东户-账户查询 -->
<template>
  <div>
    <h5 class="com_title">股东户加挂，需要满足以下任一条件</h5>
    <div v-show="szaAccountList.length>0" class="cond_box">
      <ul>
        <li
          :class="{ok:openAccountFlag!='0',error:openAccountFlag=='0'}"
          v-if="shaAccountList.length>0"
        >
          <div class="tit">
            <p>存在账户状态正常的的深A账户</p>
          </div>
          <div class="cont">
            <p v-for="(item,index) in shaAccountList" :key="index">
              <span>{{item.stkbdName}} {{item.stockAccount}}</span>
              <em
                :class="item.holderStatus|filAccountClass(item.regFlag,item.openStatus)"
              >{{item.holderStatus|filAccountState(item.regFlag,item.openStatus)}}</em>
            </p>
          </div>
        </li>
        <li
          :class="{ok:openAccountFlag!='0',error:openAccountFlag=='0'}"
          v-if="szaAccountList.length>0"
        >
          <div class="tit">
            <p>存在账户状态正常的的沪A账户</p>
          </div>
          <div class="cont">
            <p v-for="(item,index) in szaAccountList" :key="index">
              <span>{{item.stkbdName}} {{item.stockAccount}}</span>
              <em
                :class="item.holderStatus|filAccountClass(item.regFlag,item.openStatus)"
              >{{item.holderStatus|filAccountState(item.regFlag,item.openStatus)}}</em>
            </p>
          </div>
        </li>
      </ul>
    </div>
    <div v-show="!nextFlag" class="cond_tips">
      <p>您没有可以加挂的账户，无法办理加挂业务</p>
    </div>
  </div>
</template>

<script>
export default {
  props: ['pageParam'],
  data () {
    return {
      szaAccountList: [],
      shaAccountList: [],
      nextFlag: false
    }
  },
  activated () {
    let list = this.pageParam
    this.szaAccountList = []
    this.shaAccountList = []
    for (let i = 0; i < list.length; i++) {
      let el = list[i]
      if (el.stkbd === '00') {
        this.szaAccountList.push(el)
      } else if (el.stkbd === '10') {
        this.shaAccountList.push(el)
      }
    }
    if (this.szaAccountList.length > 2 && this.shaAccountList.length > 2) {
      this.$store.commit('updateNextBtnText', '返回首页')
      this.nextFlag = false
    } else {
      this.$store.commit('updateNextBtnText', '下一步')
      this.nextFlag = true
    }
  },
  methods: {
    getAccountStyle (holderStatus) {
      if (holderStatus == '0') {
        return 'normal_span'
      } else {
        return 'assign_span'
      }
    },
    doCheck () {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        if (this.nextFlag) {
          // 可以下一步
          resolve()
        } else {
          // 返回业务办理首页;
          this.$router.push({ name: 'index' })
        }
      })
    },
    /**
     * 请求参数打包
     */
    reqParamPackage () {
      // 业务请求参数封装
      let params = {}
      return params
    },
    putFormData () {
      let formData = {}
      return formData
    }
  }
}
</script>
