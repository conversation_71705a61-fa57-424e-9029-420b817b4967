<template>
  <div>
      <div class="ste_cont">
          <ul>
              <li class="spel">
                  <span class="tit">转托管费用</span>
                  <div class="cont">
                      <span>￥10.00</span>
                  </div>
              </li>
              <li class="spel">
                  <span class="tit">扣费账户</span>
                  <div class="cont">
                      <span>{{ygtUserInfo.fundAccount}}</span>
                  </div>
              </li>
          </ul>
      </div>
      <div class="pword_box">
      <div class="input_form mt10">
        <div class="ui field text pword">
          <label class="ui label">资金密码</label>
          <input
            v-model.trim="pwdInputModel.newPwd.value"
            :maxlength="pwdInputModel.newPwd.maxlength"
            :type="pwdType"
            class="ui input"
            placeholder="请输入资金密码"
          />
          <a
            class="txt_close"
            @click.stop="pwdInputModel.newPwd.value=''"
            v-show="pwdInputModel.newPwd.value!=''"
          ></a>
          <a class="icon_eye" :class="{show: pwdShow}" @click.stop="pwdShow = !pwdShow"></a>
        </div>
      </div>
<!--      <div class="ce_btn mt20">-->
<!--        <a v-throttle class="ui button block rounded" @click.stop="verifyPwdInput">确认修改</a>-->
<!--      </div>-->
    </div>
  </div>
</template>

<script>
import headComponent from '@/components/headComponent' // 头部
import { checkInput } from '@/common/util'
import ztgInfoConfirm from '../../components/ztgInfoConfirm';
export default {
  props: ['pageParam'],
    components: {
        headComponent,
    },
  data () {
    return {
      ygtUserInfo: $h.getSession('ygtUserInfo', {decrypt: false}),
      loginType: $h.getSession('loginType') || '1', // 账号类型 1普通账号 2信用账号
      pwdShow: false, // 密码输入框是否明文展示
      pwdTypeVal: '', // 密码类型中文描述
      pwdInputModel: {
        newPwd: {
          name: '密码',
          value: '',
          minlength: '6',
          maxlength: '6',
          format: 'pwd',
        },
      },
    }
  },
  computed: {
    pwdType () {
      return this.pwdShow ? 'tel' : 'password'
    },
  },
  activated () {
    // this.$store.commit('updateBusinessNextBtnStatus', false) // 隐藏下一步按钮
    // 清空输入框
    for (const key in this.pwdInputModel) {
      if (this.pwdInputModel.hasOwnProperty(key)) {
        const element = this.pwdInputModel[key]
        element.value = ''
      }
    }
  },
  methods: {
    verifyPwdInput () {
      let flag = checkInput(this.pwdInputModel.newPwd)
      if (flag != '') {
          return false;
      }
      return true;
    },
    doCheck () {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
          if (this.verifyPwdInput()) {
              resolve()
          }
      })
    },
    /**
     * 请求参数打包
     */
    reqParamPackage () {
      // 业务请求参数封装
      let params = {} // 提交需要的参数
      return params
    },
    putFormData () {
      let formData = {} // 需要保存的表单数据
      return formData
    },
  },
}
</script>
