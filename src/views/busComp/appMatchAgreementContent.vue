<template>
  <div>
    <!-- 新三板暂时性需求：不匹配时不允许线上办理，提示到营业部办理 -->
    <p
      v-if="sdxParam.matchingFlag == '1' && businessCode =='xsbkt'"
      class="bot_tips"
    >您的适当性不匹配，请临柜办理。</p>

    <div v-else class="rule_check">
      <span class="icon_check" :class="{checked:isChecked}" @click.stop="readProtocolCheck"></span>
      <label>
        我已阅读并同意
        <a
          href="javascript:void(0)"
          @click.stop="toDetail(item.agreementId,item.agreeName,index)"
          v-for="(item,index) in pageParam"
          :key="index"
        >《{{item.agreeName}}》</a>
      </label>
    </div>

    <div class="ce_btn">
      <!--  最低风险等级、强匹配时不匹配、都不展示确认按钮  -->
      <a
        v-if="(sdxParam.isStrongMatch=='0' || sdxParam.matchingFlag == '0' || sdxParam.minRankFlag!='0') && !hideNextBtn"
        class="ui button block rounded"
        v-throttle
        @click.stop="nextStep"
      >确定</a>
      <a v-throttle class="ui button block rounded border mt10" @click.stop="toIndex">放弃办理</a>
    </div>
  </div>
</template>
<script>
export default {
  props: ['pageParam'],
  data () {
    return {
      isChecked: false,
      agreementIds: [],
      sdxParam: {},
      businessCode: this.$route.query.type,
      hideNextBtn: false,
    }
  },
  created () {
    this.$bus.on('sdxParam', param => {
      this.sdxParam = param
      // 新三板暂时性需求：不匹配时不允许线上办理，提示到营业部办理
      if (this.sdxParam.matchingFlag == '1' && this.businessCode == 'xsbkt') {
        this.hideNextBtn = true
      }
      // 最低风险等级、强匹配时不匹配、都不展示确认按钮
      // isStrongMatch 是不是强制匹配  1-强匹配  0-否
      // matchingFlag  适当性是否匹配  1-不匹配 0-匹配
      // minRankFlag 1是最低风险登记
      if (this.sdxParam.matchingFlag == '1' && this.sdxParam.isStrongMatch == '1') {
        this.hideNextBtn = true
      }
      if (this.sdxParam.minRankFlag == '1') {
        this.hideNextBtn = true
      }

      if (this.businessCode === 'bjstzz' || this.businessCode === 'xsbkt') {
          let bjsSignKeys = $h.getSession('bjsSignKeys') || {};
          this.sdxParam.ymt = bjsSignKeys.ymt || '';
          this.sdxParam.stockAccount = bjsSignKeys.stockAccount || '';
      }
    })
    this.$bus.on('isReadAgreement', agreementIndex => {
      this.isRead(agreementIndex)
    })
    let hasRead = $h.getSession('agreementIds') || ''
    for (let index = 0; index < this.pageParam.length; index++) {
      const element = this.pageParam[index]
      // 是否已阅读，可转债权限开通以外的业务默认为已阅读
      if (hasRead.includes(element.agreementId) && this.businessCode !== 'kzzkt') {
        element.isRead = true
      } else {
        element.isRead = false
      }
      this.$set(this.pageParam, index, element)
      this.agreementIds.push(element.agreementId)
    }
  },
  methods: {
    toDetail (agreementId, agreementName, index) {
      // 查看协议详情
      this.$router.push({
        name: 'agreementDetail',
        query: {
            agreementId: agreementId,
            agreementTitle: agreementName,
            agreementIndex: index,
            keyWords: JSON.stringify(this.sdxParam)
        }
      })
    },
    isRead (agreementIndex) {
      this.$set(this.pageParam[agreementIndex], 'isRead', true)
    },
    readProtocolCheck () {
      for (let index = 0; index < this.pageParam.length; index++) {
        const element = this.pageParam[index]
        if (!element.isRead) {
          this.isChecked = false
          _hvueToast({ mes: '请先阅读完所有协议' })
          return
        }
      }
      this.isChecked = !this.isChecked
    },
    checkSubmit () {
      if (!this.isChecked) {
        _hvueAlert({ title: '温馨提示', mes: '请先勾选同意按钮' })
        return false
      }
      return true
    },
    doCheck () {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        if (this.checkSubmit()) {
          // 可以下一步
          resolve()
        }
      })
    },
    /**
     * 请求参数打包
     */
    reqParamPackage () {
      for (let index = 0; index < this.pageParam.length; index++) {
        const element = this.pageParam[index]
        this.agreementIds.push(element.agreementId)
      }

      // 业务请求参数封装
      let params = {
        agreementId: this.agreementIds.join(','),
        keyWords: JSON.stringify(this.sdxParam),
      }
      return params
    },
    putFormData () {
      let formData = {
        signProtocol: { agreementId: this.agreementIds.join(',') }
      }
      return formData
    },
    toIndex () {
      this.$router.push({ name: 'index', params: {} })
    },
    // 调用父组件的下一步事件
    nextStep () {
      this.$parent.emitNextEvent()
    }
  },
  destroyed () {
    this.$bus.off('sdxParam')
    this.$bus.off('isReadAgreement')
  }
}
</script>
