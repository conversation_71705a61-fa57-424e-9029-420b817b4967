/**
 * @Desc: 单纯的提交表单数据，将业务准入步骤init获取的表单数据再次提交到后端时用
 * @Author: Way
 * @Date: 2020-05-13 11:21:43
 * @Last Modified by: Way
 * @Last Modified time: 2020-05-15 15:38:54
 */

<template>
  <div></div>
</template>

<script>
export default {
  props: ['pageParam'],

  methods: {
    doCheck () {
      // 执行下一步
      return new Promise((resolve, reject) => {
        resolve()
      })
    },
    /**
     * 请求参数打包
     */
    reqParamPackage () {
      // 业务请求参数封装
      let params = {}
      return params
    },
    // 表单参数打包
    putFormData () {
      let formData = {}
      // 将初始化返回的原子表单数据提交到后端
      if (this.pageParam[0]) {
        formData.qualityInspectionInfo = this.pageParam[0]
      }
      return formData
    }
  }
}
</script>
