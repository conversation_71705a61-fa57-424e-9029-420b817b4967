<template>
  <div>
    <headComponent headerTitle="固收平台" v-if="showHead"></headComponent>
    <div v-if="showUserInfo">
      <div class="ste_cont">
        <ul>
          <li class="spel">
            <span class="tit">资金账号</span>
            <div class="cont">
              <span>{{ ygtUserInfo.fundAccount }}</span>
            </div>
          </li>
          <li class="spel">
            <span class="tit">客户姓名</span>
            <div class="cont">
              <span>{{ ygtUserInfo.name }}</span>
            </div>
          </li>
          <li>
            <span class="tit">证券账户</span>
            <div
                class="cont"
                @click.stop="boxClick('trdaccount', '证券账户')"
            >
                <span :class="{ default: selData.trdaccount.value === '' }">
                  {{ selData.trdaccount.value || selData.trdaccount.placeholder}}
                </span>
            </div>
            <span class="lk"></span>
          </li>
        </ul>
      </div>
      <div class="ste_cont">
        <ul>
          <li>
            <span class="tit">证券名称</span>
            <div class="cont" @click.stop="boxClick('securityName')">
              <span :class="{ default: selData.securityName.value === '' }">
                {{ selData.securityName.value || selData.securityName.placeholder}}
              </span>
            </div>
            <span class="lk"></span>
          </li>
          <li>
            <span class="tit">证券代码</span>
            <div class="cont" @click.stop="boxClick('securityCode')">
              <span :class="{ default: selData.securityCode.value === '' }">
                {{ selData.securityCode.value || selData.securityCode.placeholder}}
              </span>
            </div>
            <span class="lk"></span>
          </li>
          <li>
            <span class="tit">转让价格</span>
            <div class="cont" @click.stop="boxClick('transferPrice')">
              <span :class="{ default: selData.transferPrice.value === '' }">
                {{ (selData.transferPrice.value && selData.transferPrice.value + ' (元)') || selData.transferPrice.placeholder}}
              </span>
            </div>
            <span class="lk"></span>
          </li>
          <li>
            <span class="tit">转让数量</span>
            <div class="cont" @click.stop="boxClick('transferNumber')">
              <span :class="{ default: selData.transferNumber.value === '' }">
                {{ (selData.transferNumber.value && selData.transferNumber.value + ' (张/股)') || selData.transferNumber.placeholder}}
              </span>
            </div>
            <span class="lk"></span>
          </li>
        </ul>
      </div>
      <div class="ste_cont">
        <ul>
          <li>
            <span class="tit">委托方向</span>
            <div class="cont" @click.stop="boxClick('entrustFlag')">
              <span :class="{ default: selData.entrustFlag.value === '' }">
                {{ selData.entrustFlag.value || selData.entrustFlag.placeholder}}
              </span>
            </div>
            <span class="lk"></span>
          </li>
          <li>
            <span class="tit">交易金额</span>
            <div class="cont" @click.stop="boxClick('transaction')">
              <span :class="{ default: selData.transaction.value === '' }">
                {{ selData.transaction.value || selData.transaction.placeholder}}
              </span>
            </div>
            <span class="lk"></span>
          </li>
        </ul>
      </div>
      <div class="ste_cont">
        <ul>
          <li>
            <span class="tit">约定号</span>
            <div class="cont" @click.stop="boxClick('appoint')">
              <span :class="{ default: selData.appoint.value === '' }">
                {{ selData.appoint.value || selData.appoint.placeholder}}
              </span>
            </div>
            <span class="lk"></span>
          </li>
          <li>
            <span class="tit">对手方交易员</span>
            <div class="cont" @click.stop="boxClick('analogue')">
              <span :class="{ default: selData.analogue.value === '' }">
                {{ selData.analogue.value || selData.analogue.placeholder}}
              </span>
            </div>
            <span class="lk"></span>
          </li>
        </ul>
      </div>
      <div class="ste_cont">
        <div class="padding-left-15">
          <span class="tit">报价方式</span>
        </div>
         <sel-radio-box
             :isShow="true"
             :type="'priceTerms'"
             :category="'ismp.priceTerms'"
             :defaultStr="selData.priceTerms.id"
             :selectAble="selData.priceTerms.selectAble"
             @selRadioCallback="selRadioCallback"
         ></sel-radio-box>
      </div>
      <div class="rule_check" v-if="!showHead">
        <span class="icon_check" :class="{'checked': checked}" @click.s.stop="toggleCheck()"></span>
        <label>本人（机构）确认上述固定收益协议交易完成前后，均符合相关法律法规的要求。以上承诺信息让客户勾选才能提交业务申请。</label>
      </div>
    </div>

    <inputBox
        v-if="inputBox.show"
        v-model="inputBox.show"
        :title="inputBox.title"
        :checkType="inputBox.checkType"
        :maxLength="inputBox.maxLength"
        :value="inputBox.value"
        :placeholder="inputBox.placeholder"
        @selCallback="selCallback"
    ></inputBox>
    <selBox
        v-if="selBox.show"
        v-model="selBox.show"
        :title="selBox.title"
        :defaultStr="selBox.defaultStr"
        :category="selBox.category"
        :initData="selBox.initData"
        @selCallback="selCallback"
    ></selBox>
    <sel-stock-box
        v-if="selStockBox.show"
        v-model="selStockBox.show"
        :title="selStockBox.title"
        :defaultStr="selStockBox.defaultStr"
        :category="selStockBox.category"
        :initData="selStockBox.initData"
        :isMarket="selStockBox.isMarket"
        :idString="'stockAccount'"
        @selCallback="selCallback"
    ></sel-stock-box>

  </div>
</template>

<script>
import headComponent from '@/components/headComponent'

import selBox from '@/components/selBox' // 选择器
import selStockBox from '@/components/selStockBox' // 选择器
import selRadioBox from '@/components/selRadioBox' // tab
import inputBox from '@/components/inputBox' // 输入器
import {
  queryRecordList,
  queryStockList,
} from '@/service/comServiceNew'
import {queryDictionary} from '@/common/util'

export default {
  props: ['pageParam'],
  components: {
    selRadioBox,
    selBox,
    selStockBox,
    inputBox,
    headComponent,
  },
  data() {
    return {
      ygtUserInfo: $h.getSession('ygtUserInfo', {decrypt: false}),
      serivalId: '',
      showHead: false,
      activeKey: 0,
      checked: false,
      submitParam: {},
      selData: {
        market: {
          name: '交易市场',
          value: '请选择交易市场',
          type: 'ismp.market',
          selected: 0,
          id: '',
        },
        trdaccount: {
          name: '证券账户',
          value: '',
          placeholder: '请选择股东账户',
          type: 'trdaccount',
          selected: 0,
          id: '',
        },
        appoint: {
          title: '约定号',
          placeholder: '请输入约定号',
          value: '',
          checkType: 'appoint',
          maxLength: 12,
        },
        analogue: {
          title: '对手方交易员',
          placeholder: '请输入对手方交易员',
          value: '',
          checkType: 'analogue',
          maxLength: 12,
        },
        securityName: {
          title: '证券名称',
          placeholder: '请输入证券名称',
          value: '',
          checkType: 'isCnEnNum',
          maxLength: 12,
        },
        entrustFlag: {
          name: '委托方向',
          value: '',
          placeholder: '请选择买卖方向',
          type: 'ismp.direction',
          selected: 0,
          id: '',
        },
        securityCode: {
          title: '证券代码',
          placeholder: '请输入证券代码',
          value: '',
          checkType: 'securityCode',
          maxLength: 12,
        },
        transferPrice: {
          title: '转让价格',
          placeholder: '请输入价格（元）',
          value: '',
          checkType: 'transferPrice',
          maxLength: 12,
        },
        transferNumber: {
          title: '转让数量',
          placeholder: '请输入数量（张/股）',
          value: '',
          checkType: 'transferNumber',
          maxLength: 12,
        },
        transaction: {
          title: '交易金额',
          placeholder: '请输入交易金额（元）',
          value: '',
          checkType: 'transaction',
          maxLength: 12,
        },
        priceTerms: {
          name: '报价方式',
          value: '请选择报价方式',
          type: 'ismp.priceTerms',
          selected: 0,
          id: '1',
          selectAble: true,
        },
      },
      selBox: {
        show: false,
        title: '',
        category: '',
        defaultStr: '',
        initData: [],
      },
      selStockBox: {
        show: false,
        isMarket: true,
        title: '',
        category: '',
        defaultStr: '',
        initData: [],
      },
      inputBox: {
        show: false,
        title: '',
        placeholder: '',
        value: '',
        checkType: '',
        maxLength: '',
      },
      inputType: ['securityName', 'securityCode', 'transferPrice', 'transferNumber', 'transaction', 'analogue', 'appoint'],
      selectType: ['entrustFlag', 'market'],
      selectStockType: ['trdaccount'],
      selectRadioType: ['priceTerms'],// 报价方式
    };
  },
  created () {
    this.serivalId = this.$route.query.serivalId || $h.getSession('result_serivalId') || '';
    if (this.$route.query.serivalId) {
      this.showHead = true;
    }
  },
  activated () {
    // this.$store.commit("updateBusinessNextBtnStatus", false); // 隐藏下一步按钮
  },
  mounted(){
    window.phoneBackBtnCallBack = this.pageBack
  },
  computed: {
    showUserInfo () {
      if (!this.selBox.show && !this.inputBox.show && !this.selStockBox.show) {
        this.$store.commit('updateBusinessNextBtnStatus', true) // 显示下一步按钮
        return true
      } else {
        return false
      }
    },
  },
  watch: {
    'serivalId': {
      handler (newVal) {
        if (newVal) {
          this.queryRecordList('', newVal);
        }
      },
      immediate: true,
    },
  },
  methods: {
    // 查询数据
    async queryRecordList (queryFlag = '', serivalId = '') {
      if (!serivalId) return;
      let _selData = this.selData;

      let results = await queryRecordList({
        serivalId: serivalId,
        businessCode: this.$route.query.type,
        clientId: $h.getSession('ygtUserInfo', {decrypt: false}).clientId,
      });

      let _results = results.results ? results.results : results.DataSet,
      dzjyList = _results[0].dzjyList;
      dzjyList = dzjyList ? JSON.parse(dzjyList) : {};

      let keys = Object.keys(dzjyList)

      keys.forEach(key => {
        if (this.inputType.includes(key)) {
          Object.assign(_selData[key], {
            value: dzjyList[key],
            selectAble: !this.showHead,
          })
        }
        if (this.selectRadioType.includes(key)) {
          Object.assign(_selData[key], {
            id: dzjyList[key],
            defaultStr: dzjyList[key],
            selectAble: !this.showHead,
          })
        }

        if (this.selectType.includes(key)) {
          if (key === 'market') {
            queryDictionary(
                {type: 'ismp.market', key: dzjyList[key]},
                (d) => {
                  Object.assign(_selData[key], {
                    value: d.value,
                    id: d.key,
                    selected: d.index,
                    selectAble: !this.showHead,
                  })
                },
            )
          } else {
            queryDictionary(
                {type: 'ismp.direction', key: dzjyList[key]},
                (d) => {
                  Object.assign(_selData[key], {
                    value: d.value,
                    id: d.key,
                    selected: d.index,
                    selectAble: !this.showHead,
                  })
                },
            )
          }
        }
      })

      let that = this;
      let res = await queryStockList({
        userId: $h.getSession('ygtUserInfo', {decrypt: false}).userId,
        isQueryZD: '0',
      })
      res = res.results ? res.results : res.DataSet;
      res.map((item, key) => {
        if (item.stockAccount === dzjyList['trdaccount']) {
          Object.assign(_selData['trdaccount'], {
            value: `${item.stkbdName} ${item.stockAccount}`,
            selectAble: !this.showHead,
            selected: key,
            id: item.stockAccount,
          })
          that.selData['market'].id = item.market;
        }
      })
    },
    // 选择单选框
    checkedRadio () {
      this.selData[arguments[0]].value = arguments[1].currentTarget.dataset.value;
    },
    // 打开选择器和输入组件
    boxClick (type, title) {
      if (this.selData[type].selectAble === false) {
        return
      }
      this.$store.commit('updateBusinessNextBtnStatus', false) // 隐藏下一步按钮
      this.writeIndex = type
      // 打开select选择组件
      if (this.selectType.includes(type)) {
        let _type = this.selData[type].type
        //打开选择器
        this.selBox.title = title
        this.selBox.category = _type
        this.selBox.show = true
        this.selBox.defaultStr = this.selData[type].id;
      } else if (this.selectStockType.includes(type)) {
        let _type = this.selData[type].type
        //打开选择器
        this.selStockBox.title = title
        this.selStockBox.category = _type
        this.selStockBox.show = true
        this.selStockBox.defaultStr = this.selData[type].id;
      } else if (this.inputType.includes(type)) { // 打开input输入组件
        // 打开大宗额度，营业部代码，约定号、交易单元、对方交易单、证券名称
        this.inputBox.show = true
        this.inputBox.title = this.selData[type].title
        this.inputBox.placeholder = this.selData[type].placeholder
        this.inputBox.checkType = this.selData[type].checkType
        this.inputBox.value = this.selData[type].value
        this.inputBox.maxLength = this.selData[type].maxLength
        this.inputBox.tips = this.selData[type].tips
      }
    },
    // 选择器和输入器组件回调方法
    selCallback (d) {
      this.$store.commit('updateBusinessNextBtnStatus', true) // 显示下一步按钮
      let type = this.writeIndex
      if (this.selectType.includes(type)) {
        let value = d.data.value;
        // 学历职业返回
        Object.assign(this.selData[this.writeIndex], {
          value: value,
          id: d.data.key,
          selected: d.index,
        })
      } else if (this.selectStockType.includes(type)) {
        let value = `${d.data.stkbdName + ': ' + d.data.stockAccount}`;
        Object.assign(this.selData[this.writeIndex], {
          value: value,
          id: `${d.data.stockAccount}`,
          selected: d.index,
        })
        this.selData['market'].id = d.data.market;
      } else if (this.inputType.includes(type)) {
        // 打开大宗额度，营业部代码，约定号、交易单元、对方交易单、证券名称
        Object.assign(this.selData[this.writeIndex], {
          value: d.value,
        })
      }
    },
    // 单选组件回调
    selRadioCallback (d) {
        let value = d.data.value;
        Object.assign(this.selData[d.type], {
          value: value,
          id: d.data.key,
          selected: d.index,
        })
    },
    // 勾选复选框
    toggleCheck () {
      this.checked = !this.checked;
    },
    // 提交前的校验
    checkSubmit () {
      let keys = Object.keys(this.selData),
          that = this;

      try {
        keys.forEach( key => {
          if (
              that.selData[key].value && this.selData[key].value !== '请选择' ||
              that.selData[key].id
          ){
            if (
                that.selectType.includes(key) ||
                that.selectStockType.includes(key) ||
                that.selectRadioType.includes(key)
            ) {
              that.submitParam[key] = this.selData[key].id;
            } else {
              that.submitParam[key] = this.selData[key].value
            }
          } else {
            throw Error(`${that.selData[key].title || that.selData[key].name}不能为空`)
          }
        })
      } catch (e) {
        let err = e.toString().replace('Error:', '')
        _hvueToast({
          mes: err,
        })
        return false;
      }
      return true;
    },
    doCheck () {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        if (this.checked && this.checkSubmit()) {
          // 可以下一步
          resolve();
        }
      });
    },
    /**
     * 请求参数打包
     */
    reqParamPackage() {
      this.submitParam['userName'] = this.ygtUserInfo.name;
      this.submitParam['clientId'] = this.ygtUserInfo.clientId;
      // 业务请求参数封装
      let params = {
        queryValue: 'gspt',
        keyWords: JSON.stringify(this.submitParam),
      }; // 提交需要的参数
      return params;
    },
    putFormData() {
      let formData = {
        dzjyList: this.submitParam
      }
      return formData;
    },
    pageBack() {
      this.$bus.emit('isActiveTabkey', this.$route.query.key) // 通过vuebus调用
      this.$router.go(-1)
    },
  }
};
</script>
