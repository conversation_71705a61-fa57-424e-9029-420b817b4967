<!-- 结果页组件 -->
<template>
  <div v-if="formStatus != '4'">
    <!--  账户类业务展示的结果列表界面  -->
    <template v-if="busiType == 1">
      <closeAccountResultList
        v-if="busiCode == 'xh'"
        :page-param="pageParam"
      ></closeAccountResultList>
      <div v-else class="result_sfinfo">
        <ul>
          <li v-for="(item, index) in pageParam" v-show="item.isShow" :key="index">
            <span v-if="item.views" class="tit">{{ item.views }}</span>
            <p v-if="item.views">
              <span
                v-if="businessCode == 'zhzh'"
                :class="item.dealBusinessResultState | fillStateStyle"
                class="status"
                @click.stop="copyAccount(item.views, item)"
                >复制并登录</span
              >
              <span v-else :class="item.dealBusinessResultState | fillStateStyle" class="status">{{
                item.dealBusinessResultState | fillStateDesc
              }}</span>
            </p>
            <div v-if="item.dealBusinessResultState == 2" class="fail_reason">
              <span>失败原因</span>
              <p @click.stop="showErrorInfo(item.dealBusinessDesc)">
                {{ item.dealBusinessDesc.substring(0, 8) }}...
              </p>
            </div>
          </li>
        </ul>
        <div v-if="businessCode === 'zhzh'" class="result_tips">
          <span>注：您的账户已通过短信发送至您预留手机号码，请注意查收。</span>
        </div>
      </div>
    </template>
    <!--  非账户类业务，办理失败时展示的失败原因界面  -->
    <template v-else-if="busiType == 2">
      <div v-if="pageParam[0].dealBusinessResultState == 2" class="fail_reason">
        <span>失败原因</span>
        <p @click.stop="showErrorInfo(pageParam[0].dealBusinessDesc)">
          {{ pageParam[0].dealBusinessDesc.substring(0, 8) }}...
        </p>
      </div>
    </template>

    <!-- 佣金表单 -->
    <div class="yj_info_wrap" v-if="this.commissionBcode.includes(businessCode)">
      <h5 class="title">收费标准<i class="icon_exp"></i></h5>
      <table class="yj_infotable" cellpadding="0" cellspacing="0">
        <thead>
          <tr>
            <th width="15%">市场</th>
            <th width="15%">品种</th>
            <th width="32%">费用项目</th>
            <th width="38%">费用标准</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(e, i) in commissionList" :key="i">
            <td>{{ e.market }}</td>
            <td>{{ e.item }}</td>
            <td>{{ e.name }}</td>
            <td>{{ e.brokerage }}</td>
          </tr>
        </tbody>
      </table>
      <p class="cond_tips" v-if="commissionList.length == 0">暂未查询到佣金费率值</p>
    </div>
    <div class="ce_btn mt20">
      <a
        v-if="handleStatus == '2' || formStatus == '4'"
        class="ui button block rounded border mt10"
        v-throttle
        @click.stop="giveup"
        >放弃办理</a
      >
      <a v-throttle v-else class="ui button block rounded" @click.stop="pageBack">返回首页</a>
    </div>
    <!--</article> -->
  </div>
</template>

<script>
import { queryCommission } from '@/service/comServiceNew'
import closeAccountResultList from '@/views/common/closeAccountResultList' // 销户结果账号列表

export default {
  name: 'businessResult',
  components: {
    closeAccountResultList
  },
  props: {
    pageParam: {
      type: Array
    }
  },
  data() {
    return {
      busiType: 1, // 1账户类业务 2其他业务
      busiCode: this.$route.query.type !== 'yyxh' ? this.$route.query.type : 'xh',
      fundType: { '0': '资金账户', '7': '信用资金账户' }, // 找回的账户类型
      businessCode: this.$route.query.type,
      loginType: $h.getSession('loginType') || '1', // 登录账号类型 1普通账号 2信用账号
      commissionList: [], // 佣金列表数据
      commissionBcode: [
        'kzzkt',
        'gszqqxkt',
        'sgtkt',
        'hgtkt',
        'bjstzz',
        'xsbkt',
        'fxjskt',
        'zkgdh',
        'jggdh',
        'cybkt',
        'kcbkt'
      ], // 需要展示佣金率的业务
      markets: { '00': '深市', '10': '沪市', '14': '沪市(信用)', '24': '深市(信用)' }, //市场
      stkbds: []
    }
  },
  methods: {
    pageBack() {
      this.$bus.emit('pageBack') // 通过vuebus调用businessFlowInfo组件pageBack事件
    },
    giveup() {
      // 放弃办理
      this.$bus.emit('giveUp') // 通过vuebus调用businessFlowInfo组件giveUp事件
    },
    showErrorInfo(info) {
      _hvueAlert({
        title: '失败原因',
        mes: info
      })
    },
    copyAccount(account, views) {
      let infos = JSON.parse(views.relFields)
      account = account.match(/\d/g)
      account.length && (account = account.join(''))
      let input = document.createElement('input') // 创建input对象
      input.value = account // 设置复制内容
      document.body.appendChild(input) // 添加临时实例
      input.select() // 选择实例内容
      document.execCommand('Copy') // 执行复制
      document.body.removeChild(input) // 删除临时实例
      _hvueToast({
        mes: `复制成功`,
        callback: () => {
          this.$router.push({
            name: 'login',
            query: { loginType: infos.fundType === '7' ? '2' : '1' }
          })
        }
      })
    }
  },
  activated() {
    if (this.pageParam.length > 0) {
      let a = this.pageParam
      this.stkbds = []
      for (let s = 0; s < a.length; s++) {
        let element = a[s]
        if (element.viewFields === undefined) {
          // 非权限和开通类业务没有账户数据，只在业务办理失败的时候展示一个失败原因
          this.busiType = 2
          if (element.dealBusinessResultState === '2') {
            element.isShow = true
          } else {
            element.isShow = false
          }
        } else if (this.busiCode === 'xh') {
          this.busiType = 1
          element.isShow = true
          let views = JSON.parse(element.viewFields)
          element.views = ''
          for (let i = 0; i < views.length; i++) {
            let v = views[i]
            if (
              v.filed != 'market' &&
              v.filed != 'account' &&
              v.filed != 'exchangeType' &&
              v.filed != 'mainFlag'
            ) {
              continue
            }
            // 是否是数据字典类型
            if (v.isEnumFlag === '1') {
              // 不是数据字典值直接展示value
              if (v.filed === 'exchangeType' && v.value === '9') {
                element.views = '特转A  '
              } else if (v.filed === 'exchangeType') {
                continue
              } else {
                element.views += v.value + ' '
              }
            } else {
              // 是数据字典值展示enumValue
              element.views += v.enumValue + ' '
            }
          }
        } else {
          // 权限和开通类业务有账户数据，需要展示结果列表的
          this.busiType = 1
          element.isShow = true
          let views = JSON.parse(element.viewFields)
          element.views = ''
          for (let i = 0; i < views.length; i++) {
            let v = views[i]
            // 是否是数据字典类型
            if (v.isEnumFlag === '1') {
              // 不是数据字典值直接展示value
              if (v.filed === 'fundType') {
                // 账户账户的类型
                element.views += this.fundType[v.value] + ' '
              } else {
                element.views += v.value + ' '
              }
            } else {
              // 是数据字典值展示enumValue
              element.views += v.enumValue + ' '
              this.stkbds.push(v.value)
            }
          }
        }
        this.$set(this.pageParam, s, element)
      }
    } else {
      _hvueAlert({
        mes: '未查询到办理结果'
      })
    }
    if (this.$parent.businessName === '修改手机号' && this.formStatus !== '3') {
      let ygtUserInfo = $h.getSession('ygtUserInfo', { decrypt: false })
      let newMobile = $h.getSession('newMobile')
      ygtUserInfo.mobile = newMobile
      $h.setSession('ygtUserInfo', ygtUserInfo, { encrypt: false })
      $h.clearSession('newMobile')
    }

    // 增开查询佣金率
    if (this.commissionBcode.includes(this.$route.query.type)) {
      queryCommission({
        userId: $h.getSession('ygtUserInfo', { decrypt: false }).userId,
        businessCode: this.$route.query.type,
        stkbds: this.stkbds.join(',')
      }).then(res => {
        if (res.error_no === '0') {
          let originalArray = res.DataSet
          this.commissionList = originalArray.map(item => {
            item.market = this.markets[item.market]
            return item
          })
        } else {
          _hvueAlert({
            title: '提示',
            mes: res.error_info
          })
        }
      })
    }
  },
  computed: {
    handleStatus() {
      console.log('updateHandleStatus:' + this.$store.state.handleStatus)
      return this.$store.state.handleStatus
    },
    formStatus() {
      console.log('updateFormStatus:' + this.$store.state.formStatus)
      return this.$store.state.formStatus
    }
  }
}
</script>
