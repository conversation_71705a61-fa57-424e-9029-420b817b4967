<template>
  <div>
    <div class="phone_info">
      <p>您当前手机号码</p>
      <div class="tel">{{ygtUserInfo.mobile|formatMobileNo}}</div>
    </div>
    <div class="input_form">
      <div class="ui field text">
        <div class="ui label">新手机号</div>
        <input
          v-model="inputModel.mobile.value"
          :maxlength="inputModel.mobile.maxlength"
          type="text"
          class="ui input"
          placeholder="请输入新手机号"
        />
        <a
          class="txt_close"
          @click.stop="inputModel.mobile.value=''"
          v-show="inputModel.mobile.value!=''"
        ></a>
      </div>
      <div class="ui field text code">
        <div class="ui label">验证码</div>
        <input
          v-model="inputModel.code.value"
          :maxlength="inputModel.code.maxlength"
          type="text"
          class="ui input"
          placeholder="请输入6位短信验证码"
        />
        <a
          class="txt_close"
          @click.stop="inputModel.code.value=''"
          v-show="inputModel.code.value!=''"
        ></a>
        <smsTimer v-model="startFlag" @sendSms="sendMsg"></smsTimer>
      </div>
    </div>
    <div class="ce_btn mt20">
      <a
        v-throttle
        class="ui button block rounded"
        :class="{disable : showDisableClass}"
        @click.stop="submitMobile"
      >立即修改</a>
    </div>
  </div>
</template>

<script>
import smsTimer from '@/components/smsTimer'
import { checkInput, queryDictionary } from '@/common/util'
import { sendMobileMsg, checkMobileMsg } from '@/service/comServiceNew'
export default {
  props: ['pageParam'],
  components: {
    smsTimer
  },
  data () {
    return {
      ygtUserInfo: $h.getSession('ygtUserInfo', {decrypt: false}),
      submitParam: {},
      smsNo: '', // 修改手机号短信类型数据字典
      startFlag: false,
      sendStatu: 1, // 1: 显示发送按钮 2: 显示倒计时 3:显示重新发送
      inputModel: {
        mobile: {
          name: '手机号',
          value: '',
          maxlength: '11',
          minlength: '11',
          format: 'phone'
        },
        code: {
          name: '短信验证码',
          value: '',
          maxlength: '6',
          minlength: '6',
          format: 'num'
        }
      }
    }
  },
  activated () {
    this.$store.commit('updateBusinessNextBtnStatus', false) // 隐藏下一步按钮
    this.ygtUserInfo.mobile = this.pageParam[0].mobile
    $h.setSession('ygtUserInfo', this.ygtUserInfo, {encrypt: false})
  },
  mounted () {
    // 查询短信类型数据字典
    queryDictionary(
      { type: 'ismp.sms_type', value: this.$parent.businessName },
      data => {
        this.smsNo = data.key
      }
    )
  },
  methods: {
    // 发送验证码
    sendMsg () {
      let _mobile = this.inputModel.mobile
      let flag = checkInput(_mobile)
      if (flag != '') {
        return
      }
      if (_mobile.value == this.ygtUserInfo.mobile) {
        _hvueToast({ mes: '新手机号与旧手机号一样，无需修改' })
        return
      }
      sendMobileMsg({
        flow_name: this.$route.query.type,
        userId: this.ygtUserInfo.userId,
        mobile: _mobile.value,
        smsNo: this.smsNo,
        businessCode: this.$route.query.type
      })
        .then(data => {
          if (data.error_no === '0') {
            // 开启倒计时
            // this.startCountDown()
            this.startFlag = true
            this.sendStatu = 2
          } else {
            _hvueToast({ mes: data.error_info })
          }
        })
        .catch(e => {
          _hvueToast({ mes: e.message })
        })
    },
    // 检测验证码
    checkMsg (submitParam) {
      checkMobileMsg(submitParam)
        .then(data => {
          if (data.error_no === '0') {
            this.submitParam = submitParam
            $h.setSession('newMobile', this.inputModel.mobile.value)
            // 调用父组件下一步事件
            this.$parent.emitNextEvent()
          } else {
            _hvueToast({ mes: data.error_info })
          }
        })
        .catch(e => {
          _hvueToast({ mes: e.message })
        })
    },
    /**
     * 提交前校验格式，组装参数
     */
    submitMobile () {
      let mobile = this.inputModel.mobile
      let code = this.inputModel.code
      let flag = checkInput(mobile)
      if (flag != '') {
        return
      }
      flag = checkInput(code)
      if (flag != '') {
        return
      }
      if (this.sendStatu == 1) {
        _hvueToast({
          mes: '您还未获取验证码'
        })
        return
      }
      let submitParam = {
        userId: this.ygtUserInfo.userId,
        mobile: mobile.value,
        verifyCode: code.value,
        smsNo: this.smsNo,
        businessCode: this.$route.query.type
      }
      this.checkMsg(submitParam)
    },
    /**
     * 请求参数打包
     */
    reqParamPackage () {
      let params = this.submitParam // 提交需要的参数
      return params
    },
    putFormData () {
      let formData = {
        userInfoModify: {
          oldMobile: this.ygtUserInfo.mobile,
          newMobile: this.inputModel.mobile.value
        }
      } // 需要保存的表单数据
      return formData
    },
    // 执行下一步事件
    doCheck (backParam) {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        // 可以下一步
        resolve()
      })
    }
  },
  computed: {
    showDisableClass: function () {
      let mobile = this.inputModel.mobile
      let code = this.inputModel.code
      let flag = checkInput(mobile, false)
      if (flag != '') {
        return true
      }
      flag = checkInput(code, false)
      if (flag != '') {
        return true
      }
      if (this.sendStatu == 1) {
        return true
      }
      return false
    }
  },
  deactivated () {
    // this.clearCountDown()
  }
}
</script>
