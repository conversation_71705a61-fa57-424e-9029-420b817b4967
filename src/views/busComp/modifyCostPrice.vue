<template>
  <section class="main fixed">
    <headComponent headerTitle="修改成本价"></headComponent>
    <article v-show="showPageIndex==1" class="content">
        <div class="com_title">
            <p>请选择需要变更的成本价计算方式:</p>
        </div>
        <div class="com_infobox">
            <ul>
                <li>
                    <span class="tit">成本价类型</span>
                    <p>{{ originalType }}</p>
                </li>
            </ul>
        </div>
        <div class="txt_info_box">
            <h5 class="title">温馨提示</h5>
            <div class="table_scroll">
                <table class="txt_info_table" cellspacing="0" cellpadding="0">
                    <thead>
                        <tr>
                            <th>成本价类型</th>
                            <th>算法说明</th>
                            <th>计算时间</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                <p>买入均价</p>
                            </td>
                            <td>
                                <p>算法:白天实时成交买入和卖出不影响成本价，不包含卖出费用。</p>
                                <p>公式:成本价=【(日终证券持仓数量-当日买入成交数量)x原成本价+买入成交金额】/证券余额</p>
                                <p class="imp">清仓证券当天清算前，该证券的成本价类型固定为买入均价</p>
                            </td>
                            <td>
                                <p>日终计算</p>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <p>持仓成本</p>
                            </td>
                            <td>
                                <p>算法:考虑白天实时成交买入，但不考虑白天实时成交卖出;该成本价包含了买入费用，但不包含卖出费用。</p>
                                <p>公式:成本价=(累计买入金额+当日买入金额)/(累计买入数量+当日买入数量)</p>
                            </td>
                            <td>
                                <p>实时计算</p>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <p>保本价</p>
                            </td>
                            <td>
                                <p>算法:考虑白天实时成交买入和卖出，同时该成本价包含了买入卖出实际已产生的费用，并预估了后续全部卖出可能产生的费用(实际并未扣取)</p>
                                <p>公式:成本价=(累计买入金额+当日买入金额-累计卖出金额-当日卖出金额+预估卖出费用)/(累计买入数量+当日买入数量-累计卖出数量-当日卖出数量)</p>
                            </td>
                            <td>
                                <p>实时计算</p>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <p>摊薄持仓成本</p>
                            </td>
                            <td>
                                <p>算法:考虑白天实时成交买入和卖出，该成本价包含了买入卖出实际已产生的费用，但未预估后续全部卖出可能产生的摊薄持仓成费用。</p>
                                <p>公式:成本价=(累计买入金额+当日买入金额-累计卖出金额-当日卖出金额)/(累计买入数量+当日买入数量-累计卖出数量-当日卖出数量)</p>
                            </td>
                            <td>
                                <p>实时计算</p>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </article>
    <article v-show="showPageIndex==2" class="content">
        <div class="com_title">
            <p>请选择成本价类型:</p>
        </div>
        <ul class="select_list">
            <li
            v-for="(d, index) in dataList"
            @click.stop="selClick(d, index)"
            :class="{ active: costPriceType.id === d.key }"
            :key="index"
          >
            <span>{{ d.value }}</span>
          </li>
        </ul>
    </article>
    <div class="bottom">
        <div v-show="showPageIndex==1" class="ce_btn"><a class="ui button block rounded" @click="showPageIndex=2">修改成本价类型</a></div>
        <div v-show="showPageIndex==2" class="ce_btn"><a class="ui button block rounded" @click="confirmChange">确认变更</a></div>
    </div>
  </section>
</template>

<script>
import { queryDictionary } from '@/common/util'
import headComponent from '@/components/headComponent'

export default {
  props: ['pageParam'],
  components: {
    headComponent
  },
  data () {
    return {
      ygtUserInfo: $h.getSession('ygtUserInfo', {decrypt: false}),
      showPageIndex: 1, // 1:修改成本价类型 2:确认变更
      originalType: '',
      newType: '',
      submitParam: {},
      costPriceType: {
        name: '成本价类型',
        value: '',
        type: 'cost_price_type',
        placeholder: '请选择成本价类型',
        id: '',
        selected: 0
      },
      dataList: []
    }
  },
  activated () {
    queryDictionary(
      { type: 'costPrice' },
      data => {
        this.dataList = data
        // 根据接口返回的数据设置 costPriceType 的默认值
        this.originalType = this.dataList.find(item => item.key === this.pageParam[0].profit_flag).value
        this.costPriceType.value = this.originalType
        this.costPriceType.id = this.dataList.find(item => item.key === this.pageParam[0].profit_flag).key
      }
    )
    this.showPageIndex = 1

    // 隐藏下一步按钮显示
    this.$store.commit('updateBusinessNextBtnStatus', false)
    this.$store.commit('updateIsShowHead', false) // 初始化展示头部 ，需要隐藏时在子组件处理
  },
  deactivated () {
    // 显示下一步按钮
    this.$store.commit('updateBusinessNextBtnStatus', true)
    this.$store.commit('updateIsShowHead', true)
  },
  methods: {
    // 提交后对结果进行处理的方法
    handleResultFunc (res) {
      _hvueToast({
        mes: '提交成功',
        icon: 'success',
        timeout: 1500,
        callback: () => {
          this.$router.push({ name: 'index', params: {} })
        }
      })
    },
    formatFundAccount (account) {
      if (!account || account.length < 4) return account
      return `${account.substring(0, 2)}****${account.substring(account.length - 2)}`
    },
    selClick (item, index) {
      this.costPriceType.id = item.key
      this.costPriceType.value = item.value
    },
    confirmChange () {
      if (!this.costPriceType.id) {
        _hvueToast({ mes: '请先选择新的成本价类型' })
        return
      }
      this.newType = this.costPriceType.value
      if (this.originalType === this.newType) {
        _hvueToast({ mes: '请选择不同的成本价类型' })
        return
      }
      _hvueConfirm({
        mes: `确定将普通资金号${this.formatFundAccount(this.ygtUserInfo.fundAccount)}的成本价类型从【${this.originalType}】变更为【${this.newType}】吗？`,
        opts: [
          {
            txt: '取消',
            color: false,
            callback: () => {
              this.costPriceType.id = ''
            }
          },
          {
            txt: '确定',
            color: true,
            callback: () => {
              // 这里添加实际提交逻辑
              this.submitParam = {
                profitFlag: this.costPriceType.id,
                oldProfitFlag: this.pageParam[0].profit_flag
              }
              this.$parent.emitNextEvent()
            }
          }
        ]
      })
    },

    selCallback (d) {
      Object.assign(this.costPriceType, {
        value: d.data.value,
        id: d.data.key
      })
    },
    checkSubmit () {
      if (!this.costPriceType.id) {
        _hvueToast({ mes: '请选择成本价类型' })
        return false
      }
      return true
    },
    doCheck () {
      return new Promise((resolve) => {
        if (this.checkSubmit()) {
          resolve()
        }
      })
    },
    reqParamPackage () {
      return this.submitParam
    },
    putFormData () {
      return {
        modifyCostPrice: this.submitParam
      }
    },
    pageBack () {
      if (this.showPageIndex === 1) {
        this.$parent.back()
      } else {
        this.showPageIndex = 1
      }
    }
  }
}
</script>
