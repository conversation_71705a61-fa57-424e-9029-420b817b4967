<template>
  <div class="investor_condition">
      <headComponent headerTitle="内部条件判断"></headComponent>
      <article class="content">
          <div class="cond_box">
              <h5 class="title">成为合格投资者需要满足以下内部条件：</h5>
              <ul class="condition_box">
                  <li class="ok" id="asset">
                      <div class="tit">
                          <p class="asset_info">
                              <span>内部资产大于等于500万</span>
                              <a class="icon_help" tips="内部资产会计算您在我司的股票持仓市值、理财产品市值、账户余额的总和。" href="javascript:void(0);"></a>
                          </p>
                      </div>
                  </li>
                  <li class="error" id="firsTransactionDate">
                      <div class="tit">
                          <p class="asset_info">
                              <span>交易经验已满2年</span>
                              <a class="icon_help" tips="交易经验以您在中登记录的首次交易日期开始计算" href="javascript:void(0);"></a>
                          </p>
                      </div>
                  </li>
              </ul>
          </div>
          <div class="cond_tips" style="display:none;">
              <p class="error">您的内部交易经验不满足监管要求，可通过提交外部证明材料进行申请。</p>
          </div>
      </article>
  </div>
</template>
<script>
import headComponent from '@/components/headComponent'
export default {
  components: {
    headComponent,
  },
  props: ["pageParam"],
  data() {
    return {
      nextFlag: false
    };
  },
  activated() {
    let str = JSON.stringify(this.pageParam[0]);
    if (str.indexOf("0") != -1) {
      this.nextFlag = false;
      this.$store.commit("updateNextBtnText", "返回首页");
      this.errorDesc = "抱歉，您不满足基本条件，暂时不能办理";
    } else {
      this.nextFlag = true;
      this.$store.commit("updateNextBtnText", "下一步");
      this.errorDesc = "恭喜您，满足全部基本条件";
    }
  },
  methods: {
    doCheck() {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        if (this.nextFlag || process.env.NODE_ENV == "development") {
          // 可以下一步
          resolve();
        } else {
          // 返回业务办理首页;
          this.$router.push({ name: "index" });
        }
      });
    },
    /**
     * 请求参数打包
     */
    reqParamPackage() {
      // 业务请求参数封装
      let params = {
        param1: "",
        param2: ""
      };
      return params;
    },
    putFormData() {
      let formData = {};
      return formData;
    },
    // 跳转到风险测评
    toRisk() {
      this.$router.push({
        name: "business",
        query: { type: "fxcp", name: "风险测评" }
      });
    }
  }
};
</script>
