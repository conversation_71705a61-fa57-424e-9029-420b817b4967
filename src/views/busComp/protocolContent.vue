<!-- 结果页组件 -->
<template>
  <div>
      <!--  协议权限类业务（电子签名、市价委托、otc）可查看协议  -->
      <div class="notice_box">
        <div class="pic">
          <img :src="imageSrc" />
        </div>
        <h5 v-html="pageParam[0].title"></h5>
      </div>
      <div class="result_sfinfo">
        <ul>
          <li>
            <span class="tit">{{businessName}}</span>
            <p>
              <span class="status">已开通</span>
            </p>
            <div class="fail_reason"
                 v-for="(item,index) in pageParam"
                :key="index">
              <span>《{{item.agreeName}}》</span>
              <p
                @click.stop="toDetail(item.agreementId,item.agreeName)"
              >查看协议</p>
            </div>
          </li>
        </ul>
      </div>
    <div class="ce_btn mt20">
      <a v-throttle class="ui button block rounded" @click.stop="back">返回首页</a>
    </div>
  </div>
</template>

<script>
import { queryBusiness } from '@/common/util'
export default {
  name: 'protocolContent',
  props: {
    pageParam: {
      type: Array
    }
  },
  data () {
    return {
      imageSrc: require('../../assets/images/not_ic04.png'),
      businessName: ''
    }
  },
  methods: {
    back () {
      this.$router.push({ name: 'index', params: {} })
    },
    // 查看协议
    toDetail (agreementId, agreementName) {
      // 查看协议详情
      this.$router.push({
        name: 'agreementDetail',
        query: { agreementId: agreementId, agreementTitle: agreementName, readOnly: '1' }
      })
    }
  },
  activated () {
    this.$store.commit('updateBusinessNextBtnStatus', false) // 隐藏下一步按钮
    queryBusiness(this.$route.query.type, data => {
      this.businessName = data.businessName
    })
    if (this.pageParam.length > 0) {

    } else {
      _hvueAlert({
        mes: '未查询到协议'
      })
    }
  }
}
</script>
