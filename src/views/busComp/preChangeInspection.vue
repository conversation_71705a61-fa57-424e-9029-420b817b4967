<!-- 三方存管-变更银行卡-条件检测 -->
<template>
  <div>
    <div v-if="interestState !=2 && interestState !=3">
      <div class="card_wbox">
        <ul class="sele_card">
          <li>
            <div class="card_cont">
              <span class="card_spel">主卡</span>
              <dl class="card_info">
                <dt>
                  <img :src="cardDetails.smallImg" />
                </dt>
                <dd>
                  <h5>{{cardDetails.bankFullName}}</h5>
                  <p>储蓄卡</p>
                </dd>
              </dl>
              <div class="card_number">{{cardDetails.bankAccount|formatBankCardNo}}</div>
            </div>
            <div class="card_shadow">
              <span>
                <strong>暂时无法变更</strong>
              </span>
            </div>
          </li>
        </ul>
      </div>
      <div class="cond_box">
        <h5 class="title">原因如下</h5>
        <ul>
          <li class="error" v-for="(it,index) in errorList" :key="index">
            <div class="tit spel">
              <p>{{it.describe}}</p>
              <a v-if="it.type=='3'" class="link" @click.stop="to_interest">前往结息</a>
              <a v-else-if="it.type=='4' && !fromTHSGB" class="link" @click.stop="to_transfer">银证转账</a>
            </div>
          </li>
        </ul>
      </div>
      <div class="ce_btn mt20">
        <a v-throttle class="ui button block rounded" @click.stop="pageBack">确定</a>
      </div>
    </div>
    <!-- 结息成功页面 -->
    <businessSuccess v-on:my-event="pageBack" v-if="interestState ==2">
      <div class="result_main">
        <div class="icon_ok"></div>
        <h5>结息成功</h5>
        <p class="center">您于 {{busTime|formatDate('yyyy.MM.dd hh:mm:ss')}}结息成功</p>
        <p v-if="transferState==0" class="center">请于下个工作日再行办理变更存管</p>
        <p v-else class="center">请银证转账出余额，并下个工作日再行办理变更存管</p>
      </div>
      <div class="ce_btn mt20">
        <a v-throttle v-if="transferState==0 || fromTHSGB" class="ui button block rounded" @click.stop="pageBack">返回首页</a>
        <a v-throttle v-else-if="transferState !=0 && !fromTHSGB" class="ui button block rounded" @click.stop="to_transfer">银证转账</a>
      </div>
    </businessSuccess>
  </div>
</template>

<script>
import businessSuccess from '@/views/common/businessSuccess' // 结果页
import { interestSettlement } from '@/service/comServiceNew'
export default {
  props: ['pageParam'],
  components: { businessSuccess },
  data () {
    return {
      ygtUserInfo: $h.getSession('ygtUserInfo', {decrypt: false}), // 客户基本信息
      cardDetails: $h.getSession('cardDetails') || {}, // 旧银行卡信息
      interestState: 0, // 结息状态 0 无需结息，1需要做结息 2结息成功 3结息失败
      transferState: 0, // 转账状态 0 无需转账，1需要做转账
      busTime: new Date(), // 业务办理时间
      errorList: [], // 条件列表
      fromTHSGB: $h.getSession('fromTHSGB'), // 渠道同花顺公版
    }
  },
  activated () {
    this.$store.commit('updateBusinessNextBtnStatus', false) // 隐藏下一步按钮
    this.cardDetails = $h.getSession('cardDetails') || {}
    if (this.pageParam.length < 1) {
      _hvueAlert({ mes: '未查询到办理条件', title: '提示' })
    } else {
      // 处理不满足变更条件页面
      let array = JSON.parse(this.pageParam[0].nonPassList)
      for (let i = 0; i < array.length; i++) {
        let element = array[i]
        if (element.type == '3') {
          this.interestState = 1 // 需要结息
        } else {
          this.transferState = 1 // 需要转账
        }
      }
      this.errorList = array
    }
  },
  methods: {
    // 结息
    to_interest () {
      interestSettlement({
        clientId: this.ygtUserInfo.clientId,
        bankNo: this.cardDetails.bankNo, // 原银行编号
        moneyType: this.cardDetails.moneyType, // 原币种类型
        opAccount: this.cardDetails.fundAccount // 原银行卡资金账户
      }).then(
        res => {
          if (res.error_no === '0') {
            this.busTime = new Date()
            this.interestState = 2 // 结息成功
          } else {
            _hvueToast({
              icon: 'error',
              mes: res.error_info
            })
          }
        },
        err => {
          console.log(err)
        }
      )
    },
    // 跳转到交易的银证转账功能
    to_transfer () {
      let msg = {
        funcNo: '60099',
        actionType: '6', // 打开交易页面
        params: {
          pageName: '107'
        }
      }
      $h.callMessageNative(msg)
    },
    // 返回 -- 需要回滚到上一步去。
    pageBack () {
      this.$router.push({ name: 'index', params: {} })
      //   this.$router.go(-1);
    }
  }
}
</script>
