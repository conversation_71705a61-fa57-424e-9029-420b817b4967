/*
 * @Author: lyt
 * @Date: 2021-9-8
 * @Last Modified by: Way
 * @Last Modified time: 2021-9-8
 */
<template>
    <div>
        <div v-show="showZtgInfo">
            <headComponent headerTitle="填写转托管信息"></headComponent>
            <div class="ste_cont">
                <ul v-for="(item, key) in JSON.parse(ztgInfo[0].data)" :key="key">
                    <li class="spel">
                        <span class="tit">证券账户</span>
                        <div class="cont">
                            <span>{{ item.szAccount }}</span>
                        </div>
                    </li>
                    <li class="spel">
                        <span class="tit">证券名称</span>
                        <div class="cont">
                            <span>{{
                                item.stockName
                            }}</span>
                        </div>
                    </li>
                    <li class="spel">
                        <span class="tit">转出数量</span>
                        <div class="cont">
                            <span>{{item.stockCount}}</span>
                        </div>
                    </li>
                </ul>
            </div>
            <div class="ste_cont">
                <ul>
                    <li>
                        <span class="tit">转入券商</span>
                        <div class="cont" @click.stop="boxClick('zqName')">
                            <span>{{ selData.zqName.value }}</span>
                        </div>
                        <span class="lk"></span>
                    </li>
                    <li>
                        <span class="tit">券商席位号</span>
                        <div class="cont" @click.stop="boxClick('seatNo')">
                            <span>{{ selData.seatNo.value }}</span>
                        </div>
                        <span class="lk"></span>
                    </li>
                </ul>
            </div>
            <div class="tips">
                <span>
                    温馨提示：本业务仅支持所有证券账户持仓转入同一家券商，且转入券商及券商席位号需手动录入。
                </span>
            </div>
        </div>
        <div v-show="showZtgInfoC">
            <headComponent headerTitle="确认转托管信息"></headComponent>
            <div class="tips">
            <span>
                请您确认以下信息：
            </span>
            </div>
            <ztg-info-confirm :isShow="showZtgInfoC" :dataList="dataList"></ztg-info-confirm>
        </div>
        <inputBox
            v-if="inputBox.show"
            v-model="inputBox.show"
            :title="inputBox.title"
            :checkType="inputBox.checkType"
            :maxLength="inputBox.maxLength"
            :value="inputBox.value"
            :placeholder="inputBox.placeholder"
            @selCallback="selCallback"
        ></inputBox>
    </div>
</template>

<script>
import headComponent from '@/components/headComponent' // 头部
import inputBox from '@/components/inputBox' // 输入器
import ztgInfoConfirm from "../../components/ztgInfoConfirm";

export default {
  props: {
    pageParam: {
      type: Array,
    },
  },
  components: {
    headComponent,
    inputBox,
    ztgInfoConfirm,
  },
  data () {
    return {
      userInfoModify: {}, // 提交表单的参数
      ygtUserInfo: $h.getSession('ygtUserInfo', {decrypt: false}),
      writeIndex: '',
      selData: {
        zqName: {
          title: '券商',
          placeholder: '请输入转入券商',
          value: '',
          checkType: 'zqName',
        },
        seatNo: {
          title: '席位号',
          placeholder: '请输入转入券商席位号',
          value: '',
          checkType: 'seatNo',
        },
      },
      selBox: {
        show: false,
        title: '',
        category: '',
        defaultStr: '',
        initData: [],
      },
      inputBox: {
        show: false,
        title: '',
        placeholder: '',
        value: '',
        checkType: '',
        maxLength: '',
      },
      dataList: [],
        showZtgInfoC: false, // 展示转托管信息确认
    }
  },
  computed: {
    showZtgInfo () {
      if (!this.selBox.show && !this.inputBox.show && !this.showZtgInfoC) {
        this.$store.commit('updateBusinessNextBtnStatus', true) // 显示下一步按钮
        return true
      } else {
        return false
      }
    },
  },
  created () {
    // 隐藏头部
    this.$store.commit('updateIsShowHead', false)
    // 更改下一步按钮的文字
    // this.$store.commit('updateNextBtnText', '下一步')
      this.showZtgInfoC = false;
      this.ztgInfo = this.pageParam || this.$route.query.dataList;
      //
      // this.ztgInfo = [{
      //     data: '[{"szAccount":"**********","stockName":"科大讯飞","stockCount":26800,"stockCode":"002230"}]',
      // }]
  },
    mounted () {
        window.phoneBackBtnCallBack = this.pageBack
    },
  methods: {
      pageBack () {
          if (this.showZtgInfo) {
              if (this.$route.path == '/index') {
                  closeYgt(0, '1A', 0, 1)
              } else {
                  if (this.$parent.pageBack) {
                      this.$parent.pageBack()
                  } else {
                      this.$parent.back()
                  }
              }
          } else {
              this.showZtgInfoC = false;
              this.showZtgInfo = true;
          }
      },
    checkSubmit () {
      let _selData = this.selData
      if (!_selData.zqName.value) {
        _hvueToast({
          mes: '请完善转入券商',
        })
        return false
      }
        if (!_selData.seatNo.value) {
            _hvueToast({
                mes: '请完善转入券商席位号',
            })
            return false
        }

        this.dataList = this.ztgInfo.map(item => {
            let data = JSON.parse(item.data);
            data[0]['zqName'] = _selData.zqName.value;
            data[0]['seatNo'] = _selData.seatNo.value
            return data[0];
        })
        return true
    },
    doCheck () {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
          if (!this.showZtgInfoC) {
              if (this.checkSubmit()) {
                  // 可以下一步 确认信息
                  this.showZtgInfoC = true;
                  // this.$router.push({
                  //     name: 'ztgInfoCon',
                  //     query: {
                  //         dataList: this.ztgInfo,
                  //     },
                  // })
              }
          } else {
              resolve()
          }
      })
    },
    /**
         * 请求参数打包
         */
    reqParamPackage () {
      // 业务请求参数封装
      let params = {
          ztgInfo: JSON.stringify(this.dataList)
      }
      return params
    },
    /**
         * 表单参数打包
         */
    putFormData () {
      let formData = {
          ztgInfo: this.dataList,
      }
      return formData
    },
    // 打开选择器和输入组件
    boxClick (type, title) {
      this.$store.commit('updateBusinessNextBtnStatus', false) // 隐藏下一步按钮
      this.writeIndex = type
      if (
        type === 'zqName' ||
        type === 'seatNo'
      ) {
        // 打开券商、券商席位号
        this.inputBox.show = true
        this.inputBox.title = this.selData[type].title
        this.inputBox.placeholder = this.selData[type].placeholder
        this.inputBox.checkType = this.selData[type].checkType
        this.inputBox.value = this.selData[type].value
        this.inputBox.maxLength = this.selData[type].maxLength
        this.inputBox.tips = this.selData[type].tips
      }
    },
    // 选择器和输入器组件回调方法
    selCallback (d) {
      this.$store.commit('updateBusinessNextBtnStatus', true) // 显示下一步按钮
      let type = this.writeIndex
      if (
        type === 'zqName' ||
        type === 'seatNo'
      ) {
        // 返回邮编电子邮箱年收入，职业输入
        Object.assign(this.selData[this.writeIndex], {
          value: d.value,
        })
      }
    },
    back () {
      this.$router.go(-1)
    },
  },
}
</script>
<style scoped>
    .tips{
        padding: 0.1rem;
        color: #999999;
    }
</style>
