<!-- 关联关系-账户查询 -->
<template>
  <div class="glgxAccountList">
    <h5 class="com_title">将为您的下列账户办理关联关系确认：</h5>
    <div v-show="szaAccountList.length>0" class="cond_box">
      <h5 class="title">深市账户</h5>
      <ul class="acct">
        <li v-for="(item,index) in szaAccountList" :key="index">
          <div class="cont">
            <p>
              <span>{{item.marketName}} {{ item.stockAccount }}</span>
              <em :class="getAccountStyle(item.holderStatus,item.isExists)">未绑定</em>
            </p>
          </div>
        </li>
      </ul>
    </div>
    <div v-show="shaAccountList.length>0" class="cond_box">
      <h5 class="title">沪市账户</h5>
      <ul class="acct">
        <li v-for="(item,index) in shaAccountList" :key="index">
          <div class="cont">
            <p>
              <span>{{item.marketName}} {{ item.stockAccount }}</span>
              <em :class="getAccountStyle(item.holderStatus,item.isExists)">未绑定</em>

            </p>
          </div>
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
import headComponent from '@/components/headComponent'

export default {
  components: {
    headComponent,
  },
  props: ['pageParam'],
  data () {
    return {
      szaAccountList: [],
      shaAccountList: [],
      nextFlag: false
    }
  },
  activated () {
    let list = this.pageParam
    this.szaAccountList = []
    this.shaAccountList = []
    list.forEach(item => {
      if (item.exchangeType === '2') {
        this.szaAccountList.push(item)
      } else if (item.exchangeType === '1') {
        this.shaAccountList.push(item)
      }
    })
  },
  methods: {
    getAccountStyle (holderStatus, isExists) {
      if (isExists === '0' || holderStatus === '0') { // 已指定
        return 'normal_span'
      } else {
        return 'assign_span'
      }
    },
    doCheck () {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        // 可以下一步
        resolve()
      })
    },
    /**
     * 请求参数打包
     */
    reqParamPackage () {
      // 业务请求参数封装
      let params = {}
      return params
    },
    putFormData () {
      $h.setSession('glgxSignKeys', JSON.stringify(this.pageParam))
      let formData = {
        chooseAccount: this.pageParam
      }
      return formData

    }
  }
}
</script>
<style>
.glgxAccountList .com_title{
  font-size: 0.16rem;
}
.glgxAccountList .cond_box .title, .glgxAccountList .cond_box ul li .cont p{
 color:#333;
}
</style>
