<template>
  <section>
    <div v-if="+bopUserInfo.profFlag">
      <div class="ste_cont">
        <ul>
          <li class="spel">
            <span class="tit">专业投资者类型</span>
            <div class="cont">
              <span>{{bopUserInfo.profType}}专业投资者</span>
            </div>
          </li>
          <li class="spel" :class="[(bopUserInfo.profEndDateFlag === '0' ? 'error' : 'normal')]">
            <span class="tit">有效期至：{{bopUserInfo.profEndDate|formatDate('yyyy.MM.dd')}}</span>
            <div class="cont">
              <span v-show="bopUserInfo.profEndDateFlag === '0'">即将过期</span>
            </div>
          </li>
        </ul>
      </div>
      <div class="cond_tips textleft">
        <p>投资小贴士：</p>
        <p>申请转化成为专业投资者或者个人申请成为专业投资者，根据《证券期货投资者适当性管理办法》中第十一条规定，同时符合下列条件的自然普通专业投资者可以申请转化成B专业投资者。</p>
        <p>（1）金融资产不低于500万元（系统内部资产）；或近3年个人年均收入不低于50万元</p>
        <p>（2）具有2年以上证券交易经验（取中国结算记录）。</p>
        <p>同时符合下列条件的自然人普通投资者可以申请转化成C专业投资者：</p>
        <p>（1）金融资产不低于300万元（系统内部资产）；或近3年个人年均收入不低于30万元</p>
        <p>（2）具有1年以上证券交易经验（取中国结算记录）。</p>
      </div>
    </div>
    <div class="investor_condition" v-else>
      <article class="content">
        <h5 class="com_title">请选择您需要认定的类型</h5>
        <ul class="p_check_list">
          <li :key="idx" v-for="(itm, idx) in typeList" @click.prevent="getActiveId(itm.id)">
            <div class="base"><span class="icon_radio" :class="{'checked': activeId === itm.id}">{{itm.value1}}</span></div>
            <div class="upload_txtcont">
              <p>{{itm.value2}}</p>
              <p>{{itm.value3}}</p>
              <p>{{itm.value4}}</p>
            </div>
          </li>
        </ul>
      </article>
    </div>
  </section>
</template>
<script>
export default {
  props: ["pageParam"],
  data() {
    return {
      continueFlag: false, // 是否可以办理开通股转账户标识
      typeList: [
      {
        id: '1',
        value1: 'B类专业投资者',
        value2: '同时符合下列条件的自然人普通投资者可以申请转化成为B专业投资者：',
        value3: '（1）金融资产不低于500万元，或最近3年个人年均收入不低于50万元；',
        value4: '（2）证券交易经验大于等于2年。'
      },
      {
        id: '2',
        value1: 'C类专业投资者',
        value2: '同时符合下列条件的自然人普通投资者可以申请转化成为C专业投资者：',
        value3: '（1）金融资产不低于300万元，或最近3年个人年均收入不低于30万元；',
        value4: '（2）证券交易经验大于等于1年。'
      }
    ],
      activeId: '',
      bopUserInfo: {}
    }
  },
  activated() {
    // var params = {
    //   profFlag, //0-否 1-是  是否专业投资者
    //   profType, //投资者类型 B C
    //   profEndDate, //投资者截止日志
    //   profEndDateFlag, //0-快到期 1-未到期
    //   openDateFlag //0-当日新开用户 1-老用户
    // }
    this.bopUserInfo = this.pageParam[0] || {};
    this.activeId = '';
    this.continueFlag = true;

    if(!+this.bopUserInfo.profFlag){//非专业投资者，开启认定
      this.$store.commit("updateNextBtnText", "申请专业投资者认定");
    }else{
      if(+this.bopUserInfo.profEndDateFlag){//专业投资者未过期无需认定
        this.continueFlag = false;
        this.$store.commit("updateNextBtnText", "返回首页");
      }else{
        this.activeId = this.bopUserInfo.profType === 'B' ? '1' : '2';
        this.$store.commit("updateNextBtnText", "申请专业投资者续签"); // 即将过期，续签
      }
    }
  },
  methods: {
    getActiveId (id) {
      this.activeId = id;
    },
    checkSubmit() {
      if (!+this.bopUserInfo.profFlag && !this.activeId) {
        _hvueToast({ mes: '请选择您需要认定的类型' })
        return false;
      }

      // if(!+this.bopUserInfo.profFlag && this.bopUserInfo.openDateFlag === '0'){
      //   _hvueToast({ mes: '对不起，当日新开账户无法提交专业投资者申请，请下个交易日再来' })
      //   return false;
      // }
      return true;
    },
    doCheck() {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        if (this.continueFlag) {
          if (this.checkSubmit()) {
            // 可以下一步
            resolve();
          }
        } else {
          // 无法继续办理
          this.$router.push({ name: "index" });
        }
      });
    },
    /**
     * 请求参数打包
     */
    reqParamPackage() {
      // 业务请求参数封装
      let params = {
        zytzzrdOpenType: this.activeId
      };
      return params;
    },
    putFormData() {
      this.bopUserInfo.zytzzrdOpenType = this.activeId;
      if (this.activeId === '1') {
        this.bopUserInfo.profType = 'B'
      } else if (this.activeId === '2') {
        this.bopUserInfo.profType = 'C'
      }
      let formData = {
        zytzzrdOpenView: this.bopUserInfo
      }; // 需要保存的表单数据
      return formData;
    }
  }
};
</script>
<style scoped>
  .normal .tit, .normal .cont{
    color: #999!important;
  }
  .error .tit, .error .cont{
    color: red!important;
  }
</style>
