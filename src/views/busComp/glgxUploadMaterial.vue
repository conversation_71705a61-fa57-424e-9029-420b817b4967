<template>
  <div class="glgxUpload">
    <div class="agree_main">
      <h5 class="title">请上传<span>沪B：43455654</span>的无余额证明材料:</h5>
      <div class="glgx_uploadtips">
        <span class="tip link">如何获取证明 ></span>
      </div>
      <div class="upload_cont">
        <div class="upload_pic">
          <div class="pic">
            <img src="@/assets/images/sl_img.png"/>
          </div>
          <div class="textcenter">
            <a
                class="btn"
                @click.stop="typePop('show', '4')"
            >点击拍摄/上传</a
            >
          </div>
        </div>
      </div>
      <div>
        <span class="tip">请上传jpg / png / jpeg 格式的图片</span>
      </div>
    </div>
  </div>
</template>
<script>
import getImgBoxBrowser from '@/components/getImg_browser';
import { uploadImg } from '@/service/comServiceNew'
import showImage from '../common/showImage' // 展示图片
export default {
  components: {
    getImgBoxBrowser,
    showImage,
  },
  props: ['pageParam'],
  data () {
    return {
      positive: {
        src: require('@/assets/images/id_icon01.png'),
        uploaded: false,
      },
      // imgArr: [{
      //   imgSrc: "43242"
      // }], // 图片列表
      exampleArr: [
        {
          id: 1,
          imgSrc: (process.env.NODE_ENV == 'development' ? '' : ROUTER_BASE) +
              '/static/images' + '/nashui.jpg',
        },
      ], // 示例列表
      serivalId: this.$parent.publicParam.serivalId,
      flow_current_step_name: this.$parent.flow.currentStepInfo.flow_current_step_name,
      fillContent: {
        name: '资产证明',
        mediaCode: '231',
        content:
            `<h5 class="title">收入证明</h5>
            <p>请提供您近3年年均收入达到50万或近1年收入达到 150万的证明材料，提供以下任意一种材料：</p>
            <dl class="info">
              <dd>1.单位收入证明（加盖公章）+银行交易明细（加盖 银行业务公章/电子章）</dd>
              <dd>2.单位收入证明（加盖公章）+个人所得税纳税记录 (有税务凭证号和验证码）</dd>
            </dl>`,

      },
      isShow: false, // 是否展示图片详情
      activeImgIdx: -1, // 当前图片序号
      activeImgSrc: '', // 当前图片地址
      ygtUserInfo: $h.getSession('ygtUserInfo', { decrypt: false }),
    };
  },
  created () {
    window.imgCallBack = this.getImgCallBack;
  },
  activated () {

  },
  methods: {
    doCheck () {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        if (this.imgArr.length) {
            // 可以下一步
            resolve();
        } else {
          _hvueToast({ mes: '请上传证明' })
        }
      });
    },
    selImgClick (imgType) {
      this.imgType = imgType;
      this.$refs.getImgBoxBrowser.getImg();
    },
    getImgCallBack (imgInfo) {
      // h5本地上传base64到bus
      imgInfo.base64 = this.filterBase64Pre(
          imgInfo.base64
      )
      let param = {
        mediaCode: this.fillContent.mediaCode,
        isOcr: '0',
        file_data: encodeURIComponent(imgInfo.base64),
        serivalId: this.serivalId,
        businessCode: this.$route.query.type,
        flow_current_step: this.flow_current_step_name,
      }
      uploadImg(param)
          .then((data) => {
            if (data.error_no === '0') {
              let uploadInfo = data.uploadInfo[0];
              let obj = {
                imgSrc: 'data:image/jpeg;base64,' + imgInfo.base64,
                uploadInfo: uploadInfo,
              }
              this.imgArr.push(obj);
            } else {
              return Promise.reject(new Error(data.error_info))
            }
          })
          .catch((e) => {
            _hvueToast({ mes: e.message })
          })
    },
    // 删除图片
    deleteImg (i) {
      this.imgArr.splice(i, 1);
    },
    // 展示案例图
    showImg (item) {
      this.activeImgIdx = item.id;
      this.activeImgSrc = item.imgSrc;
      this.isShow = true;
    },
    isShowImage (isShow) {
      this.isShow = isShow;
    },
    filterBase64Pre (ndata) {
      let arr = ndata.split('base64,')
      return arr[arr.length - 1]
    },

    typePop () { },
    /**
     * 请求参数打包
     */
    reqParamPackage () {
      // 业务请求参数封装
      let params = {};
      return params;
    },
    putFormData () {
      let uploadInfo = this.imgArr.map(item => item.uploadInfo);
      let formData = {
        uploadIdProve: uploadInfo,
      };
      return formData;
    },
  },
};
</script>
<style>
  .glgxUpload .tip{
    padding:0 0.4rem;
    color:#948f8f;
  }
  .glgxUpload .tip.link{
    color:#1971cb;
    text-decoration-line: underline;
  }
  .glgxUpload .tip:before{
    content: "";
    width: 0.2rem;
    height: 0.2rem;
    position: absolute;
    left: 0.1rem;
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100%;
    background-image: url(../../../static/images/error_tipimg.png);
  }
  .glgxUpload .btn{
    display: inline-block;
    margin-top: 0.2rem;
    color:#386fd1;
  }
</style>