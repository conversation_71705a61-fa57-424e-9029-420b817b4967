<template>
  <div>
    <headComponent headerTitle="办理结果"></headComponent>
    <div class="result_main">
      <div
        :class="{
          icon_ing: (handleStatus == '0' && formStatus != '4') || handleStatus == '',
          icon_ok: formStatus == '2' && handleStatus == '1',
          icon_error: formStatus == '4' || handleStatus == '2'
        }"
      ></div>
      <h5>{{ showDealDesc }}</h5>
      <p v-if="formStatus == '2' && handleStatus == '1' && businessCode === 'zhzh'" class="center">
        为防止账户再次丢失，请您将账号记录保存
      </p>
      <p v-else-if="formStatus == '2' && handleStatus == '1'" class="center">
        您于 {{ this.businessFlowInfo.updateDate }} 办理{{ businessName }}成功
      </p>
      <p v-else-if="formStatus == '4' || handleStatus == '2'" class="center">
        您于 {{ this.businessFlowInfo.updateDate }} 办理{{ businessName }}失败
      </p>
      <p class="center" v-else>{{ showTips }}</p>
    </div>
    <!-- 视频或身份证驳回 -->
    <div class="reject_info" v-if="formStatus == '4' && !isFormReject">
      <div class="item" v-for="(item, index) in rejectList" :key="index">
        <div>
          <h5>{{ item.rejectCode | fillRejectCode }}</h5>
          <p>{{ item.reason }}</p>
        </div>
      </div>
    </div>
    <!-- 表单驳回 -->
    <div v-if="isFormReject">
      <div v-for="(item, index) in rejectList" :key="index" class="fail_reason">
        <span>失败原因</span>
        <p @click.stop="showErrorInfo(item.reason)">{{ item.reason.substring(0, 8) }}...</p>
      </div>
    </div>
    <dl
      class="result_tips"
      v-if="
        this.$parent.flow.flowName == 'lryykh' &&
          formStatus == '2' &&
          (handleStatus == '1' || handleStatus == '0')
      "
    >
      <dt>
        <span>提示</span>
      </dt>
      <dd>1. 请您持有效身份证明文件等相关资料亲临我公司营业部办理融资融券业务</dd>
      <dd>2. 请您确保临柜办理业务时您的账户20个交易日日均资产满50万元</dd>
      <dd>3. 本页面仅作为融资融券预约，不代表我公司已同意您的融资融券业务申请</dd>
    </dl>

    <div class="ce_btn mt20" v-if="formStatus == 4">
      <a
        v-throttle
        v-if="!isFormReject || this.reFixedList.includes($route.query.type)"
        class="ui button block rounded"
        @click.stop="reject"
        >{{ rejectBtnName }}</a
      >
      <a
        v-throttle
        v-if="this.reFixedList.includes($route.query.type)"
        class="ui button block rounded mt10"
        @click.stop="back"
        >返回首页</a
      >
      <a v-throttle v-else class="ui button block rounded border mt10" @click.stop="giveup">{{
        giveupBtnName
      }}</a>
    </div>
  </div>
</template>

<script>
import headComponent from '@/components/headComponent' // 头部
import { closeYgt } from '@/common/sso'
import { queryRejectReason, closeFlow } from '@/service/comServiceNew'
import { queryBusiness } from '@/common/util'

export default {
  components: {
    headComponent
  },
  props: ['pageParam'],
  data() {
    return {
      serivalId: this.$parent.publicParam.serivalId,
      formStatus: '', // 表单状态
      handleStatus: '', // 表单处理状态  0:未处理   1：处理成功   2：处理失败
      businessName: '',
      businessCode: '',
      businessFlowInfo: '',
      rejectList: [], // 驳回信息
      rejectStepInfo: [],
      isFormReject: false, // 标记是否是表单驳回
      rejectBtnName: '', // 驳回按钮
      giveupBtnName: '放弃办理', // 放弃办理按钮
      reHandleList: ['zghgtzz', 'smhgtzz'], // 放弃办理按钮文案改成重新办理的业务
      reFixedList: ['dzjy', 'gspt'], // 可以驳回修复（特殊需求）
      busiCode: this.$route.query.type !== 'yyxh' ? this.$route.query.type : 'xh'
    }
  },
  created() {
    let type = this.$route.query.type
    // 销户业务编号个性化处理
    if (type === 'xh') {
      _hvueAlert({
        mes: '尊敬的投资者，请不要在销户审核期间对账户进行操作'
      })
    }
    if(type === 'xh') {
      type = 'yyxh'
    }
    queryBusiness(type, data => {
      this.businessName = data.businessName
    })
    if (this.reFixedList.includes(this.$route.query.type)) {
      this.serivalId = $h.getSession('result_serivalId') || this.serivalId
    }
  },
  activated() {
    window.phoneBackBtnCallBack = this.back
    if (this.pageParam.length <= 0 && !this.reFixedList.includes(this.$route.query.type)) {
      _hvueAlert({
        mes: '未查询到办理结果'
      })
    }
    // 隐藏business.vue的下一步按钮
    this.$store.commit('updateBusinessNextBtnStatus', false) // 隐藏下一步按钮
    this.$store.commit('updateIsShowHead', false) // 隐藏head

    this.businessFlowInfo = this.pageParam[0]
    this.formStatus = this.businessFlowInfo.formStatus
    this.handleStatus = this.businessFlowInfo.handleStatus
    this.$parent.flow.formStatus = this.formStatus
    this.businessCode = this.businessFlowInfo.businessCode
    console.log('setHandleStatus:' + this.handleStatus)
    this.$store.commit('updateHandleStatus', this.handleStatus) // handleStatus
    this.$store.commit('updateFormStatus', this.formStatus) // formStatus
    if (this.formStatus === '4') {
      this.queryRejectReason()
    }
    // 办理成功时，先结束流程
    if (this.handleStatus === '1' && this.formStatus === '2') {
      this.confirmEvent()
    }
    // 通过vuebus接受businessResult组件的pageBack事件
    this.$bus.on('pageBack', () => {
      this.back()
    })
    // 通过vuebus接受businessResult组件的giveUp事件
    this.$bus.on('giveUp', () => {
      this.giveup()
    })
  },
  computed: {
    showDealDesc() {
      return this.getDealDesc(this.formStatus, this.handleStatus)
    },
    showTips() {
      return this.getTips(this.businessCode)
    }
  },
  methods: {
    showErrorInfo(errorDesc) {
      _hvueAlert({
        title: '失败原因',
        mes: errorDesc
      })
    },
    getDealDesc(formStatus, handleStatus) {
      if (handleStatus === '0' && formStatus !== '4') {
        return '成功提交，处理中'
      }
      if (handleStatus === '1' && formStatus === '2' && this.businessCode === 'zhzh') {
        return '找到账户如下'
      }
      if (handleStatus === '1' && formStatus === '2') {
        return '办理成功'
      }
      if (formStatus === '4') {
        return '审核驳回'
      }
      if (handleStatus === '2' && formStatus === '2') {
        return '办理失败'
      }
      return '成功提交，处理中'
    },
    getTips(code) {
      switch (code) {
        case 'cybzq':
          return '创业板转签开通资料已提交审核，结果将于2个工作日内以短信方式通知。'
        case 'lryykh':
          return '恭喜您，融资融券开户预约成功，请等待后端审核通过后前往营业部'
        case 'xh':
          return '业务申请已提交，请耐心等待处理结果，处理通知请关注手机短信。审核期间请不要操作账户，否则可能导致销户失败！'
        default:
          let tips = this.businessName + '业务申请已提交，请耐心等待处理完成后再进入业务查看结果'
          return tips
      }
    },
    // 点击确定按钮，直接返回首页
    confirmEvent() {
      if (this.$parent.flow.currentStepInfo.flow_finish === '0') {
        // 表单已结束 结束业务流程 调用success的submit方法。调用父组件的提交方法。
        this.$parent.next({}, { isLastReq: true })
      }
    },
    back() {
      let loginType = this.$route.query.loginType || $h.getSession('loginType') || '1'
      if (this.$route.query.type === 'xgmm') {
        if (
          $h.getSession('passwordType').indexOf('2') !== -1 &&
          this.handleStatus === '1' &&
          this.formStatus === '2'
        ) {
          loginType == '1'
            ? closeYgt(1, '1A', 1, 0, this.$router)
            : closeYgt(1, '1B', 1, 0, this.$router)
          this.$router.push({ name: 'index', params: {} })
        } else {
          this.$router.push({ name: 'index', params: {} })
        }
      } else {
        this.$router.push({ name: 'index', params: {} })
      }
    },
    giveup() {
      // 放弃办理时 若pageFlow未结束 则先将pageFlow结束掉
      // if(this.$parent.flow.currentStepInfo.flow_finish ==='0' && this.$parent.flow.currentStepInfo.flow_current_step_name=='success'){
      //   this.$parent.next();
      // }
      // 放弃办理
      closeFlow({
        userId: $h.getSession('ygtUserInfo', { decrypt: false }).userId,
        businessCode: this.$route.query.type,
        serivalId: this.serivalId
      }).then(res => {
        if (res.error_no === '0') {
          this.$router.push({ name: 'index' })
        } else {
          _hvueAlert({
            title: '提示',
            mes: res.error_info
          })
        }
      })
    },

    reject() {
      // 步骤名称为空时，结束流程，直接返回首页
      if (!this.rejectStepInfo.stepName) {
        // 返回首页
        this.$parent.next({}, { isLastReq: true }).then(res => {
          // 返回首页
          this.$router.push({ name: 'index' })
        })
        return
      }
      if (
        this.$parent.flow.currentStepInfo.flow_finish === '1' ||
        this.reFixedList.includes(this.$route.query.type)
      ) {
        // 如果流程已经结束 则走驳回
        this.$parent.rejectFlow(this.rejectStepInfo.stepName)
      } else {
        // 如果未结束，先结束，再驳回
        this.$parent.next().then(res => {
          this.$parent.rejectFlow(this.rejectStepInfo.stepName)
        })
      }
    },
    queryRejectReason() {
      queryRejectReason({
        serivalId: this.serivalId
      }).then(data => {
        if (data.error_no === '0') {
          this.rejectList = data.rejectInfo
          this.rejectStepInfo = data.rejectStepInfo[0]
          if (data.rejectStepInfo[0].stepName.indexOf('uploadIdCard') > -1) {
            this.rejectBtnName = '重新上传证件'
          } else if (data.rejectStepInfo[0].stepName.indexOf('videoPage') > -1) {
            this.rejectBtnName = '重新视频见证'
          } else if (data.rejectStepInfo[0].stepName && data.rejectStepInfo[0].stepName != '') {
            // 表单审核驳回--视频和身份证之外的资料被驳回时（暂时两融有这种情况）
            this.rejectStepInfo.stepName = data.rejectStepInfo[0].stepName || ''
            // 列出驳回列表
            this.rejectBtnName = '重新修改'
          } else {
            this.rejectStepInfo.stepName = ''
            // 列出驳回列表
            this.rejectBtnName = '返回首页'
            this.isFormReject = true
            if (this.reHandleList.includes(this.$route.query.type)) {
              this.giveupBtnName = '重新办理'
            }
          }

          // 大宗交易等业务单独处理修复
          if (this.reFixedList.includes(this.$route.query.type)) {
            // 列出驳回列表
            this.rejectBtnName = '重新申请'
          }

          $h.setSession('rejectList', this.rejectList) // 保存驳回信息
        } else {
          _hvueAlert({
            mes: data.error_info
          })
        }
      })
    }
  },
  destroyed() {
    this.$store.commit('updateBusinessNextBtnStatus', true)
    this.$store.commit('updateIsShowHead', true)
    this.$bus.off('pageBack') // 事件销毁，防止多次触发
    this.$bus.off('giveUp') // 事件销毁，防止多次触发
    $h.clearSession('agreementIds')
  }
}
</script>
