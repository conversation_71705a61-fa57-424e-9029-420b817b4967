<template>
  <div class="glgxUploadCase">
    <div>
      <h5 class="title">途径一：中国结算官方网站</h5>
    </div>
    <div>
      <p class="tip">
        中登官网中国结算-投资者服务专区（chinaclear.cn）:
        证券查询服务➝证券持有余额➝点击选择证券账户➝点击获取电子凭证➝
        点击发送凭证至邮箱➝投资者可在邮箱自行下载，下载完成后在电脑上打开，
        使用手机拍照上传，或将文件传至手机上，截图进行上传。
      </p>
      <div class="upload_cont">
        <div class="upload_pic">
          <div class="pic">
            <img src="@/assets/images/sl_img.png"/>
          </div>
          <div class="pic">
            <img src="@/assets/images/sl_img.png"/>
          </div>
          <div class="pic">
            <img src="@/assets/images/sl_img.png"/>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import getImgBoxBrowser from '@/components/getImg_browser';
import { uploadImg } from '@/service/comServiceNew'
import showImage from '../common/showImage' // 展示图片
export default {
  components: {
    getImgBoxBrowser,
    showImage,
  },
  props: ['pageParam'],
  data () {
    return {
      exampleArr: [
        {
          id: 1,
          imgSrc: (process.env.NODE_ENV == 'development' ? '' : ROUTER_BASE) +
              '/static/images' + '/nashui.jpg',
        },
      ], // 示例列表
      isShow: false, // 是否展示图片详情
    };
  },
  created () {
  },
  activated () {

  },
};
</script>
<style>
  .glgxUploadCase{
    padding:0.2rem;
    overflow-y: auto;
  }
  .glgxUploadCase .tip{
    color:#7d7a7a;
  }
  .glgxUploadCase .pic{
    margin-top:0.1rem;
  }
</style>