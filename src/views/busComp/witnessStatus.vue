<template>
  <section v-show="showWitnessStatus" class="main fixed">
    <article class="content">
      <div class="queue_box" v-if="queueStatus === 1">
        <div class="queue_level">
          <i class="bg"></i>
          <div class="pic">
            <img src="@/assets/images/queue_cs.png" />
          </div>
        </div>
        <h5>坐席忙碌中，请稍等…</h5>
      </div>
      <div class="queue_box" v-else-if="queueStatus === 2">
        <div class="queue_level">
          <i class="bg"></i>
          <div class="pic">
            <img src="@/assets/images/queue_cs.png" />
          </div>
          <span class="num">{{ queueLocation }}</span>
        </div>
        <h5>正在排队中…</h5>
        <p>您当前排在第{{ queueLocation }}位</p>
      </div>
      <div class="queue_box" v-else-if="queueStatus === 3 || queueStatus === 4">
        <div class="queue_level">
          <i class="bg"></i>
          <div class="pic">
            <img src="@/assets/images/queue_cs.png" />
          </div>
        </div>
        <h5>
          {{ queueStatus === 3 ? '等待坐席进入…' : '等待坐席确认...' }}
        </h5>
      </div>
      <div class="ce_btn mt20" v-throttle>
        <a class="ui button block rounded" @click.stop="cancelQueueClick">取消排队</a>
      </div>
    </article>
    <witnessThs ref="ths" @videoCallBack="videoCallBack"></witnessThs>
  </section>
</template>

<script>
import { videoQueue, cancelQueue } from '@/service/comServiceNew';
import witnessThs from '@/components/witness_ths';

export default {
  name: 'witnessStatus',
  components: {
    witnessThs
  },
  props:{
    serivalId: {
      type: String,
      default: ''
    },
    businessCode: {
      type: String,
      default: ''
    },
    bizType: {
      type: String,
      default: ''
    },
    chooseAccount:{
      type: String,
      default: ''
    },
    showWitnessStatus: {
      type: Boolean,
      default() {
        return false;
      }
    }
  },
  data() {
    return {
      platform: $hvue.env, // 渠道类型
      videoType: $hvue.config.videoType, // 1anychat 0 tchat
      userInfo: $h.getSession('ygtUserInfo', { decrypt: false }),
      queueLocation: '-',
      queueInterval: null,
      queueStatus: 1, // 排队状态 1：客服忙碌中 2：排队中 3:接入中 4：坐席确认中 5:已接通
      fromTHSGB: $h.getSession('fromTHSGB'), // 渠道同花顺公版
    };
  },
  created() {
    if(this.showWitnessStatus){
      this.start();
      this.SetH5DealBackEventSure(true);
      //注册返回事件
      this.registerBackEvent();
    }
  },
  methods: {
    start() {
      this.queue();
      this.queueInterval = setInterval(this.queue, 2000);
    },
    queue() {
      let param = {
        mobile_no: this.userInfo.mobile,
        id_no: this.userInfo.identityNum,
        user_id: this.userInfo.userId,
        branch_id: this.userInfo.branchNo,
        branchno: this.userInfo.branchNo,
        user_name: this.userInfo.name,
        origin: $hvue.platform,
        client_id: this.userInfo.userId,
        client_name: this.userInfo.name,
        videoType: this.fromTHSGB ? '1' : this.videoType,
        // business_name: '非现场开户',
        biz_type: this.bizType,
        business_type: this.bizType,
        business_code: this.businessCode,
        business_id: this.serivalId,
        useTsyp: $hvue.config.useTYSP
      };

      videoQueue(param, {isShowWait: '0'})
      .then(data => {
        if (data.error_no === '0') {
          if (data.results[0].staff_exist === 'true') {
            if (data.results[0].queue_location === '0') {
              // 开始视频
              this.queueStatus = 3;
              clearInterval(this.queueInterval);
              let roomInfoArr = data.results[0].server_roomNo.split(':');
              if (this.fromTHSGB) {
                _hvueToast({ mes: '开始接入' });
                this.$refs.ths.start(roomInfoArr);
              }
            } else if (data.results[0].queue_location === '-1') {
              this.queueStatus = 4; // 坐席确认中
            } else {
              this.queueLocation = data.results[0].queue_location; // 排队中
              this.queueStatus = 2;
            }
          } else {
            this.queueStatus = 1; // 无坐席
          }
        } else {
          _hvueToast({
            mes: data.error_info
          });
        }
      })
      .catch(e => {
        if (e.message === 'Network Error') {
          clearInterval(this.queueInterval);
        } else {
          console.log(e);
        }
      });
    },
    cancelQueueClick() {
      var param = {
        user_id: this.userInfo.userId,
        branch_id: this.userInfo.branchNo,
        branchno: this.userInfo.branchNo,
        user_name: this.userInfo.name,
        origin: $hvue.platform,
        client_id: this.userInfo.userId,
        client_name: this.userInfo.name,
        biz_type: this.bizType,
        business_type: this.bizType,
        business_code: this.businessCode,
        videoType: this.videoType,
        // useTsyp: $hvue.customConfig.video.useTysp
      };
      cancelQueue(param)
      .then(data => {
        if (data.error_no === '0') {
          this.SetH5DealBackEventSure()
          // this.$router.replace({ name: 'videoPage' });
          this.$emit('cancelQueueCB')
        } else {
          _hvueToast({ mes: data.error_info });
        }
      })
      .catch(e => {
        if (e.message === 'Network Error') {
          clearInterval(this.queueInterval);

          this.$router.replace({
            name: 'witnessResult',
            params: { pageStatus: 3, rejectStatus: 2 }
          });
        } else {
          console.log(e);
        }
      });
    },
    cleanData() {
      clearInterval(this.queueInterval);
      this.queueStatus = 1;
    },
    videoCallBack(param) {
      this.cancelQueueClick();
      this.queueStatus = 1;
    },
    //告诉原生H5要接管返回事件
    SetH5DealBackEventSure(isH5DealBackEvent = false) {
      //调用原生方法，告诉原生H5是否接管返回事件
      callNativeHandler(
          "JSWangTingEvent",
          {
            action: "SetH5DealBackEvent",
            param: {"isDealBackEvent": isH5DealBackEvent}
          },
          function (callback) {
          }
      );
    },
    //注册返回事件
    registerBackEvent() {
      let self = this;
      registerWebHandler(
          "JSWangTingEvent",
          function (data) {
            if (data.action == "ClickNavBackBtn") {
              _hvueConfirm({
                mes: '确定返回？',
                opts: [
                  {
                    txt: '取消',
                    color: false
                  },
                  {
                    txt: '确定',
                    color: true,
                    callback: () => {
                      self.cancelQueueClick();
                    }
                  }
                ]
              });
            }
          }
      );
    }
  },
  destroyed() {
    this.cleanData();
  },
  deactivated() {
    this.cleanData();
  }
};
</script>
