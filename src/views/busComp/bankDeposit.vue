<!-- 三方存管 -->
<template>
  <div>
    <headComponent headerTitle="证券银行卡"></headComponent>
    <div v-if="!showCardDetails">
      <ul class="sele_card">
        <li v-for="(it,index) in userBanks" :key="index">
          <div class="card_cont" @click.stop="showCard(it)">
            <span v-if="it.mainFlag == '1'" class="card_spel">主卡</span>
            <dl class="card_info">
              <dt>
                <img :src="it.smallImg" />
              </dt>
              <dd>
                <h5>{{it.bankFullName}}</h5>
                <p>储蓄卡</p>
              </dd>
            </dl>

            <p v-if="it.contractStatus == '1'" class="tips">该卡已做预指定，请前往银行继续办理</p>
            <div v-else class="card_number">{{it.bankAccount|formatBankCardNo}}</div>

            <div v-show="it.contractStatus == '4'" class="card_shadow">
              <span>
                <strong>重新绑定</strong>更换过程中，新银行卡绑定失败
              </span>
            </div>
          </div>
        </li>
      </ul>
      <div class="add_cardbtn" v-show="pageParam.length==0">
        <a @click.stop="add_card">添加银行卡</a>
      </div>
    </div>
    <div v-else>
      <div class="bank_infobox">
        <ul>
          <li>
            <span class="tit">开户银行</span>
            <p>{{cardDetails.bankFullName}}</p>
          </li>
          <li>
            <span class="tit">银行卡号</span>
            <p>{{cardDetails.bankAccount|formatBankCardNo}}</p>
          </li>
          <li>
            <span class="tit">卡片类型</span>
            <p>储蓄卡</p>
          </li>
          <li>
            <span class="tit">币种类型</span>
            <p>{{cardDetails.moneyTypeCn}}</p>
          </li>
        </ul>
      </div>
      <div class="ce_btn mt20">
        <a v-throttle class="ui button block rounded" @click.stop="change_card">更变银行卡</a>
        <!-- <a v-throttle class="ui button block rounded border mt15" href="#">设为主卡</a> -->
      </div>
    </div>
  </div>
</template>

<script>
import headComponent from '@/components/headComponent' // 头部
import { queryDictionary } from '@/common/util'
export default {
  components: {
    headComponent
  },
  props: ['pageParam'],
  data () {
    return {
      ygtUserInfo: $h.getSession('ygtUserInfo', {decrypt: false}),
      userBanks: [],
      showCardDetails: false, // 展示银行卡详情
      cardDetails: {} // 当前展示的绑定银行卡信息
    }
  },
  activated () {
    this.userBanks = []
    this.$store.commit('updateIsShowHead', false) // 隐藏头部
    this.$store.commit('updateBusinessNextBtnStatus', false) // 隐藏下一步按钮
    for (let j = 0; j < this.pageParam.length; j++) {
      let menu = this.pageParam[j]
      let flag = this.isValidUUID(menu.smallImg)
      if (flag) {
        menu.smallImg = $hvue.config.imgUrl + menu.smallImg
      } else {
        menu.smallImg =
        (process.env.NODE_ENV == 'development' ? '' : ROUTER_BASE) +
        '/static/images' +
        menu.smallImg
      }
      this.userBanks.push(menu)
    }
  },
  mounted () {
    window.phoneBackBtnCallBack = this.pageBack
  },
  methods: {
    showCard (it) {
      queryDictionary({ type: 'ismp.moneyType', key: it.moneyType }, data => {
        it.moneyTypeCn = data.value
        Object.assign(this.cardDetails, it)
        this.showCardDetails = true
      })
    },
    // 添加银行卡
    add_card () {
      this.$router.push({
        name: 'business',
        query: { type: 'cgyhtj', name: '存管银行添加' }
      })
    },
    // 变更银行卡
    change_card () {
      _hvueConfirm({
        title: '提示',
        mes: '变更银行卡会先取消您原银行卡绑定</br>是否继续？',
        opts: () => {
          // 确定之后的回调
          $h.setSession('cardDetails', this.cardDetails)
          this.$router.push({
            name: 'business',
            query: {
              type: 'cgyhbg',
              name: '存管银行变更'
            },
            params: this.cardDetails
          })
        }
      })
    },
    // 返回
    pageBack () {
      this.$router.push({ name: 'index', params: {} })
    },
    isValidUUID (uuid) {
      // UUID校验
      if (uuid == null) {
        return false
      }
      let regex = '^[0-9a-f]{8}[0-9a-f]{4}[0-9a-f]{4}[0-9a-f]{4}[0-9a-f]{12}$'
      if (uuid.match(regex)) {
        return true
      }
      return false
    }
  },
  deactivated () {
    this.$store.commit('updateIsShowHead', true) // 隐藏头部
  }
}
</script>
