<template>
  <div class="investor_condition">
<!--      <headComponent headerTitle="内部条件判断"></headComponent>-->
    <article class="content">
      <h5 class="com_title">请根据您的实际情况选择资产认证方式</h5>
      <ul class="p_check_list">
        <li :key="idx" v-for="(itm, idx) in typeArr[type]" @click.prevent="getActiveId(idx)">
          <div class="base"><span class="icon_radio" :class="{'checked': activeId === idx}">{{itm.value1}}</span></div>
          <div class="upload_txtcont" v-show="activeId === idx">
            <p>{{itm.value2}}</p>
            <p>{{itm.value3}}</p>
          </div>
        </li>
      </ul>
    </article>
  </div>
</template>
<script>
import {pictureAmount} from '@/service/comServiceNew';
import headComponent from '@/components/headComponent'
export default {
  components: {
    headComponent,
  },
  props: ['pageParam'],
  data () {
    return {
      type: this.$route.query.type,
      activeId: -1, // 活动Id
      typeArr: {
        zghgtzz: [
            {
              id: '0',
              value1: '方式一：近3年年均收入认证',
              value2: '需要您提供您个人近3年年均收入达到40万的相关证明材料。',
              value3: '收入证明的相关材料包括：单位收入证明、银行交易流水工资收入、个人收入纳税证明等。',
            },
            {
              id: '1',
                  value1: '方式二：家庭金融资产或净资产认证',
                value2: '需要您提供您家庭在所有相关金融机构拥有的金融资产超过500万的证明，不足500万元，请提供征信报告。',
                value3: '相关金融资产证明材料包括：证券资产、银行存款、债券、基金、信托计划、资管计划、保险、其他金融品种的对账单、确认书、合同、份额证明书等。',
            },
        ],
        smhgtzz: [
          {
            id: '0',
            value1: '方式一：近3年年均收入认证',
            value2: '需要您提供您个人近3年年均收入达到50万或者近1年收入达到150万的相关证明材料。',
            value3: '收入证明的相关材料包括：单位收入证明、银行交易流水工资收入、个人收入纳税证明等。',
          },
          {
            id: '1',
            value1: '方式二：金融资产认证',
            value2: '需要提供您个人在所有相关金融机构拥有的金融资产超过300万的证明。',
            value3: '相关金融资产证明材料包括：证券资产、银行存款、债券、基金、信托计划、资管计划、保险、其他金融品种的对账单、确认书、合同、份额证明书等。',
          },
        ],
        zytzzrd: [
          {
            id: '0',
            value1: '方式一：近3年个人年均收入证明',
            value2: '拟申请B类专业投资者要求近3年个人年均收入不低于50万元',
            value3: '',
          },
          {
            id: '1',
            value1: '方式二：金融资产证明',
            value2: '拟申请B类专业投资者要求金融资产不少于500万元。',
            value3: '',
          },
        ],
      },
    };
  },
  activated () {
    if (this.pageParam[0] && this.pageParam[0].zytzzrdOpenType) {
      const profType = this.pageParam[0].zytzzrdOpenType === '1' ? 'B' : 'C'
      this.typeArr.zytzzrd[0].value2 = `拟申请${profType}类专业投资者要求近3年个人年均收入不低于${profType === 'B' ? '50' : '30'}万元`
      this.typeArr.zytzzrd[1].value2 = `拟申请${profType}类专业投资者要求金融资产不少于${profType === 'B' ? '500' : '300'}万元`
    }
    // let str = JSON.stringify(this.pageParam[0]);
    // if (str.indexOf('0') != -1) {
    //   this.nextFlag = false;
    //   this.$store.commit('updateNextBtnText', '返回首页');
    //   this.errorDesc = '抱歉，您不满足基本条件，暂时不能办理';
    // } else {
    //   this.nextFlag = true;
    //   this.$store.commit('updateNextBtnText', '下一步');
    //   this.errorDesc = '恭喜您，满足全部基本条件';
    // }
  },
  created () {
    this.pictureAmount()
  },
  methods: {
    doCheck () {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        if (this.activeId === -1) {
          _hvueToast({ mes: '请选择您的资产认证方式' })
          return false;
        } else {
          // 可以下一步
          resolve();
        }
      });
    },
    /**
     * 请求参数打包
     */
    reqParamPackage () {
      // 业务请求参数封装
      let params = {
        param1: '',
        param2: '',
        assetsMaterialId: this.activeId,
      };
      return params;
    },
    putFormData () {
      let formData = {
      };
      return formData;
    },
    getActiveId (id) {
      this.activeId = id;
    },
    pictureAmount () {
      let param = {
        enumNo: 'picture_amount'
      }
      pictureAmount(param)
        .then((data) => {
          if (data.error_no === '0') {
            let result = data.DataSet[0] || {}
            $h.setSession('pictrueAmount', result.itemValue)
          } else {
            return Promise.reject(new Error(data.error_info))
          }
        })
        .catch((e) => {
          _hvueToast({ mes: e.message })
        })
    }
  },
};
</script>
