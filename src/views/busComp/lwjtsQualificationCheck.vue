<template>
  <div>
    <h5 class="com_title">请选择以下需要开通权限的账户</h5>
    <div class="acct_spbox mt10">
      <ul>
        <li
          :class="{disable:item.holderStatus!=0, checked : selectedIndexes.indexOf(item.stockAccount)!=-1}"
          v-for="(item,index) in showAccountList"
          :key="index"
          @click.stop="addCheckedClass(index,item,(item.openStatus == '1' || item.holderStatus != '0') ? false : true)"
        >
          <p>{{item.stockAccount}}</p>
          <em>{{item.exchangeType === '9' ? '股转' : '深A'}}</em>
          <span class="state">{{item.holderStatus!=0 ? item.holderName : ""}}</span>
        </li>
      </ul>
    </div>
  </div>
</template>
<script>
export default {
  props: ["pageParam"],
  data() {
    return {
      currentChecked: "-1", // 当前选中的账户index
      checkedAccount: "", // 选中的账号,
      showAccountList: [], // 展示的账户列表
      continueFlag: false, // 是否可以办理开通股转账户标识
      selectAccountList: [], // 选择开通的账号
      selectedIndexes: [], // 已经选中的index
    };
  },
  activated() {
    $h.clearSession('hasGzzhFlag');
    if (this.pageParam[0].transferList && this.pageParam[0].transferList !== '[]') {
      // 有股转账户
      this.showAccountList = JSON.parse(this.pageParam[0].transferList);
      $h.setSession('hasGzzhFlag', true);// 保存股转账户标记
    } else {
      this.showAccountList = JSON.parse(this.pageParam[0].accountList);
        $h.setSession('hasGzzhFlag', false);// 保存股转账户标记
    }

      for (let index = 0; index < this.showAccountList.length; index++) {
          const element = this.showAccountList[index];
          if (element.holderStatus == 0 && element.market == "00") {
              this.continueFlag = true;
          }
      }

    if (this.continueFlag) {
      // 可以办理开通股转账户
      this.$store.commit("updateNextBtnText", "下一步"); // 修改下一步按钮
    } else {
      // 无法继续办理
      this.$store.commit("updateNextBtnText", "返回首页"); // 修改下一步按钮
    }
    this.currentChecked = -1;
    this.checkedAccount = "";
  },
  methods: {
    addCheckedClass(index, item, enableChecked) {
      if (!enableChecked) {
        return;
      }

      if (this.selectedIndexes.includes(item.stockAccount)) { // 已经选中则移除
          this.selectedIndexes.remove(item.stockAccount);
      } else { // 未选则添加
          this.selectedIndexes.push(item.stockAccount)
      }
    },
    checkSubmit() {
      if (!this.selectedIndexes.length) {
        _hvueToast({
          mes: "请选择账户"
        });
        return false;
      }

      this.selectAccountList = this.showAccountList.map((item) => {
          if(this.selectedIndexes.includes(item.stockAccount)){
              let ite = {
                  exchangeType: item.exchangeType,
                  market: '00',
                  stockAccount: item.stockAccount
              }
              return ite;
          }
      })
      this.selectAccountList = this.selectAccountList.filter(item => item)
      return true;
    },
    doCheck() {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        if (this.continueFlag) {
          if (this.checkSubmit()) {
            // 可以下一步
            resolve();
          }
        } else {
          // 无法继续办理
          this.$router.push({ name: "index" });
        }
      });
    },
    /**
     * 请求参数打包
     */
    reqParamPackage() {
      // 业务请求参数封装
      let params = {};
      return params;
    },
    putFormData() {
      let formData = {
        chooseAccount: this.selectAccountList
      }; // 需要保存的表单数据
      return formData;
    }
  }
};
</script>
