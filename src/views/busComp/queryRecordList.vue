<template>
  <div>
    <div>
      <tab-change
          :tabList="tabList"
          :activeKey="activeKey"
          :isShow="true"
          @changeTab="changeTab">
      </tab-change>
      <article class="content">
        <table-list-dzjy
            :isShow="true"
            :tableList="tableList"
            :activeKey="activeKey"
            @showDetail="showDetail">
        </table-list-dzjy>
      </article>
      <div class="ce_btn" v-show="activeKey == 0">
        <a v-throttle class="ui button block rounded" @click.stop="nextStep"
        >发起申请</a>
      </div>
    </div>
  </div>
</template>

<script>
import tabChange from '@/components/tab' // tab
import tableListDzjy from '@/components/tableListDzjy' // tableListDzjy
import {
  queryRecordList
} from '@/service/comServiceNew'

export default {
  props: ['pageParam'],
  components: {
    tabChange,
    tableListDzjy,
  },
  data() {
    return {
      ygtUserInfo: $h.getSession('ygtUserInfo', {decrypt: false}),
      activeKey: 0,
      failserivalId: '',
      tabList: [
        {
          type: '0',
          name: '申请中',
        },
        {
          type: '1',
          name: '已完成',
        },
        {
          type: '2',
          name: '申请失败',
        },
      ],
      tableList: [],
      cName: 'blockTrade',
    };
  },
  created () {
    $h.clearSession('result_serivalId')
  },
  activated () {
    this.$store.commit('updateBusinessNextBtnStatus', false) // 隐藏下一步按钮
    // 返回的tab键
    this.$bus.on('isActiveTabkey', (key) => {
      this.activeKey = key;
    })
    this.queryRecordList(this.activeKey, '');
  },
  watch: {
    activeKey (val) {
      if (!this.$route.query.type) return;
      this.queryRecordList(val);
    }
  },
  methods: {
    // 查询数据
    queryRecordList (queryFlag, serivalId = '') {
      queryRecordList({
        queryFlag: queryFlag,
        serivalId: serivalId,
        businessCode: this.$route.query.type,
        clientId: this.ygtUserInfo.clientId,
      }).then(
        res => {
          if (res.error_no === '0') {
            let _results = res.results ? res.results : res.DataSet;
            this.tableList = _results;
          } else {
            _hvueToast({
              icon: 'error',
              mes: res.error_info
            })
          }
        },
        err => {
          console.log(err)
        }
      )
    },
    // tab切换监听
    changeTab (item, key) {
      if (key == this.activeKey) return;
      this.tableList = [];
      this.activeKey = key;
    },
    // 展示详情
    showDetail (data) {
      let dzjyList = [],
          param = {}
      if (this.$route.query.type === 'gspt') {
        this.cName = 'recycleList'
      } else if (this.$route.query.type === 'dzjy' && this.ygtUserInfo.userType !== '0') {
        this.cName = 'blockTradeInstitution'
      }

      param.serivalId = data.serivalId;
      param.key = this.activeKey;

      if (this.activeKey == 2) {
        // this.cName = 'businessResultDG';
        // dzjyList = JSON.parse(data.dzjyList);
        //
        // param.type = this.$route.query.type;
        // param.market = dzjyList.market;
        // param.trdaccount = dzjyList.trdaccount
        $h.setSession('result_serivalId', param.serivalId)
        this.failserivalId = param.serivalId;
        this.nextStep();

      } else {
        this.$router.push({
          name: this.cName,
          query: param
        })
      }
    },
    nextStep() {
      this.businessCode = this.$route.query.type;
      this.$parent.emitNextEvent();
    },
    doCheck() {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        // 可以下一步
        resolve();
      });
    },
    /**
     * 请求参数打包
     */
    reqParamPackage() {
      // 业务请求参数封装
      let params = {
        businessCode: this.businessCode,
      }; // 提交需要的参数

      if (this.activeKey == 2) {
        params.resultFlag = '1';
        params.serivalId1 = this.failserivalId;
      }
      return params;
    },
    putFormData() {
      let formData = {}; // 需要保存的表单数据
      return formData;
    }
  },
  deactivated(){
    this.activeKey = 0;
  }
};
</script>
