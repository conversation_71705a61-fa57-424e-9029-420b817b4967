<template>
  <div>
    <div class="test_result">
      <div class="test_level knowledge">
        <div class="test_canvas">
          <span style="display:none;" ref="zstestData">{{pageParam[0].paperScore}}</span>
          <div class="info">
            <strong>{{parseInt(pageParam[0].paperScore)}}</strong>
            <span>测评得分</span>
          </div>
          <canvas ref="zstestCanvas" width="320" height="320"></canvas>
        </div>
        <h5 v-if="pageParam[0].knowledgeQuesPassFlag!='1'" class="error">
          抱歉！你的测评分数过低
          <br />请重新评测
        </h5>
        <h5 v-else-if="pageParam[0].knowledgeQuesPassFlag=='1'">恭喜你通过测评</h5>
        <p
          v-if="this.$parent.flow.currentStepInfo.flow_current_step_name == 'creditInvestigation'"
        >该分数为根据您当前情况预估的征信分数，最终征信分数以您临柜申请后公司审批为准。</p>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: ['pageParam'],
  name: 'knowledgeQuesResult',
  data () {
    return {
      serivalId: this.$parent.publicParam.serivalId
    }
  },
  activated () {
    if (this.pageParam[0].knowledgeQuesPassFlag === '1') {
      this.$store.commit('updateNextBtnText', '下一步') // 修改下一步按钮
    } else {
      this.$store.commit('updateNextBtnText', '重新测评') // 修改下一步按钮
    }
    this.drawCanvas()
  },
  methods: {
    drawCanvas () {
      let canvas1 = this.$refs.zstestCanvas
      let ctx1 = canvas1.getContext('2d')
      let W1 = canvas1.width
      let H1 = canvas1.height
      let deg1 = 0,
        new_deg1 = 0,
        dif1 = 0
      let loop1, re_loop1
      let t_data1 = this.$refs.zstestData.innerHTML
      let deColor1 = '#ffffff',
        dotColor1 = '#B80205'

      function init1 () {
        ctx1.clearRect(0, 0, W1, H1)
        ctx1.beginPath()
        ctx1.strokeStyle = deColor1
        ctx1.lineWidth = 5
        ctx1.arc(
          W1 / 2,
          H1 / 2 + 30,
          130,
          (Math.PI * 5) / 6,
          (Math.PI * 39) / 18,
          false
        )
        ctx1.stroke()

        let r1 = (2.4 * deg1 * Math.PI) / 180
        ctx1.beginPath()
        ctx1.strokeStyle = dotColor1
        ctx1.lineWidth = 5
        ctx1.arc(
          W1 / 2,
          H1 / 2 + 30,
          130,
          (Math.PI * 5) / 6,
          r1 + (Math.PI * 5) / 6,
          false
        )
        ctx1.stroke()

        let r2 = (2.4 * deg1 * Math.PI) / 180 + (Math.PI * 1) / 3
        ctx1.beginPath()
        ctx1.fillStyle = dotColor1
        ctx1.arc(
          W1 / 2 - 130 * Math.sin(r2),
          H1 / 2 + 30 + 130 * Math.cos(r2),
          7,
          -180,
          true
        )
        ctx1.fill()
      }
      function draw1 () {
        new_deg1 = t_data1
        dif1 = new_deg1 - deg1
        loop1 = setInterval(to1, 500 / dif1)
      }
      function to1 () {
        if (deg1 == new_deg1) {
          clearInterval(loop1)
        }
        if (deg1 < new_deg1) {
          deg1++
        }
        init1()
      }
      draw1()
    },
    doCheck () {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        resolve()
      })
    },
    /**
     * 请求参数打包
     */
    reqParamPackage () {
      return {}
    },
    putFormData () {
      let formData = {
        questionSubmit: {
          serivalId: this.serivalId,
          paperScore: this.pageParam[0].paperScore
        }
      }
      return formData
    }
  }
}
</script>
