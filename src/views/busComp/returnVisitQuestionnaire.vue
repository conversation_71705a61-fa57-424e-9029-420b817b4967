<!-- 问卷回访 -->
<template>
  <div>
    <div class="visit_list">
      <div class="visit_item"
        v-for="(questionItem, questionIndex) in questionList" :key="questionIndex">
        <h5><span class="num">{{ questionItem.questionNo }}、</span>{{ questionItem.questionContent }}</h5>
        <ul>
          <li v-for="(answerItem, answerIndex) in questionItem.answereArray"
            :key="answerIndex">
            <a class="icon_radio" :class="{ checked: answerItem.checked }"
              href="javascript:;" @click.stop="questionSelected(questionIndex, answerIndex)">{{ answerItem.answerContent }}</a></li>
        </ul>
        <div class="ui field text" v-if="questionItem.questionKind == 2">
          <multLineInput
              class="teare01 needsclick"
              v-model.trim="inputObj[questionIndex+'_'+questionItem.questionNo]"
              :maxlength="300"
          ></multLineInput>
        </div>
      </div>
    </div>
    <div class="ce_btn">
      <a class="ui button block rounded" v-throttle @click.stop="submit">提交</a>
    </div>
    <p v-show="$route.query.type === 'fxjskt'" style="padding:0.1rem 0.15rem;">【温馨提示】您在参与上海风险警示板交易前，请认真阅读并理解上海风险警示板业务的法律法规和业务规则等相关规定，对其他可能存在的风险因素也应当有所了解和掌握，并确信自己已做好足够的风险评估与财务安排，避免因参与上海风险警示板交易而遭受难以承受的损失和其他后果。</p>
  </div>
</template>
<script>
import multLineInput from '@/components/multLineInput'
export default {
  props: ['pageParam'],
  name: 'returnVisitQuestionnaire',
  components: {
    multLineInput
  },
  data() {
    return {
      questionList: [], // 问题
      answerList: [], // 答案
      totalScore: 0,
      inputObj:{} // 文本输入的内容
    }
  },
  created() {

  },
  mounted() {

  },
  activated() {
    this.$store.commit('updateBusinessNextBtnStatus', false) // 隐藏下一步按钮
    this.initPage()
    this.questionList = this.pageParam
    // this.questionList = this.questionList.filter(({ questionKind }) => questionKind === '0')
    this.questionList.forEach((item, index) => {
      item.selected = []
      if (item.questionKind !== '2') {
        item.answereArray = JSON.parse(item.answereArray)

        item.answereArray.forEach(item => {
          // item.checked = (item.isTrue === '1')
        })
      } else {
        this.inputObj[index + '_' + item.questionNo] = ''
      }
    })
  },
  deactivated() {
    this.answerList = []
  },
  methods: {
    initPage() {
      this.answerList = []
    },
    // 选择选项
    questionSelected(questionIndex, answerIndex) {
      this.questionList[questionIndex].answereArray.forEach((item, index) => {
        if (this.questionList[questionIndex].questionKind == '1') {
          if (index === answerIndex) {
            item.checked = !item.checked
          }
        } else {
          item.checked = false
          if (index === answerIndex) {
            item.checked = true
          }
        }

        this.$set(this.questionList[questionIndex].answereArray, index, item)
      })
    },
    // 检查是否所有问题都已选择
    checkAllQuestionSelected() {
      const wrongQuestion = this.questionList.filter(item => {
        let isTrueChecked = true
        if (item.answereArray.length) {
          item.answereArray.forEach(item => {
            if (item.checked && item.questionScore === '1') {
              isTrueChecked = false
            }
          })
        } else if (item.questionKind === '2'){
          isTrueChecked = false
        }
        return isTrueChecked
      })
      return wrongQuestion
    },
    submit () {
      // 调用父组件提交事件方法
      this.$parent.emitNextEvent()
    },
    /** ************子组件公共方法定义****** */
    doCheck() {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        const wrongQuestion = this.checkAllQuestionSelected().map(item => { return item.questionNo })
        // console.log(wrongQuestion)
        if (wrongQuestion.length === 0) {
          resolve()
        } else {
          _hvueAlert({ mes: `您的第${wrongQuestion.join('、')}题回访不符合适当性监管要求，无法为您办理，请您确认后重新提交，或您可拨打4007121212客服电话反馈。` })
        }
      })
    },
    /**
     * 请求参数打包
     */
    reqParamPackage() {
      // 业务请求参数封装
      let _this = this
      let paperAnswer = []
      this.totalScore = 0
      this.questionList.forEach(questionItem => {
        if (questionItem.questionKind !== '2') {
          questionItem.answereArray.forEach(item => {
            if (item.checked) {
              paperAnswer.push(`${questionItem.questionNo}_${item.answerNo}_${item.questionScore}`)
              this.totalScore += +item.questionScore
            }
          })
        } else {
          // 文本输入的答案处理 题号_内容_code
          let keys = Object.keys(_this.inputObj)
          if (keys.length) {
            for (let key of keys) {
              let keyArr = key.split('_'),
                  no = keyArr[1]
              paperAnswer.push(`${no + (_this.inputObj[key] ? ('_' + _this.inputObj[key]) : '_' + no)}_code`)
            }
          } else {
            paperAnswer.push(`${questionItem.questionNo}_${questionItem.questionNo}_code`)
          }
        }
      })

      let params = {
        paperType: this.questionList[0].paperTypes,
        subjectId: this.questionList[0].subjectId,
        paperAnswer: paperAnswer.join('|')
      }
      return params
    },
    putFormData() {
      let formData = {
        questionSubmit: {
          subjectId: this.questionList[0].subjectId,
          paperType: this.questionList[0].paperTypes,
          paperScore: this.totalScore
        }
      }
      return formData
    }
    /** ************子组件公共方法定义****** */
  }
}
</script>
