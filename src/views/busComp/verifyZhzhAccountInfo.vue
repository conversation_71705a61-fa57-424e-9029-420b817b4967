<template>
  <div class="user_main">
    <h5 class="com_title">请输入真实身份信息</h5>
    <div class="input_form">
      <div class="ui field text">
        <label class="ui label">姓名</label>
        <input
          v-model.trim="inputModel.name.value"
          :maxlength="inputModel.name.maxlength"
          type="text"
          class="ui input"
          placeholder="请输入姓名"
        />
        <a
          class="txt_close"
          @click.stop="inputModel.name.value=''"
          v-show="inputModel.name.value!=''"
        ></a>
      </div>
      <div class="ui field text">
        <label class="ui label">身份证号</label>
        <input
          v-model.trim="inputModel.idno.value"
          :maxlength="inputModel.idno.maxlength"
          type="text"
          class="ui input"
          placeholder="请输入身份证号"
        />
        <a
          class="txt_close"
          @click.stop="inputModel.idno.value=''"
          v-show="inputModel.idno.value!=''"
        ></a>
      </div>
    </div>
    <h5 class="com_title">请填写您预留的手机号码</h5>
    <div class="input_form">
      <div class="ui field text">
        <label class="ui label">手机号码</label>
        <input
            type="text"
            class="ui input"
            placeholder="输入手机号"
            v-model.trim="inputModel.mobile.value"
            :maxlength="inputModel.mobile.maxlength"
        />
        <a class="txt_close" @click.stop="inputModel.mobile.value=''" v-show="inputModel.mobile.value!=''"></a>
      </div>
      <div class="ui field text">
        <label class="ui label">图形验证码</label>
        <input
            v-model.trim="inputModel.imageCode.value"
            :maxlength="inputModel.imageCode.maxlength"
            type="text"
            class="ui input image-code"
            placeholder="请输入图形验证码"
        />
        <a
            class="txt_close"
            @click.stop="inputModel.imageCode.value=''"
            v-show="inputModel.imageCode.value!=''"
        ></a>
        <a class="code_img" @click.stop="getImgCode">
          <img :src="codeImgUrl" />
        </a>
      </div>
      <div class="ui field text code">
        <div class="ui label">验证码</div>
        <input
            v-model="inputModel.verifyCode.value"
            :maxlength="inputModel.verifyCode.maxlength"
            type="text"
            class="ui input"
            placeholder="请输入6位短信验证码"
        />
        <a
            class="txt_close"
            @click.stop="inputModel.verifyCode.value=''"
            v-show="inputModel.verifyCode.value!=''"
        ></a>
        <smsTimer v-model="startFlag" @sendSms="verifyImgCode"></smsTimer>
      </div>
    </div>
    <div class="bottom_check">
      <p class="tips">温馨提示: 如果预留手机号不正确或无法接收验证码，请致电4007-121212转人工服务或者前往就近营业部办理</p>
      <p class="tips">提供的证件信息仅用于身份实名验证，我们将严格保护您的个人隐私安全，并仅用于此目的。</p>
    </div>
    <div class="ce_btn mt20">
      <a v-throttle class="ui button block rounded" @click.stop="verifyAccountInput">下一步</a>
    </div>
  </div>
</template>

<script>
import { checkInput, queryDictionary } from '@/common/util'
import smsTimer from '@/components/smsTimer'
import { getImgCode, sendMobileCode, verifyImgCode } from '@/service/comServiceNew'
export default {
  components: {
    smsTimer
  },
  data () {
    return {
      handleResult: true,
      loginType: $h.getSession('loginType') || '1', // 账号类型 1普通账号 2信用账号
      ygtUserInfo: {},
      smsNo: '', // 修改手机号短信类型数据字典
      startFlag: false,
      sendStatu: 1, // 1: 显示发送按钮 2: 显示倒计时 3:显示重新发送
      inputModel: {
        name: {
          name: '姓名',
          value: '',
          maxlength: '15',
          minlength: '2',
          format: 'name'
        },
        idno: {
          name: '身份证号',
          value: '',
          minlength: '18',
          maxlength: '18',
          format: 'idno'
        },
        mobile: {
          name: '手机号',
          value: '',
          maxlength: '11',
          minlength: '11',
          format: 'phone'
        },
        imageCode: {
          name: '图片验证码',
          value: '',
          maxlength: '4',
          minlength: '4',
          format: 'enNum'
        },
        verifyCode: {
          name: '验证码',
          value: '',
          maxlength: '6',
          minlength: '6',
          format: 'enNum'
        }
      },
      codeImgUrl: '', // 图片验证码图片url
      codeImgKey: ''
    }
  },
  activated () {
    this.$store.commit('updateBusinessNextBtnStatus', false) // 隐藏下一步按钮
    this.getImgCode() // 获取验证码
    this.queryDictionary()
    // 清空输入框
    for (const key in this.inputModel) {
      if (this.inputModel.hasOwnProperty(key)) {
        const element = this.inputModel[key]
        element.value = ''
      }
    }
    // this.inputModel.account.placeholder = this.loginType == '1' ? '请输入资金账户' : '请输入信用资金账户';
  },
  methods: {
    // 查询短信类型数据字典
    queryDictionary () {
      queryDictionary(
        { type: 'ismp.sms_type', value: this.$parent.businessName },
        data => {
          debugger
          this.smsNo = data.key
        })
    },
    // 获取图片验证码
    getImgCode () {
      getImgCode({}, {}).then(
        res => {
          if (res.error_no === '0') {
            let results = res.results
              ? res.results[0]
              : res.generateVerifyCode[0]
            this.codeImgUrl = results.imageCode
            this.codeImgKey = results.mobileKey
            this.inputModel.imageCode.value = ''
          } else {
            _hvueToast({
              icon: 'error',
              mes: res.error_info
            })
          }
        },
        err => {
          console.log(err)
        }
      )
    },
    // 校验图片验证码
    verifyImgCode () {
      let _mobile = this.inputModel.mobile
      let _imageCode = this.inputModel.imageCode
      let mobileFlag = checkInput(_mobile)
      if (mobileFlag != '') {
        return
      }
      let imageCodeFlag = checkInput(_imageCode)
      if (imageCodeFlag != '') {
        return
      }
      if (_mobile.value == this.ygtUserInfo.mobile) {
        _hvueToast({ mes: '新手机号与旧手机号一样，无需修改' })
        return
      }
      verifyImgCode(
        { mobileKey: this.codeImgKey, imageCode: this.inputModel.imageCode.value },
        {}
      ).then(
        res => {
          if (res.error_no === '0') {
            // 调用父组件的下一步事件
            this.sendMsg()
            // this.$parent.emitNextEvent()
          } else {
            _hvueToast({
              icon: 'error',
              mes: res.error_info
            })
            this.getImgCode()
          }
        },
        err => {
          console.log(err)
        }
      )
    },
    // 发送验证码
    sendMsg () {
      sendMobileCode({
        flow_name: this.$route.query.type,
        // userId: this.ygtUserInfo.userId,
        mobile: this.inputModel.mobile.value,
        smsNo: this.smsNo,
        businessCode: this.$route.query.type
      })
        .then(data => {
          if (data.error_no === '0') {
            // 开启倒计时
            // this.startCountDown()
            this.startFlag = true
            this.sendStatu = 2
          } else {
            _hvueToast({ mes: data.error_info })
          }
        })
        .catch(e => {
          _hvueToast({ mes: e.message })
        })
    },
    /**
     * 提交前校验格式，组装参数
     */
    verifyAccountInput () {
      let inputs = Object.values(this.inputModel)
      let flag = ''
      for (let s = 0; s < inputs.length; s++) {
        let el = inputs[s]
        flag = checkInput(el)
        if (flag != '') {
          break
        }
      }
      if (flag) {
        _hvueToast({
          mes: flag
        })
        return
      }
      if (this.sendStatu == 1) {
        _hvueToast({
          mes: '您还未获取验证码'
        })
        return
      }
      // 调用提交信息
      this.$parent.emitNextEvent()
    },
    doCheck () {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        // 可以下一步
        resolve()
      })
    },
    /**
     * 请求参数打包
     */
    reqParamPackage () {
      // 业务请求参数封装
      let params = {
        mobile: this.inputModel.mobile.value,
        userName: this.inputModel.name.value,
        identityNum: this.inputModel.idno.value,
        verifyCode: this.inputModel.verifyCode.value,
        smsNo: this.smsNo
      } // 提交需要的参数
      return params
    },
    putFormData () {
      let formData = {
        verifyAccountInfo: {
          mobile: this.inputModel.mobile.value,
          userName: this.inputModel.name.value,
          identityNum: this.inputModel.idno.value,
          verifyCode: this.inputModel.verifyCode.value,
          smsNo: this.smsNo
        }
      } // 需要保存的表单数据
      return formData
    },
    // 提交后对结果进行处理的方法
    async handleResultFunc (res) {
      this.zhzhUserInfo = res.userInfo[0]
      // this.ygtUserInfo.name = this.inputModel.name.value
      Object.assign(this.$parent.publicParam, this.zhzhUserInfo)
      this.$parent.publicParam.serivalId = res.results[0].serivalId
      this.$parent.flow.serivalId = res.results[0].serivalId
      $h.setSession('zhzhUserInfo', this.zhzhUserInfo, {encrypt: false})
      // 1、查询表单状态
      // let formStatusResult = await queryUserFlowStatus({
      //   userId: this.ygtUserInfo.userId,
      //   businessCode: this.$router.query.type
      // })
      // if (formStatusResult.error_no === '0') {
      //   let formStatus = formStatusResult.results[0].formStatus
      // } else {
      //   _hvueAlert({ mes: formStatusResult.error_info })
      // }
      this.$parent.$emit('init')
    }
  }
}
</script>
<style>
.image-code{
  padding-left: 0.85rem!important;
}
</style>
