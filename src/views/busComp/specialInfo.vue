<template>
  <div class="page_main" v-cloak>
    <headComponent :headerTitle="'特殊信息申报'+(currentIndex+1)+'/5'"></headComponent>
    <article class="content" ref="specialPage">
      <div
        class="special_sb_base"
        v-for="(item,index) in questionList"
        :key="index"
        v-show="currentIndex == index"
      >
        <h5 class="title">{{item.title}}</h5>
        <ul class="list">
          <li v-for="(ansItem,ansIndex) in item.answerList" :key="ansIndex" @click.stop="bindCheck">
            <span class="icon_radio">{{ansItem.answerName}}</span>
          </li>
        </ul>
      </div>
      <!--特殊信息申报表单组件-->
      <specialInfoForm
        v-show="formShow"
        :initData="formInitData"
        v-model="formLength"
        :title="formTitle"
        ref="infoForm"
      ></specialInfoForm>
      <div class="add_com_btn" v-show="formShow">
        <a v-throttle class="ui button block" @click.prevent="continueAdd">继续添加</a>
      </div>
      <div class="bottom_check">
        <div class="ce_btn">
          <a v-throttle class="ui button block rounded" @click.prevent="nextItem">{{nextItemText}}</a>
        </div>
      </div>
    </article>
  </div>
</template>
<script>
import specialInfoForm from '@/components/specailInfoForm'
import headComponent from '@/components/headComponent' // 头部
export default {
  components: {
    headComponent,
    specialInfoForm
  },
  data () {
    return {
      currentIndex: 0, // 当前题目的编号
      checkedIndex: -1, // 当前选中的答案的index  -1表示未选中
      formLength: 1, // 表单加载的条数记录
      formInitData: [], // 加载表单的数据
      relationInfo: [], // 关联人信息
      companyPercent: [], // 上市公司股份信息
      companyManage: [], // 职务信息
      limitStock: [], // 限售股份信息
      formTitle: '', // 表单标题
      nextItemText: '下一项', // 下一步按文字提示内容
      //   formShow : false, // 是否显示表单
      questionList: [
        {
          title:
            '是否为我公司股东或关联人申报（此处所称股东，不包括我公司上市后仅持有5%以下上市流通股份的股东）',
          relTitle: '',
          checkedIndex: -1,
          answerList: [
            {
              answerName: '否',
              answerId: '1',
              relAnswerList: []
            },
            {
              answerName: '是',
              answerId: '2',
              relAnswerList: []
            }
          ]
        },
        {
          title:
            '是否存在关联人（关联人是指如果一方有能力直接或者间接控制、共同控制另一方或者对另一方施加重大影响，或者如果两方或多方同受一方控制，则将该各方视为关联人，包括关联法人、关联自然人和潜在关联人）',
          relTitle: '请补充关联人信息',
          checkedIndex: -1,
          answerList: [
            {
              answerName: '否',
              answerId: '1',
              relAnswerList: []
            },
            {
              answerName: '是',
              answerId: '2',
              relAnswerList: [
                {
                  name: '姓名',
                  value: '',
                  key: 'relationName',
                  type: 'input',
                  placeHolder: '请输入关联人姓名',
                  maxLength: 10,
                  format: /[\u4e00-\u9fa5]+$/
                },
                {
                  name: '关系',
                  value: '',
                  key: 'relationType',
                  placeHolder: '请选择关联人关系',
                  type: 'select',
                  category: 'ismp.relation',
                  format: ''
                },
                {
                  name: '性质', // 关联人性质 机构或个人
                  value: '',
                  key: 'relationProperties',
                  placeHolder: '请选择关联人性质',
                  type: 'select',
                  category: 'ismp.userType',
                  format: ''
                },
                {
                  name: '证件类别',
                  value: '',
                  key: 'idType',
                  type: 'select',
                  placeHolder: '请选择关联人证件类别',
                  category: 'ismp.idtype',
                  format: ''
                },
                {
                  name: '证件号码',
                  value: '',
                  key: 'idNo',
                  type: 'input',
                  placeHolder: '请输入关联人证件号码',
                  maxLength: 18,
                  format: /^[1-9]\d{7}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}$|^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}([0-9]|X)$/ // 替换不是数字和x或X的字符
                },
                {
                  name: '沪A账户',
                  value: '',
                  key: 'shAccount',
                  maxLength: 10,
                  placeHolder: '请输入关联人上海证券账号',
                  type: 'input',
                  format: /^A[0-9]{9}$/
                },
                {
                  name: '深A账户',
                  value: '',
                  key: 'szAccount',
                  type: 'input',
                  placeHolder: '请输入关联人深圳证券账号',
                  maxLength: 10,
                  format: /[0-9]{10}$/
                },
                {
                  name: '证券机构',
                  placeHolder: '请输入关联人所在证券机构',
                  value: '',
                  key: 'zqName',
                  type: 'input',
                  maxLength: 30,
                  format: /[\u4e00-\u9fa5]+$/
                }
              ]
            }
          ]
        },
        {
          title: '是否为持有上市公司股份5%（含）以上',
          relTitle: '请补充持股信息（可多只）',
          checkedIndex: -1,
          answerList: [
            {
              answerName: '否',
              answerId: '1',
              relAnswerList: []
            },
            {
              answerName: '是',
              answerId: '2',
              relAnswerList: [
                {
                  name: '股票代码',
                  value: '',
                  key: 'stockCode',
                  maxLength: 6,
                  type: 'input',
                  format: /[0-9]{6}$/
                },
                {
                  name: '股票名称',
                  value: '',
                  key: 'stockName',
                  type: 'input',
                  maxLength: 10,
                  format: /[\u4e00-\u9fa5]|[a-zA-Z]+$/
                },
                {
                  name: '持股数量',
                  value: '',
                  key: 'stockCount',
                  type: 'input',
                  maxLength: 15,
                  unit: '股',
                  format: /^[0-9]*$/
                },
                {
                  name: '持股比例',
                  placeHolder: '精确到整数',
                  value: '',
                  key: 'stockPercent',
                  type: 'input',
                  unit: '%',
                  maxLength: 10,
                  format: /^([1-9]?\d|99)$/
                }
              ]
            }
          ]
        },
        {
          title: '是否持有上市公司限售股份',
          relTitle: '请补充持股信息（可多只）',
          checkedIndex: -1, // 当前选中的答案选项
          answerList: [
            {
              answerName: '无',
              answerId: '1',
              relAnswerList: []
            },
            {
              answerName: '有',
              answerId: '2',
              relAnswerList: [
                {
                  name: '股票代码',
                  value: '',
                  key: 'stockCode',
                  type: 'input',
                  format: /[0-9]{6}$/,
                  maxLength: 6
                },
                {
                  name: '股票名称',
                  value: '',
                  key: 'stockName',
                  type: 'input',
                  format: /[\u4e00-\u9fa5]|[a-zA-Z]+$/
                },
                {
                  name: '持股数量',
                  value: '',
                  key: 'stockCount',
                  type: 'input',
                  unit: '股',
                  format: /^[0-9]*$/
                },
                {
                  name: '是否解除限售',
                  value: '',
                  key: 'isLimit',
                  type: 'radio',
                  category: 'ismp.id_type'
                }
              ]
            }
          ]
        },
        {
          title: '是否为上市公司董事、监事、高级管理人员',
          relTitle: '请补充高管信息',
          checkedIndex: -1, // 当前选中的答案选项
          answerList: [
            {
              answerName: '否',
              answerId: '1',
              relAnswerList: []
            },
            {
              answerName: '是',
              answerId: '2',
              relAnswerList: [
                {
                  name: '公司名称',
                  value: '',
                  key: 'companyName',
                  type: 'input',
                  format: /[\u4e00-\u9fa5]+$/
                },
                {
                  name: '股票代码',
                  value: '',
                  key: 'stockCode',
                  type: 'input',
                  format: /[0-9]{6}$/,
                  maxLength: 6
                },
                {
                  name: '任职职务',
                  value: '',
                  key: 'takeOffice',
                  type: 'input',
                  format: /[\u4e00-\u9fa5]+$/
                },
                {
                  name: '任职期限',
                  value: '',
                  key: 'termOfService',
                  type: 'input',
                  format: /^[0-9\u4e00-\u9fa5\-]+$/
                }
              ]
            }
          ]
        }
      ]
    }
  },
  activated () {
    this.$store.commit('updateBusinessNextBtnStatus', false) // 隐藏下一步按钮
    this.$store.commit('updateIsShowHead', false) // 隐藏head
    // 切换回来后初始化界面显示数据
  },
  computed: {
    // 计算是否需要显示表单组件
    formShow () {
      // 不是第一题，且选择的答案是第二个时，显示
      if (
        this.currentIndex != 0 &&
        this.questionList[this.currentIndex].checkedIndex == 1
      ) {
        this.formLength = 1
        this.formInitData = this.questionList[
          this.currentIndex
        ].answerList[1].relAnswerList
        this.formTitle = this.questionList[this.currentIndex].relTitle
        return true
      } else {
        this.formLength = 1
        this.formInitData = []
        this.formTitle = []
        return false
      }
    }
  },
  watch: {
    currentIndex () {
      if (this.currentIndex >= this.questionList.length - 1) {
        this.currentIndex = this.questionList.length - 1
        this.nextItemText = '下一步'
      }
    }
  },
  mounted () {
    window.phoneBackBtnCallBack = this.pageBack
  },
  methods: {
    /** ************子组件公共方法定义****** */
    doCheck () {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        // 判断是否答完所有题目 此处不做判断 每一题做相关控制
        resolve()
      })
    },
    /**
     * 请求参数打包
     */
    reqParamPackage () {
      // 业务请求参数封装
      let params = {}
      return params
    },
    putFormData () {
      let formData = {
        // specialInfo:{
          corporateShareholder: [{isCorporateShareholderFlag: '0'}],
          relationInfo: this.relationInfo,
          companyPercent: this.companyPercent,
          companyManage: this.companyManage,
          limitStock: this.limitStock
        // }
      }
      return formData
    },
    /** ************子组件公共方法定义****** */
    // 选中选项事件
    bindCheck (event) {
      let element = event.currentTarget // 当前点中的选项
      let childrens = element.parentNode.children // 获取父级的所有子节点
      for (let i = 0; i < childrens.length; i++) {
        if (element == childrens[i]) {
          // 添加checked
          this.addClass(childrens[i].firstChild, 'checked')
          this.checkedIndex = i
          // 记录当前问题的选中答案项的index
          this.questionList[this.currentIndex].checkedIndex = this.checkedIndex
        } else {
          this.removeClass(childrens[i].firstChild, 'checked')
        }
      }
      // 第一题做特殊判断，如果选择是，弹框提示不符合办理条件。
      if (this.currentIndex == 0 && this.checkedIndex == 1) {
        _hvueAlert({
          mes: '我公司关联股东不符合融资融券申请人要求',
          callback: () => {
            // 取消选中
            this.removeClass(
              childrens[this.checkedIndex].firstChild,
              'checked'
            )
            this.checkedIndex == -1
            this.questionList[this.currentIndex].checkedIndex = -1
          }
        })
        return
      }

      // 判断是否需要跳转到下一步 没有需要填充表单信息时，且判断答案是正确时，直接跳转下一体
      // if (this.currentIndex == 0) {
      // 选择第二个选线时，不自动跳转下一题
      if (this.checkedIndex == 1) {
        return
      } else if (this.currentIndex >= this.questionList.length - 1) {
        // 最后一道题时,更改按钮文字
        this.currentIndex = this.questionList.length - 1
        this.nextItemText = '下一步'
        return
      }
      setTimeout(() => {
        this.currentIndex++
        this.checkedIndex = -1
      }, 50)
      // }
    },
    addClass (element, className) {
      if (
        !new RegExp('(^|\\s)' + className + '(\\s|$)').test(element.className)
      ) {
        element.className += ' ' + className
      }
    },
    removeClass (element, className) {
      element.className = element.className.replace(
        new RegExp('(^|\\s)' + className + '(?=(\\s|$))', 'g'),
        ''
      )
    },
    nextItem () {
      // 如果展示的表单 则获取表单数据
      if (this.formShow) {
        let formData = this.$refs['infoForm'].getFormData() // 特殊信息申报表单数据
        if (!formData) {
          return
        }
        /** *****此处去掉检查 在表单组件中做判断****** */
        // 检查formData数据是否完整 ，遍历key，若有key的值为空，则视为信息填写不完整
        // for (let index = 0; index < formData.length; index++) {
        //   const element = formData[index];
        //   for (const key in element) {
        //     if (element[key] == '') {
        //       // 提示信息未填写完整
        //       _hvueToast({mes:this.questionList[this.currentIndex].relTitle});
        //       return;
        //     }
        //   }
        // }
        switch (this.currentIndex) {
          case 1:
            this.relationInfo = formData
            break
          case 2:
            this.companyPercent = formData
            break
          case 3:
            this.limitStock = formData
            break
          case 4:
            this.companyManage = formData
            break
          default:
            break
        }
      }
      if (this.currentIndex == this.questionList.length - 1) {
        // 最后一道题时 提交
        this.$parent.emitNextEvent()
      }
      if (this.questionList[this.currentIndex].checkedIndex != -1) {
        // 判断是否可以继续做下一题 -- >判断是否已经填写相关内容
        this.checkedIndex = -1
        this.currentIndex++
        // 滚动页面到顶部
        this.$refs.specialPage.scrollTop = 0
        if (this.currentIndex >= this.questionList.length - 1) {
          // 最后一道题时,更改按钮文字
          this.currentIndex = this.questionList.length - 1
          this.nextItemText = '下一步'
        }
      } else {
        _hvueToast({ mes: '请先答题' })
      }
    },
    // 继续添加
    continueAdd (event) {
      // 复制一份当前信息。
      this.formLength++
    },
    pageBack () {
      this.checkedIndex = -1
      if (this.currentIndex == 0) {
        this.$parent.back()
        return
      }
      this.currentIndex--
      if (this.currentIndex != this.questionList.length - 1) {
        // 最后一道题时,更改按钮文字
        this.nextItemText = '下一项'
      }
    }
  }
}
</script>
