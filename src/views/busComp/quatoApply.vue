<template>
  <div class="amount_main">
    <div class="max_amount">
      <ul>
        <li>
          <span class="tit">最大申请额度</span>
          <p>{{parseInt(initData.totalMaxQuota)}}万元</p>
        </li>
      </ul>
    </div>
    <p class="amount_tips">该额度是用户授信额度最大值，请确认拟申请的额度值小于最大授信额度值</p>
    <div class="amount_info">
      <h5 class="tit">拟申请授信额度</h5>
      <div class="amount_input">
        <i class="icon">￥</i>
        <input class="t1" type="text" placeholder="请输入整数" v-model="applyMount" :maxlength="maxLength" :change="checkNum()" />
        <span class="unit">万元</span>
      </div>
      <div class="tips" v-show="isShowTips">
        <span>超过最大额度</span>
        ，请输入小于{{parseInt(initData.totalMaxQuota)}}万元的整数
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: ['pageParam'],
  data () {
    return {
      applyMount: '',
      maxLength: 10
    }
  },
  computed: {
    initData () {
      return this.pageParam[0]
    },
    isShowTips () {
      // 申请额度大于最大额度时显示超出额度的提示
      if (this.applyMount > parseInt(this.initData.totalMaxQuota)) {
        return true
      } else {
        return false
      }
    }
  },
  methods: {
    /** ********************************************子组件公共方法定义*****start******************************* */
    doCheck () {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        // 校验是否已经输入
        if (!this.applyMount) {
          _hvueToast({ mes: '请输入申请额度' })
          return false
        }
        if (
          this.applyMount &&
          this.applyMount > parseInt(this.initData.totalMaxQuota)
        ) {
          _hvueToast({ mes: '超过最大额度,请重新输入' })
          return false
        }
        // 必须为10万元的倍数
        if (this.applyMount % 10 != 0) {
          _hvueAlert({ mes: '规定只能设置成取整10万，请重新输入', title: '提示' })
          return false
        }
        // 继续执行
        resolve()
      })
    },
    /**
     * 请求参数打包
     */
    reqParamPackage () {
      // 业务请求参数封装
      let params = {
        applyAmount: this.applyMount,
        maxLimit: parseInt(this.initData.totalMaxQuota)
      }
      return params
    },
    putFormData () {
      let formData = {
        quotaApply: {
          clientId: $h.getSession('ygtUserInfo').clientId,
          fundAccount: this.$parent.flow.fundAccount,
          applyAmount: this.applyMount,
          maxLimit: parseInt(this.initData.totalMaxQuota)
        }
      }
      return formData
    },
    /** ********************************************子组件公共方法定义***end************************************* */
    checkNum () {
      this.applyMount = this.applyMount.replace(/[^\d]/g, '')
    }
  }
}
</script>
<style scoped>
</style>
