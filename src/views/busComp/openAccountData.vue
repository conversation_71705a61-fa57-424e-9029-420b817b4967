<!-- 增开股东户-股东账户检测情况 -->
<template>
  <div>
    <div v-show="pageParam[0].isOpenAccountFlag=='false' || pageParam[0].isJgAccountFlag=='false'" class="cond_tips">
      <p>{{pageParam[0].reason}}</p>
    </div>
  </div>
</template>

<script>
export default {
  props: ['pageParam'],
  data () {
    return {
    }
  },
  activated () {
    if(this.pageParam[0].isOpenAccountFlag === 'true' || this.pageParam[0].isJgAccountFlag === 'true'){
      if (this.$route.query.type === 'zkgdh') {
        this.$store.commit('updateNextBtnText', '继续开户')
      } else if (this.$route.query.type === 'jggdh'){
        this.$store.commit('updateNextBtnText', '选择账户下挂')
      }
    } else {
      this.$store.commit('updateNextBtnText', '返回首页')
      this.$store.commit('updateNextBtnCss', true)
    }
  },
  methods: {
    doCheck () {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        if (this.pageParam[0].isOpenAccountFlag === 'true' || this.pageParam[0].isJgAccountFlag === 'true') {
          // 可以下一步
          resolve()
        } else {
          // 返回业务办理首页;
          this.$router.push({ name: 'index' })
        }
      })
    },
    /**
     * 请求参数打包
     */
    reqParamPackage () {
      // 业务请求参数封装
      let params = {}
      return params
    },
    putFormData () {
      let formData = {}
      return formData
    }
  }
}
</script>
