<template>
  <div></div>
</template>

<script>
export default {
  props: ['pageParam'],

  activated() {
    if (this.pageParam.length > 0) {
      $h.setSession('xhBankNo', this.pageParam[0].bankNo)
      $h.setSession('xhBankAccount', this.pageParam[0].bankAccount)
      $h.setSession('xhBankList', this.pageParam)
    } else {
      $h.setSession('xhBankNo', '')
      $h.setSession('xhBankAccount', '')
      $h.setSession('xhBankList', [])
    }
  },
  methods: {
    doCheck() {
      // 执行下一步
      return new Promise((resolve, reject) => {
        resolve()
      })
    },
    /**
     * 请求参数打包
     */
    reqParamPackage() {
      // 业务请求参数封装
      let params = {}
      return params
    },
    // 表单参数打包
    putFormData() {
      let formData = {}
      // 将初始化返回的原子表单数据提交到后端
      if (this.pageParam[0]) {
        // formData.reason = this.pageParam[0].reason
      }
      return formData
    }
  }
}
</script>
