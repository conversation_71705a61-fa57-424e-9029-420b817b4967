<template>
  <div v-cloak>
      <div class="cond_box">
      <h5 class="title">请先满足以下条件后再进行此业务办理</h5>
      <ul>
        <li :key="idx" v-for="(item, idx) in itemDesc" :class="{ok : itemValue[idx]=='1',error:itemValue[idx] != '1'}">
          <div class="tit">
            <p>{{ item }}</p>
            <a
              class="link"
              @click.stop="toRisk"
              v-if="checkItem[idx] === 'minRankFlag' && itemValue[idx] != '1'"
            >重新测评</a
            >
            <span class="tips" v-if="itemValue[idx] == '0'">{{reasonDesc[idx]}}</span>
          </div>
        </li>
      </ul>
    </div>
  </div>
</template>
<script>
export default {
  props: ['pageParam'],
  data () {
    return {
      itemDesc: [], // 条件项
      itemValue: [], // 判断
      reasonDesc: [], // 不满足项提示
      checkItem: [],
      nextFlag: false,
    };
  },
  activated () {
    this.itemDesc = this.pageParam[0].itemDesc;
    this.reasonDesc = this.pageParam[0].reasonDesc;
    this.itemValue = this.pageParam[0].itemValue;
    this.checkItem = this.pageParam[0].checkItem

    if (this.itemDesc) {
      this.itemDesc = this.itemDesc.split('|')
      this.itemValue = this.itemValue.split('|');
      this.reasonDesc = this.reasonDesc.split('|');
      this.checkItem = this.checkItem.split('|');
    }

    if (this.itemValue.includes('0') || this.itemValue.includes('2')) {
      this.nextFlag = false;
      this.$store.commit('updateNextBtnText', '返回首页');
      this.errorDesc = '抱歉，您不满足基本条件，暂时不能办理';
    } else {
      this.nextFlag = true;
      this.$store.commit('updateNextBtnText', '下一步');
      this.errorDesc = '恭喜您，满足全部基本条件';
    }
  },
  methods: {
    doCheck () {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        if (this.nextFlag) {
          // 可以下一步
          resolve();
        } else {
          // 返回业务办理首页;
          this.$router.push({ name: 'index' });
        }
      });
    },
    /**
     * 请求参数打包
     */
    reqParamPackage () {
      // 业务请求参数封装
      let params = {
        param1: '',
        param2: '',
      };
      return params;
    },
    putFormData () {
      let formData = {};
      return formData;
    },
    // 跳转到风险测评
    toRisk () {
      this.$router.push({
        name: 'business',
        query: { type: 'fxcp', name: '风险测评' },
      });
    },
  },
};
</script>
