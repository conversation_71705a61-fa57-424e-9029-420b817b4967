<template>
  <div>
    <div class="cond_box">
      <h5 v-if="!totalFlag" class="title">以下条件尚未满足，请先满足</h5>
      <h5 v-else class="title">以下条件已满足，可继续办理</h5>
      <ul>
        <li
          v-show="pageParam[0].validityFlag != undefined && ygtUserInfo.userType === '0'"
          :class="pageParam[0].validityFlag==='0' ? 'error' : 'ok'"
        >
          <div class="tit">
            <p>身份证{{pageParam[0].validityFlag==='0' ? '已' : '未'}}过期</p>
            <a v-if="fundAccount == ''" @click.stop="showTips(0)" v-show="pageParam[0].validityFlag==='0' ? true : false" class="a_link">更新身份证</a>
            <router-link
              v-else
              v-show="pageParam[0].validityFlag==='0' ? true : false"
              :to="{name: 'business',query: {type: 'sfzgx',name: '身份证更新',isBreakPoint: '1'}}"
              class="link"
            >更新身份证</router-link>
          </div>
        </li>
        <li
            v-show="pageParam[0].licenceFlag != undefined  && ygtUserInfo.userType !== '0'"
            :class="!+pageParam[0].licenceFlag ? 'error' : 'ok'"
        >
          <div class="tit">
            <p>营业执照{{!+pageParam[0].licenceFlag ? '已' : '未'}}过期</p>
          </div>
        </li>
        <li
          v-show="ygtUserInfo.userType === '0' && pageParam[0].userDataFlag != undefined"
          :class="pageParam[0].userDataFlag==='0' ? 'error' : 'ok'"
        >
          <div class="tit">
            <p>基本信息{{pageParam[0].userDataFlag==='0' ? '不' : ''}}完整</p>
            <a v-if="fundAccount == ''" @click.stop="showTips(1)" v-show="pageParam[0].userDataFlag==='0' ? true : false" class="a_link">去完善</a>
            <router-link
              v-else
              v-show="pageParam[0].userDataFlag==='0' ? true : false"
              :to="{name: 'business',query: {type: 'zlxg',name: '资料修改',isBreakPoint: '0'}}"
              class="link"
            >去完善</router-link>
          </div>
        </li>
        <li
            v-show="ygtUserInfo.userType === '1' || ygtUserInfo.userType === '3'"
            :class="(pageParam[0].organFlag==='0' || !pageParam[0].organFlag) ? 'error' : 'ok'"
        >
          <div class="tit">
            <p>基本信息{{pageParam[0].organFlag==='0' ? '不' : ''}}完整</p>
            <a v-if="fundAccount == ''" @click.stop="showTips(1)" v-show="pageParam[0].organFlag==='0' ? true : false" class="a_link">去完善</a>
            <router-link
                v-else
                v-show="pageParam[0].organFlag==='0' ? true : false"
                :to="{name: 'business',query: {type: 'zlxg',name: '资料修改',isBreakPoint: '0'}}"
                class="link"
            >去完善</router-link>
          </div>
        </li>
        <li
          v-show="pageParam[0].signFlag != undefined"
          :class="pageParam[0].signFlag==='0' ? 'error' : 'ok'"
        >
          <div class="tit">
            <p>电子签名约定书{{pageParam[0].signFlag==='0' ? '未' : '已'}}签订</p>
            <a v-if="fundAccount == ''" @click.stop="showTips(2)" v-show="pageParam[0].signFlag==='0' ? true : false" class="a_link">去签署</a>
            <router-link
              v-else
              v-show="pageParam[0].signFlag==='0' ? true : false"
              :to="{name: 'business',query: {type: 'dzqmyds',name: '电子签名约定书',isBreakPoint: '0'}}"
              class="link"
            >去签署</router-link>
          </div>
        </li>
        <li
          v-show="pageParam[0].riskFlag != undefined"
          :class="pageParam[0].riskFlag==='0' ? 'error' : 'ok'"
        >
          <div class="tit">
            <p>风险测评{{pageParam[0].riskFlag==='0' ? '已' : '未'}}过期</p>
            <a v-if="fundAccount == ''" @click.stop="showTips(3)" v-show="pageParam[0].riskFlag==='0' ? true : false" class="a_link">立即更新</a>
            <router-link
              v-else
              v-show="pageParam[0].riskFlag==='0' ? true : false"
              :to="{name: 'business',query: {type: 'fxcp',name: '风险测评',isBreakPoint: '0'}}"
              class="link"
            >立即更新</router-link>
          </div>
        </li>
      </ul>
    </div>
    <div class="cond_tips" v-show="!totalFlag">
      <p>抱歉，您办理条件未满足暂时不能办理</p>
    </div>
    <!-- <div class="ce_btn mt20">
        <a
          @click.stop="doCheck"
          class="ui button block rounded"
          :class="totalFlag ? '' : 'border'"
        >{{ totalFlag ? '下一步' : '返回'}}</a>
    </div>-->
  </div>
</template>
<script>
export default {
  props: ['pageParam'],
  data () {
    return {
      ygtUserInfo: $h.getSession('ygtUserInfo', {decrypt: false}) || {}, // 用户信息
      fundAccount: ''
    }
  },
  activated () {
    if (this.ygtUserInfo.userType === '0') {
      this.$set(this.pageParam[0], 'organFlag', '1')
      this.$set(this.pageParam[0], 'licenceFlag', '1')
    } else {
      this.$set(this.pageParam[0], 'userDataFlag', '1')
      this.$set(this.pageParam[0], 'validityFlag', '1')
    }
    this.fundAccount = this.ygtUserInfo.fundAccount
    let str = JSON.stringify(this.pageParam[0])
    if (str.indexOf('0') != -1) {
      // 有为0的情况不满足
      this.$store.commit('updateNextBtnText', '返回首页')
      return
    }
    this.$store.commit('updateNextBtnText', '下一步')
  },
  computed: {
    totalFlag () {
      let str = JSON.stringify(this.pageParam[0])
      if (str.indexOf('0') != -1) {
        // 有为0的情况不满足
        return false
      }
      return true
    }
  },
  methods: {
    // 跳转
    showTips (type) {
      switch (type) {
        case 0:
          _hvueAlert({
            title: '提示',
            mes: '您的身份证已过期/未升位，请您使用普通账户登录网厅进行身份证的更新升位'
          })
          break
        case 1:
          _hvueAlert({
            title: '提示',
            mes: '您的基本信息未完善，请您使用普通账户登录网厅进行基本信息修改'
          })
          break
        case 2:
          _hvueAlert({
            title: '提示',
            mes: '您未签署电子签名约定书，请您使用普通账户登录网厅进行电子签名约定书的签署'
          })
          break
        case 3:
          _hvueAlert({
            title: '提示',
            mes: '您的风险测评已过期，请您使用普通账户登录网厅重新进行风险测评'
          })
          break

        default:
          break
      }
    },
    doCheck () {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        // 开发环境不做拦截 || process.env.NODE_ENV == "development"
        if (this.totalFlag) {
          // 可以下一步
          resolve()
        } else {
          // 返回业务办理首页;
          this.$router.push({ name: 'index' })
        }
      })
    },
    /**
     * 请求参数打包
     */
    reqParamPackage () {
      // 业务请求参数封装
      let params = {
        param1: '',
        param2: '',
      }
      return params
    },
    putFormData () {
      let formData = {}
      return formData
    }
  }
}
</script>
<style>
.a_link {
  height: 0.24rem;
  line-height: 0.24rem;
  position: absolute;
  top: 0.11rem;
  right: 0.15rem;
  font-size: 0.13rem;
  color: #285fc1;
}
</style>
