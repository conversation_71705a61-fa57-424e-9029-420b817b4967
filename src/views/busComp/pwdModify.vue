<template>
  <div>
    <div class="pword_setcheck" v-show="showPwdSame">
      <p>交易密码，资金密码相同</p>
      <div class="ui switch">
        <input type="checkbox" @change="sameClick($event)" />
        <div class="ui switch-inner">
          <div class="ui switch-arrow"></div>
        </div>
      </div>
    </div>
    <div class="pword_box" v-show="showPwd">
      <h5 class="title">请输入要修改的密码</h5>
      <div class="input_form">
        <div class="ui field text pword">
          <label class="ui label">旧密码</label>
          <input
            v-model="inputModel.oldPwd.value"
            ref="oldPwd"
            :type="pwdType"
            class="ui input"
            placeholder="请输入旧密码"
            :maxlength="inputModel.maxlength"
          />
          <a
            class="txt_close"
            @click.stop="inputModel.oldPwd.value=''"
            v-show="inputModel.oldPwd.value!=''"
          ></a>
          <a class="icon_eye" :class="{show: pwdShow}" @click.stop="pwdShow = !pwdShow"></a>
        </div>
      </div>
      <div class="input_form mt10">
        <div class="ui field text">
          <label class="ui label">新密码</label>
          <input
            v-model="inputModel.newPwd.value"
            ref="newPwd"
            :type="pwdType"
            class="ui input"
            :maxlength="inputModel.maxlength"
            placeholder="请输入新密码"
          />
          <a
            class="txt_close"
            @click.stop="inputModel.newPwd.value=''"
            v-show="inputModel.newPwd.value!=''"
          ></a>
        </div>
        <div class="ui field text">
          <label class="ui label">再次输入</label>
          <input
            v-model="inputModel.reNewPwd.value"
            ref="reNewPwd"
            :type="pwdType"
            class="ui input"
            :maxlength="inputModel.maxlength"
            placeholder="请再次输入新密码"
          />
          <a
            class="txt_close"
            @click.stop="inputModel.reNewPwd.value=''"
            v-show="inputModel.reNewPwd.value!=''"
          ></a>
        </div>
      </div>
      <div class="ce_btn mt10">
        <a v-throttle class="ui button block rounded" @click.stop="submitPwd('1,2')">确认修改</a>
      </div>
    </div>
    <div class="pword_box" v-show="showZJPwd">
      <h5 v-show="showPwdSame" class="title">资金密码</h5>
      <h5 v-show="!showPwdSame" class="title">当前资金账户：{{fundAccount}}</h5>
      <div class="input_form">
        <div class="ui field text pword">
          <label class="ui label">旧密码</label>
          <input
            ref="oldZjPwd"
            v-model="inputModel.oldZjPwd.value"
            :type="pwdType"
            class="ui input"
            :maxlength="inputModel.maxlength"
            placeholder="请输入旧资金密码"
          />
          <a
            class="txt_close"
            @click.stop="inputModel.oldZjPwd.value=''"
            v-show="inputModel.oldZjPwd.value!=''"
          ></a>
          <a class="icon_eye" :class="{show: pwdShow}" @click.stop="pwdShow = !pwdShow"></a>
        </div>
      </div>
      <div class="input_form mt10">
        <div class="ui field text">
          <label class="ui label">新密码</label>
          <input
            ref="zjPwd"
            v-model="inputModel.zjPwd.value"
            :type="pwdType"
            class="ui input"
            :maxlength="inputModel.maxlength"
            placeholder="请输入新资金密码"
          />
          <a
            class="txt_close"
            @click.stop="inputModel.zjPwd.value=''"
            v-show="inputModel.zjPwd.value!=''"
          ></a>
        </div>
        <div class="ui field text">
          <label class="ui label">再次输入</label>
          <input
            ref="reZjPwd"
            v-model="inputModel.reZjPwd.value"
            :type="pwdType"
            class="ui input"
            :maxlength="inputModel.maxlength"
            placeholder="请再次输入新资金密码"
          />
          <a
            class="txt_close"
            @click.stop="inputModel.reZjPwd.value=''"
            v-show="inputModel.reZjPwd.value!=''"
          ></a>
        </div>
      </div>
      <div class="ce_btn mt10">
        <a v-throttle class="ui button block rounded" @click.stop="submitPwd('1')">修改资金密码</a>
      </div>
    </div>
    <div class="pword_box" v-show="showJYPwd">
      <h5 v-show="showPwdSame" class="title">交易密码</h5>
      <h5 v-show="!showPwdSame" class="title">当前资金账户：{{fundAccount}}</h5>
      <div class="input_form">
        <div class="ui field text pword">
          <label class="ui label">旧密码</label>
          <input
            ref="oldJyPwd"
            v-model="inputModel.oldJyPwd.value"
            :type="pwdType"
            class="ui input"
            :maxlength="inputModel.maxlength"
            placeholder="请输入旧交易密码"
          />
          <a
            class="txt_close"
            @click.stop="inputModel.oldJyPwd.value=''"
            v-show="inputModel.oldJyPwd.value!=''"
          ></a>
          <a class="icon_eye" :class="{show: pwdShow}" @click.stop="pwdShow = !pwdShow"></a>
        </div>
      </div>
      <div class="input_form mt10">
        <div class="ui field text">
          <label class="ui label">新密码</label>
          <input
            ref="jyPwd"
            v-model="inputModel.jyPwd.value"
            :type="pwdType"
            class="ui input"
            :maxlength="inputModel.maxlength"
            placeholder="请输入新交易密码"
          />
          <a
            class="txt_close"
            @click.stop="inputModel.jyPwd.value=''"
            v-show="inputModel.jyPwd.value!=''"
          ></a>
        </div>
        <div class="ui field text">
          <label class="ui label">再次输入</label>
          <input
            ref="reJyPwd"
            v-model="inputModel.reJyPwd.value"
            :type="pwdType"
            class="ui input"
            :maxlength="inputModel.maxlength"
            placeholder="请再次输入新交易密码"
          />
          <a
            class="txt_close"
            @click.stop="inputModel.reJyPwd.value=''"
            v-show="inputModel.reJyPwd.value!=''"
          ></a>
        </div>
      </div>
      <div class="ce_btn mt10">
        <a v-throttle class="ui button block rounded" @click.stop="submitPwd('2')">修改交易密码</a>
      </div>
    </div>
  </div>
</template>

<script>
import { checkInput } from '@/common/util'
export default {
  props: ['pageParam'],
  data () {
    return {
      fundAccount: this.$parent.flow.fundAccount,
      submitParam: {},
      passwordType: '',
      showPwdSame: false,
      showZJPwd: false,
      showJYPwd: false,
      showPwd: false,
      pwdShow: false,
      type: '',
      inputModel: {
        maxlength: '6',
        oldPwd: {
          name: '旧密码',
          value: '',
          format: 'num'
        },
        newPwd: {
          name: '新密码',
          value: '',
          format: 'pwd'
        },
        reNewPwd: {
          name: '再次输入新密码',
          value: '',
          format: 'pwd'
        },
        oldZjPwd: {
          name: '旧资金密码',
          value: '',
          format: 'num'
        },
        zjPwd: {
          name: '新资金密码',
          value: '',
          format: 'pwd'
        },
        reZjPwd: {
          name: '再次输入新资金密码',
          value: '',
          format: 'pwd'
        },
        oldJyPwd: {
          name: '旧交易密码',
          value: '',
          format: 'num'
        },
        jyPwd: {
          name: '新交易密码',
          value: '',
          format: 'pwd'
        },
        reJyPwd: {
          name: '再次输入新交易密码',
          value: '',
          format: 'pwd'
        }
      }
    }
  },
  computed: {
    pwdType () {
      return this.pwdShow ? 'tel' : 'password'
    }
  },
  activated () {
    if (this.pageParam.length < 1) {
      _hvueAlert({
        mes: '未获取到密码类型，请重新选择',
        title: '提示',
        callback: () => {
          this.$parent.back()
        }
      })
    }
    this.passwordType = this.pageParam[0].passwordType
    this.$store.commit('updateBusinessNextBtnStatus', false) // 隐藏下一步按钮
    if (this.passwordType === '2') {
      this.flowNameCn = '修改交易密码'
      this.flowName = 'xgjymm'
      this.showJYPwd = true
      this.showZJPwd = false
      this.showPwd = false
      this.showPwdSame = false
    } else if (this.passwordType === '1') {
      this.flowNameCn = '修改资金密码'
      this.showZJPwd = true
      this.showJYPwd = false
      this.showPwd = false
      this.showPwdSame = false
    } else {
      this.flowNameCn = '修改资金和交易密码'
      this.showJYPwd = true
      this.showZJPwd = true
      this.showPwdSame = true
    }
  },
  methods: {
    sameClick (e) {
      if (e.target.checked === false) {
        this.showJYPwd = true
        this.showZJPwd = true
        this.showPwd = false
      } else {
        this.showJYPwd = false
        this.showZJPwd = false
        this.showPwd = true
      }
    },
    /**
     * type: 1资金密码修改，2交易密码修改，1,2同步修改资金和交易密码
     */
    submitPwd (_type) {
      let _old, _new, _reNew
      this.type = _type
      if (_type === '1') {
        _old = this.inputModel.oldZjPwd
        _new = this.inputModel.zjPwd
        _reNew = this.inputModel.reZjPwd
        this.flowNameCn = '修改资金密码'
      } else if (_type === '2') {
        _old = this.inputModel.oldJyPwd
        _new = this.inputModel.jyPwd
        _reNew = this.inputModel.reJyPwd
        this.flowNameCn = '修改交易密码'
      } else if (_type === '1,2') {
        _old = this.inputModel.oldPwd
        _new = this.inputModel.newPwd
        _reNew = this.inputModel.reNewPwd
      }
      let flag = checkInput(_old)
      if (flag !== '') {
        return
      }
      flag = checkInput(_new)
      if (flag !== '') {
        return
      }
      flag = checkInput(_reNew)
      if (flag !== '') {
        return
      }
      if (_new.value !== _reNew.value) {
        _hvueToast({
          mes: '两次新密码输入不一致，请检查'
        })
        return
      }
      if (_new.value === _old.value) {
        _hvueToast({
          mes: '新密码与旧密码输入一致，请检查'
        })
        return
      }
      this.submitParam = {
        userId: $h.getSession('ygtUserInfo', {decrypt: false}).userId,
        oldPassword: _old.value,
        newPassword: _new.value,
        passwordType: _type
      }
      if (_type.indexOf('2') !== -1) {
        _hvueConfirm({
          title: '温馨提示',
          mes: '密码修改成功后，系统将会退出,如需办理其他业务，需重新登录',
          opts: () => {
            // 确定之后的回调
            this.$parent.emitNextEvent()
          }
        })
      } else {
        this.$parent.emitNextEvent()
      }
    },
    doCheck () {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        // 可以下一步
        $h.setSession('passwordType', this.passwordType)
        resolve()
      })
    },
    /**
     * 请求参数打包
     */
    reqParamPackage () {
      // 业务请求参数封装
      let params = this.submitParam // 提交需要的参数
      return params
    },
    putFormData () {
      let formData = {
        pwdModify: this.submitParam
      } // 需要保存的表单数据
      return formData
    }
  }
}
</script>
