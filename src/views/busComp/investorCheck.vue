<template>
  <div v-cloak>
      <headComponent headerTitle="准入条件"></headComponent>
      <div class="cond_box">
      <h5 class="title">请先满足以下条件后再进行此业务办理</h5>
      <ul>
        <li :class="{ok : assetFlag=='1',error:assetFlag == '0'}">
          <div class="tit">
            <p>已是合格投资者</p>
            <span class="tips">您已是合格投资者且在有效期内，无需重复申请</span>
          </div>
        </li>
        <li :class="{ok : tradeTimeFlag=='1',error:tradeTimeFlag == '0'}">
          <div class="tit">
            <p>存在不良诚信记录</p>
            <span class="tips">您存在不良的诚信记录，请临柜办理</span>
          </div>
        </li>
        <li :class="{ok : ageFlag=='1',error:ageFlag == '0'}">
          <div class="tit">
            <p>存管账户不正常</p>
            <span class="tips">您的三方存管账户尚未绑定，请完成后在申请</span>
          </div>
        </li>
        <li :class="{ok : riskFlag=='1',error:riskFlag == '0'}">
          <div class="tit">
            <p>风险名单客户</p>
            <span class="tips">您属于交易所重点监控名单，请临柜办理</span>
            <span class="tips">您属于实名制重点关注名单，请尽快完成账户注销</span>
          </div>
        </li>
      </ul>
    </div>
  </div>
</template>
<script>
import headComponent from '@/components/headComponent'
export default {
  components: {
    headComponent,
  },
  props: ["pageParam"],
  data() {
    return {
      ageFlag: "0",
      breakFlag: "0",
      riskFlag: "0",
      assetFlag: "0",
      tradeTimeFlag: "0",
      errorDesc: "",
      nextFlag: false
    };
  },
  activated() {
    this.ageFlag = this.pageParam[0].ageFlag;
    this.breakFlag = this.pageParam[0].breakFlag;
    this.riskFlag = this.pageParam[0].riskFlag;
    this.tradeTimeFlag = this.pageParam[0].tradeTimeFlag;
    this.assetFlag = this.pageParam[0].assetFlag;

    let str = JSON.stringify(this.pageParam[0]);
    if (str.indexOf("0") != -1) {
      this.nextFlag = false;
      this.$store.commit("updateNextBtnText", "返回首页");
      this.errorDesc = "抱歉，您不满足基本条件，暂时不能办理";
    } else {
      this.nextFlag = true;
      this.$store.commit("updateNextBtnText", "下一步");
      this.errorDesc = "恭喜您，满足全部基本条件";
    }
  },
  methods: {
    doCheck() {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        if (this.nextFlag || process.env.NODE_ENV == "development") {
          // 可以下一步
          resolve();
        } else {
          // 返回业务办理首页;
          this.$router.push({ name: "index" });
        }
      });
    },
    /**
     * 请求参数打包
     */
    reqParamPackage() {
      // 业务请求参数封装
      let params = {
        param1: "",
        param2: ""
      };
      return params;
    },
    putFormData() {
      let formData = {};
      return formData;
    },
    // 跳转到风险测评
    toRisk() {
      this.$router.push({
        name: "business",
        query: { type: "fxcp", name: "风险测评" }
      });
    }
  }
};
</script>
