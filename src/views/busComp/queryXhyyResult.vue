<template>
  <div class="page_main">
    <headComponent header-title="预约转销户"></headComponent>
    <article class="content white_bg">
      <div class="result_page" v-if="pageType === '3'">
        <div class="result_tips">
          <div class="icon ok" />
          <h5><span>转销户预约</span>撤销成功</h5>
          <p v-show="submitDate !== ''">提交时间：{{ submitDate }}</p>
        </div>
        <div class="result_info">
          <ul>
            <li>
              <span class="tit">转销户预约</span>
              <p><span class="state">撤销成功</span></p>
            </li>
          </ul>
        </div>
      </div>
      <div class="result_page" v-else-if="['2'].includes(pageType)">
        <div class="result_tips">
          <div class="icon ok" />
          <h5><span>转销户预约</span>初审通过</h5>
          <p>
            您的转销户预约初审已通过，预约有效期为30天，如超过30天不做后续操作则视为自动取消销户
          </p>
        </div>
        <div class="result_info">
          <ul>
            <li>
              <span class="tit">转销户预约</span>
              <p><span class="state">预约成功</span></p>
            </li>
          </ul>
        </div>
      </div>
      <div class="result_page" v-else>
        <div class="result_tips">
          <div class="icon ing" />
          <h5><span>成功提交，</span>处理中</h5>
          <p>
            销户预约已提交，客服会尽快进行审核，请您保持手机畅通。后续您需要上传相应资料，并与客服视频，方能完成正式转销户申请。如有疑问，请咨询客服**********。
          </p>
        </div>
        <div class="result_info">
          <ul>
            <li>
              <span class="tit">转销户预约</span>
              <p><span class="state">处理中</span></p>
            </li>
          </ul>
        </div>
      </div>
    </article>
    <footer class="footer white_bg">
      <div class="ce_btn black">
        <a v-show="['2'].includes(pageType)" class="p_button" @click="toCloseAccount">下一步</a>
        <!--  <a v-show="['2'].includes(pageType)" class="p_button border" @click="popupTaskCannel"
          >撤销转销户预约</a
        > -->
        <a v-show="pageType === '3'" class="p_button border" @click="taskCannel">返回</a>
        <a v-show="pageType === '1'" class="p_button" @click="popupTaskCannel">撤销转销户预约</a>
        <a v-show="pageType === '1'" class="p_button border" @click="back">返回</a>
      </div>
    </footer>
  </div>
</template>

<script>
import headComponent from '@/components/headComponent' // 头部
import { yyxhEnd, yyxhTaskCannel } from '@/service/comServiceNew'
import { checkHMTCardType } from '../../common/util'

export default {
  components: {
    headComponent
  },
  props: ['pageParam'],
  data() {
    return {
      ygtUserInfo: $h.getSession('ygtUserInfo', { decrypt: false }),
      clientId: '', // 客户号
      showDealDesc: '',
      showTips: ''
    }
  },
  computed: {
    // 页面类型 1预约销户审核中 2挽留失败 3挽留成功
    pageType() {
      return this.pageParam[0].state
    },
    submitDate() {
      return this.pageParam[0].submitDate || ''
    },
    idCardTypeName() {
      return checkHMTCardType()
        ? this.ygtUserInfo.identityType === 'I'
          ? '外国人永久居留证'
          : '港澳台居民来往内地通行证'
        : '二代身份证'
    }
  },
  activated() {
    // 隐藏business.vue的下一步按钮
    this.$store.commit('updateBusinessNextBtnStatus', false) // 隐藏下一步按钮
    this.$store.commit('updateIsShowHead', false) // 隐藏head
  },
  mounted() {
    window.phoneBackBtnCallBack = this.back
  },
  methods: {
    yyxhEnd() {
      yyxhEnd({ mobile: this.ygtUserInfo.mobile }, {}).then(
        res => {
          if (res.error_no === '0') {
            this.back()
          } else {
            _hvueToast({
              icon: 'error',
              mes: res.error_info
            })
          }
        },
        err => {
          _hvueToast({ mes: err })
        }
      )
    },
    toCloseAccount() {
      this.$router.push({
        name: 'business',
        query: {
          type: 'xh'
        }
      })
    },
    back() {
      this.$router.push({
        name: 'index'
      })
    },
    popupTaskCannel() {
      _hvueConfirm({
        mes:
          '点击"确认"，将为您撤销本次转销户预约。如果您有任何意见或建议，欢迎致电**********向我们反馈！',
        opts: [
          {
            txt: '取消',
            color: false
          },
          {
            txt: '确认',
            color: true,
            callback: this.taskCannel
          }
        ]
      })
    },
    taskCannel() {
      yyxhTaskCannel({
        userId: this.ygtUserInfo.userId
      })
        .then(({ error_no, error_info }) => {
          if (error_no === '0') {
            this.back()
          } else {
            return Promise.reject(error_info)
          }
        })
        .catch(err => {
          _hvueToast({ mes: err })
        })
    }
  }
}
</script>

<style scoped>
.bottom_btn {
  position: fixed;
  bottom: 0;
  border-top: 0.05rem solid #f9f9f9;
  z-index: 99999;
  background: white;
  margin-bottom: 0.1rem;
  width: 100%;
}
</style>
