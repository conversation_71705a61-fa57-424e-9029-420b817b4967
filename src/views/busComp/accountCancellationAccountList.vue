<template>
  <div>
    <div class="xh_acct_box">
      <div class="xh_acct_cont">
        <div class="xh_user_info">
          <p>{{ clientId }}</p>
          <em>客户号</em>
        </div>
        <div class="check_all">
          <span :class="{ checked: isCloseAll }" class="icon_radio" @click.stop="closeAll"
            >全部注销</span
          >
        </div>
      </div>
      <h3 v-if="accountCancellationAssetList.length > 0" class="title">
        资金账户
      </h3>
      <div v-if="accountCancellationAssetList.length > 0" class="xh_acct_cont">
        <ul>
          <li
            v-for="(d, index) in accountCancellationAssetList"
            :key="index"
            class="xh_fund_acc_box"
            @click.stop="selClick(d)"
          >
            <div class="xh_fund_acc_list" :class="{ checked: selectedAccounts.indexOf(d) != -1 }">
              <p>{{ d.account }}</p>
              <em v-if="d.mainFlag == '1'">主账户</em>
              <em v-else>辅账户</em>
            </div>
            <div v-show="selectedAccounts.indexOf(d) !== -1" class="ui field text pword">
              <label class="ui label">资金密码</label>
              <h-keypanel
                ref="fundpwd"
                class="hui-keypanel_style paddingleft_75"
                v-model="d.fundpwd"
                :mask="!pwdShow ? true : false"
                slot="left"
                type="digital"
                :length="inputModel.fundpwd.maxlength"
                placeholder="请输入资金密码"
              />
              <a :class="{ show: pwdShow }" class="icon_eye" @click.stop="pwdShow = !pwdShow"></a>
            </div>
            <!-- 银行信息展示 - 独立一行，在资金密码下方 -->
            <div v-if="getBankInfo(d.account)" class="bank-info-row">
              <span class="bank-name">{{ getBankInfo(d.account).bankFullName }}</span>
              <span class="bank-account">{{ getBankInfo(d.account).bankAccount }}</span>
            </div>
          </li>
        </ul>
        <div
          v-for="(d, index) in accountCancellationAssetList"
          :key="index"
          class="input_form mt10"
        ></div>
      </div>

      <h3 v-if="accountCancellationStockAccountList.length > 0" class="title">
        证券账户
      </h3>
      <div v-if="accountCancellationStockAccountList.length > 0" class="xh_acct_cont">
        <ul>
          <li
            v-for="(d, index) in accountCancellationStockAccountList"
            :class="{
              checked: selectedAccounts.indexOf(d) != -1,
              disabled: d.disabled === true
            }"
            :key="index"
            @click.stop="!d.disabled && selClick(d)"
          >
            <p>{{ d.marketName }}：{{ d.account }}</p>
            <em v-if="d.exchangeType === '9'">特转A</em>
            <em v-else-if="d.market == '10' || d.market == '13'">上海市场</em>
            <em v-else>深圳市场</em>
            <span
              v-show="['1', '2'].includes(d.exchangeType)"
              :class="{
                checked: isCheckedZD(d.account, d.exchangeType),
                disabled: d.zdChooseDisable === true
              }"
              class="icon_radio tb_span"
              @click.stop="!d.zdChooseDisable && chooseAccZD(d)"
              >同步中登销户</span
            >
            <div v-if="d.errorMsg" class="error-msg">{{ d.errorMsg }}</div>
          </li>
        </ul>
      </div>
      <h3 v-if="accountCsdcAcode.length > 0" class="title">
        一码通账户
      </h3>
      <div v-if="accountCsdcAcode.length > 0" class="xh_acct_cont">
        <ul>
          <li
            v-for="(d, index) in accountCsdcAcode"
            :class="{ checked: selectedAccounts.indexOf(d) !== -1 }"
            :key="index"
            @click.stop="selYmtClick(d)"
          >
            <p>一码通账户：{{ d.account }}</p>
          </li>
        </ul>
        <div class="yellow-notice">
          <p>
            一码通账户是指中国证券登记结算公司为投资者配发“一套账户”，关联投资者所持有的沪深A、B股等各类证券子账户。注销一码通账户的前提是该一码通账户具有关联关系及对应关系的所有证券子账户都注销。
          </p>
        </div>
      </div>
      <h3 v-if="accountCancellationFundAccountList.length > 0" class="title">
        基金账户
      </h3>
      <div v-if="accountCancellationFundAccountList.length > 0" class="xh_acct_cont">
        <ul>
          <li
            v-for="(d, index) in accountCancellationFundAccountList"
            :class="{ checked: selectedAccounts.indexOf(d) != -1 }"
            :key="index"
            @click.stop="selClick(d)"
          >
            <p>{{ d.account }}</p>
            <em>{{ d.accountName }}</em>
          </li>
        </ul>
        <div v-if="accountLength > 0" class="add_btn">
          <a @click.stop="showMore">展开剩下{{ accountLength }}项</a>
        </div>
      </div>
      <h3 v-if="accountCancellationOtcAccountList.length > 0" class="title">
        OTC账户
      </h3>
      <div v-if="accountCancellationOtcAccountList.length > 0" class="xh_acct_cont otc_xh_account">
        <ul>
          <li
            v-for="(d, index) in accountCancellationOtcAccountList"
            :class="{
              checked: selectedAccounts.indexOf(d) != -1 && business != 'yyxh'
            }"
            :key="index"
            @click.stop="selClick(d)"
          >
            <p>{{ d.account }}</p>
            <em><!-- {{ d.accountName }} --></em>
          </li>
        </ul>
        <div v-if="accountLength > 0" class="add_btn">
          <a @click.stop="showMore">展开剩下{{ accountLength }}项</a>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { rejectChooseAcc } from '../../service/comServiceNew'
import { cloneDeep } from 'lodash'
import { ecbEncrypt } from 'thinkive-hvue/plugin/sm/sm4'

export default {
  props: ['pageParam'],
  data() {
    return {
      ygtUserInfo: $h.getSession('ygtUserInfo', { decrypt: false }),
      business: '',
      clientId: '', // 客户号
      selectedAccounts: [], // 已选择的账户
      isCloseAll: false, // 是否全部注销
      accountCancellationClientList: [], // 客户号
      accountCancellationAssetList: [], // 资金账号
      accountCancellationStockAccountList: [], // 股东户
      accountCancellationFundAccountList: [], // 基金户
      accountCancellationOtcAccountList: [], // otc
      accountCsdcAcode: [], // 一码通账户
      accountLength: 0,
      thirdBankInput: '', // 是否输入银行卡信息
      pwdShow: false, // 是否明文展示
      inputModel: {
        fundpwd: {
          maxlength: 6,
          minlength: 6,
          name: '资金密码',
          value: '',
          format: 'num'
        }
      },
      refLists: ['fundpwd']
    }
  },
  computed: {
    pwdType() {
      return this.pwdShow ? 'tel' : 'password'
    }
  },
  created() {
    this.$store.commit('updateIsWhite', false)
  },
  activated() {
    this.visibleAndScreen()
    console.log('业务类型', this.business)
    this.business = this.$route.query.type
    this.isCloseAll = false
    this.selectedAccounts = []

    if (this.pageParam.length < 1) {
      _hvueAlert({ mes: '未查询到信息', title: '提示' })
    }
    // 转换结果集格式
    this.accountCancellationClientList = this.pageParam[0].accountCancellationClientList
      ? JSON.parse(this.pageParam[0].accountCancellationClientList)
      : []
    this.accountCancellationAssetList = this.pageParam[0].accountCancellationAssetList
      ? JSON.parse(this.pageParam[0].accountCancellationAssetList)
      : []
    this.accountCancellationStockAccountList = this.pageParam[0].accountCancellationStockAccountList
      ? JSON.parse(this.pageParam[0].accountCancellationStockAccountList)
      : []
    this.accountCancellationFundAccountList = this.pageParam[0].accountCancellationFundAccountList
      ? JSON.parse(this.pageParam[0].accountCancellationFundAccountList)
      : []
    this.accountCancellationOtcAccountList = this.pageParam[0].accountCancellationOtcAccountList
      ? JSON.parse(this.pageParam[0].accountCancellationOtcAccountList)
      : []
    this.accountCsdcAcode = this.pageParam[0].accountCsdcAcode
      ? JSON.parse(this.pageParam[0].accountCsdcAcode)
      : []
    this.clientId =
      this.accountCancellationClientList[0].clientId ||
      this.accountCancellationClientList[0].account

    for (let i = 0; i < this.accountCancellationAssetList.length; i++) {
      let el = this.accountCancellationAssetList[i]
      el.checked = false
      el.fundpwd = ''
      this.$set(this.accountCancellationAssetList, i, el)
    }
    /**
    * 证券账户需求说明
    * 账户状态检测（需查询中登）
      沪市证券账户
      1、本地账户状态正常，需检测指定交易情况：
      1）已指定本券商或未指定任一券商，则可以注销本地，也可以同步中登注销
      2）若已指定其他券商，则仅可以注销本地，【同步中登销户】不可勾选
      2、本地状态“中登休眠”，且中登状态“休眠”；则勾选注销该账户需同步注销中登，不可取消【同步中登销户】
      深市证券账户
      1、本地账户状态正常，需检测以下内容：
      1）如果使用信息仅本券商，不存在其他别家券商，则可以注销本地，也可以同步中登注销
      2）若使用信息存在别家券商，则勾选【同步中登销户】时，下方提示：该账户存在其他券商的使用信息，可能导致中登销户失败，如有疑问请联系开户分支机构或客服**********。
      2、本地状态“中登休眠”，且中登状态“休眠”，即可以选只销本地，也可以同步中登销户；默认同步中登销户
      若账户中登状态为销户，本地状态无论什么状态，均默认勾选，不可取消勾选，提示用户“该账户中登状态为注销，已无法使用该账户，需注销”
      其他状态不正常的情况，均不允许销户，不可选择
    */
    for (let i = 0; i < this.accountCancellationStockAccountList.length; i++) {
      let el = this.accountCancellationStockAccountList[i]
      el.checked = false
      el.zdCancellationFlag = '0'
      // buttonAction 限定股东账户的点击状态
      /* 0-不限定，客户自行勾选是否销股东账户、同步中登
        1-不限定，客户自行勾选是否销股东账户、同步中登，选中时默认勾选同步中登(非强制，可取消)
        2-限定如果选中本账户，则仅能销本地(同步中登无法勾选)
        3-限定如果选中本账户，则必须同步中登(同步中登无法取消)
        4-强制勾选销本地，不销中登(默认选中不可取消)
        5-状态不正常，不可勾选
        */
      if (el.buttonAction === '1') {
        el.zdCancellationFlag = '1'
      } else if (el.buttonAction === '2') {
        el.zdChooseDisable = true
        el.errorMsg = '该账户指定交易为其他券商，仅可进行本地注销。'
      } else if (el.buttonAction === '3') {
        el.zdChooseDisable = true
        el.zdCancellationFlag = '0'
        el.errorMsg = '该账户本地和中登均为休眠，需同步中登注销。'
      } else if (el.buttonAction === '4') {
        el.disabled = true
        el.checked = true
        el.errorMsg = '该账户中登状态为注销，已无法使用该账户，需注销。'
        // 自动添加到选中列表
        this.selectedAccounts.push(el)
      } else if (el.buttonAction === '5') {
        el.disabled = true
        el.checked = false
        el.errorMsg =
          '该账户状态异常，请联系开户营业部或投资经理协助规范账户，如有疑问请联系开户分支机构或客服**********。'
      } else if (el.buttonAction === '6') {
        el.errorMsg =
          '该账户存在其他券商的使用信息，可能导致中登销户失败，如有疑问请联系开户分支机构或客服**********。'
      }
      this.$set(this.accountCancellationStockAccountList, i, el)
    }
    for (let i = 0; i < this.accountCancellationFundAccountList.length; i++) {
      let el = this.accountCancellationFundAccountList[i]
      el.checked = false
      this.$set(this.accountCancellationFundAccountList, i, el)
    }
    for (let i = 0; i < this.accountCancellationOtcAccountList.length; i++) {
      let el = this.accountCancellationOtcAccountList[i]
      el.checked = false
      this.$set(this.accountCancellationOtcAccountList, i, el)
    }
    for (let i = 0; i < this.accountCsdcAcode.length; i++) {
      let el = this.accountCsdcAcode[i]
      el.checked = false
      this.$set(this.accountCsdcAcode, i, el)
    }

    // 检查默认勾选后是否需要联动勾选主资金账户和客户号
    this.checkAutoSelectMainAccounts()
  },
  deactivated() {
    this.clearKeypanel()
  },
  methods: {
    // 点击选择账户
    selClick(item) {
      // 检查账户是否被禁用
      if (item.disabled === true) {
        return
      }

      // 检查是否尝试取消勾选港通账户，如果对应的A股账户已被选中，则阻止操作
      if (this.selectedAccounts.indexOf(item) !== -1 && item.accountCancellationType === '2') {
        if (item.exchangeType === 'G') {
          // 检查是否有沪A账户被选中
          const hasShAccount = this.selectedAccounts.some(
            ({ exchangeType, accountCancellationType }) =>
              exchangeType === '1' && accountCancellationType === '2'
          )
          if (hasShAccount) {
            return // 阻止取消勾选沪港通账户
          }
        } else if (item.exchangeType === 'S') {
          // 检查是否有深A账户被选中
          const hasSzAccount = this.selectedAccounts.some(
            ({ exchangeType, accountCancellationType }) =>
              exchangeType === '2' && accountCancellationType === '2'
          )
          if (hasSzAccount) {
            return // 阻止取消勾选深港通账户
          }
        }
        // 检查是否尝试取消勾选特转A账户，如果深A账户已被选中且同步中登销户，则阻止操作
        else if (item.exchangeType === '3' || item.exchangeType === '9') {
          // 检查是否有深A账户被选中且同步中登销户
          const hasSzAccountWithZd = this.selectedAccounts.some(
            ({ exchangeType, accountCancellationType, zdCancellationFlag }) =>
              exchangeType === '2' && accountCancellationType === '2' && zdCancellationFlag === '1'
          )
          if (hasSzAccountWithZd) {
            return // 阻止取消勾选特转A账户
          }
        }
      }

      // 勾选主资金账号，全部勾选
      if (
        item.accountCancellationType === '1' &&
        item.mainFlag === '1' &&
        this.selectedAccounts.indexOf(item) === -1
      ) {
        this.isCloseAll = true
        this.setAllOpen(true)
        return
      }
      // 取消勾选主资金账号，其他账号都取消勾选
      if (
        item.accountCancellationType === '1' &&
        item.mainFlag === '1' &&
        this.selectedAccounts.indexOf(item) !== -1
      ) {
        this.isCloseAll = false
        this.setAllOpen(false)
        return
      }

      // 取消勾选客户号，其他账号都取消勾选
      if (
        item.accountCancellationType === '0' &&
        this.selectedAccounts.indexOf(item) !== -1
      ) {
        this.isCloseAll = false
        this.setAllOpen(false)
        return
      }

      // 如果勾选了客户号或者主资金账号，不可取消勾选别的账号
      if (!['0', '1'].includes(item.accountCancellationType)) {
        const canNotSelect = this.selectedAccounts.some(({ accountCancellationType }) =>
          ['0', '1'].includes(accountCancellationType)
        )
        if (canNotSelect) return
      }

      // 取消勾选其他账号，则取消勾选主资金账号
      if (item.accountCancellationType !== '1' && this.selectedAccounts.indexOf(item) !== -1) {
        this.isCloseAll = false
        for (let i = 0; i < this.accountCancellationAssetList.length; i++) {
          let el = this.accountCancellationAssetList[i]
          if (el.mainFlag === '1') this.selectedAccounts.remove(el)
        }
        // 取消勾选其他账号时，检查是否需要取消勾选一码通账户
        this.checkAndRemoveYmtAccount()
      }
      if (this.selectedAccounts.indexOf(item) !== -1) {
        if (item.accountCancellationType === '3') {
          this.accountCancellationFundAccountList.forEach(it => this.selectedAccounts.remove(it))
          // 检查是否需要取消勾选一码通账户
          this.checkAndRemoveYmtAccount()
        } else if (item.accountCancellationType === '4') {
          this.accountCancellationOtcAccountList.forEach(it => this.selectedAccounts.remove(it))
          // 检查是否需要取消勾选一码通账户
          this.checkAndRemoveYmtAccount()
        } else {
          this.selectedAccounts.remove(item)
          // 如果取消勾选的是深A股票账户，检查是否需要自动取消勾选9特转a和3京a
          if (item.accountCancellationType === '2' && item.exchangeType === '2') {
            const stillHasSzAccount = this.selectedAccounts.some(
              ({ exchangeType, accountCancellationType }) =>
                exchangeType === '2' && accountCancellationType === '2'
            )
            if (!stillHasSzAccount) {
              // 如果没有深A账户被选中，自动取消勾选9特转a和3京a
              this.accountCancellationStockAccountList.forEach(stockItem => {
                if (['3', '9'].includes(stockItem.exchangeType)) {
                  const index = this.selectedAccounts.indexOf(stockItem)
                  if (index !== -1) {
                    this.selectedAccounts.splice(index, 1)
                  }
                }
              })
            }
          }
          // 如果取消勾选的是深沪A账户，自动取消勾选对应的深沪港通账户
          if (item.accountCancellationType === '2') {
            if (item.exchangeType === '1') {
              // 取消勾选沪A账户，自动取消勾选沪港通G账户
              this.accountCancellationStockAccountList.forEach(stockItem => {
                if (stockItem.exchangeType === 'G') {
                  const index = this.selectedAccounts.indexOf(stockItem)
                  if (index !== -1) {
                    this.selectedAccounts.splice(index, 1)
                  }
                }
              })
            } else if (item.exchangeType === '2') {
              // 取消勾选深A账户，自动取消勾选深港通S账户
              this.accountCancellationStockAccountList.forEach(stockItem => {
                if (stockItem.exchangeType === 'S') {
                  const index = this.selectedAccounts.indexOf(stockItem)
                  if (index !== -1) {
                    this.selectedAccounts.splice(index, 1)
                  }
                }
              })
            }
          }
          // 检查是否需要取消勾选一码通账户
          this.checkAndRemoveYmtAccount()
        }
      } else {
        item.zdCancellationFlag && (item.zdCancellationFlag = '0')
        if (item.accountCancellationType === '3') {
          this.accountCancellationFundAccountList.forEach(it => {
            if (!it.disabled) {
              this.selectedAccounts.push(it)
            }
          })
        } else if (item.accountCancellationType === '4') {
          this.accountCancellationOtcAccountList.forEach(it => {
            if (!it.disabled) {
              this.selectedAccounts.push(it)
            }
          })
        } else {
          this.selectedAccounts.push(item)
          // 如果勾选的是buttonAction为3的证券账户，自动勾选同步中登销户
          if (item.accountCancellationType === '2' && item.buttonAction === '3') {
            item.zdCancellationFlag = '1'
          }
          // 如果勾选的是深沪A账户，自动勾选对应的深沪港通账户
          if (item.accountCancellationType === '2') {
            if (item.exchangeType === '1') {
              // 勾选沪A账户，自动勾选沪港通G账户
              this.accountCancellationStockAccountList.forEach(stockItem => {
                if (
                  stockItem.exchangeType === 'G' &&
                  this.selectedAccounts.indexOf(stockItem) === -1 &&
                  !stockItem.disabled
                ) {
                  this.selectedAccounts.push(stockItem)
                }
              })
            } else if (item.exchangeType === '2') {
              // 勾选深A账户，自动勾选深港通S账户
              this.accountCancellationStockAccountList.forEach(stockItem => {
                if (
                  stockItem.exchangeType === 'S' &&
                  this.selectedAccounts.indexOf(stockItem) === -1 &&
                  !stockItem.disabled
                ) {
                  this.selectedAccounts.push(stockItem)
                }
              })
            }
          }
        }
      }

      // 检查是否需要自动勾选主资金账户和客户号
      this.checkAutoSelectMainAccounts()

      // 检查一码通账户的勾选状态
      this.checkAndRemoveYmtAccount()

      /*  let count = 0
      for (let a = 0; a < this.selectedAccounts.length; a++) {
        let element = this.selectedAccounts[a]
        if (element.accountCancellationType == '2') {
          count++
        }
      }
     if (count == this.accountCancellationStockAccountList.length) {
        this.isCloseAll = true
        this.setAllOpen(true)
        return
      } else {
        this.isCloseAll = false
      } */
    },
    selYmtClick(item) {
      if (!this.canSelectYmtAccount()) return
      // 检查账户是否被禁用
      if (item.disabled) return
      if(this.selectedAccounts.indexOf(item) === -1) {
        this.selectedAccounts.push(item)
      } else {
        this.selectedAccounts.remove(item)
      }

    },
    isCheckedZD(stockAcc, stockAccExcType) {
      return this.selectedAccounts.some(
        ({ account, zdCancellationFlag, exchangeType }) =>
          account === stockAcc && stockAccExcType === exchangeType && zdCancellationFlag === '1'
      )
    },
    chooseAccZD({ account, exchangeType, zdChooseDisable }) {
      // 检查是否被禁用
      if (zdChooseDisable === true) {
        return
      }

      this.selectedAccounts = this.selectedAccounts.map(item => {
        if (item.account === account && item.exchangeType === exchangeType) {
          if (item.zdCancellationFlag === '0') {
            item.zdCancellationFlag = '1'
          } else {
            item.zdCancellationFlag = '0'
          }
        }
        return item
      })
      // 9特转a 3京a 如果勾选了深A+中登销户，自动勾选
      const szAccount = this.selectedAccounts.some(
        ({ exchangeType, zdCancellationFlag }) =>
          ['2'].includes(exchangeType) && zdCancellationFlag === '1'
      )
      const autoSelect = this.accountCancellationStockAccountList.some(({ exchangeType }) =>
        ['3', '9'].includes(exchangeType)
      )
      if (szAccount && autoSelect) {
        this.accountCancellationStockAccountList.forEach(item => {
          if (['3', '9'].includes(item.exchangeType)) {
            if (this.selectedAccounts.indexOf(item) === -1 && !item.disabled) {
              this.selectedAccounts.push(item)
            }
          }
        })
      } else if (!szAccount && autoSelect) {
        // 如果取消了深A+中登销户，自动取消勾选9特转a和3京a
        this.accountCancellationStockAccountList.forEach(item => {
          if (['3', '9'].includes(item.exchangeType)) {
            const index = this.selectedAccounts.indexOf(item)
            if (index !== -1) {
              this.selectedAccounts.splice(index, 1)
            }
          }
        })
      }

      const hasAnyZdAccount = this.selectedAccounts.every(
        ({ zdCancellationFlag, accountCancellationType }) =>
          accountCancellationType === '2' && zdCancellationFlag === '1'
      )
      if (!hasAnyZdAccount) {
        // 如果没有任何中登销户被勾选，自动取消勾选一码通账户
        this.accountCsdcAcode.forEach(item => {
          const index = this.selectedAccounts.indexOf(item)
          if (index !== -1) {
            this.selectedAccounts.splice(index, 1)
          }
        })
      }
    },
    // 点击全部注销
    closeAll() {
      this.isCloseAll = !this.isCloseAll
      this.setAllOpen(this.isCloseAll)
    },
    setAllOpen(flag) {
      for (let i = 0; i < this.accountCancellationAssetList.length; i++) {
        let el = this.accountCancellationAssetList[i]
        let method = flag ? 'push' : 'remove'
        if (this.selectedAccounts.indexOf(el) === -1 && method === 'push' && !el.disabled) {
          this.selectedAccounts.push(el)
        } else if (method === 'remove') {
          this.selectedAccounts.remove(el)
        }
      }
      for (let i = 0; i < this.accountCancellationStockAccountList.length; i++) {
        let el = this.accountCancellationStockAccountList[i]
        let method = flag ? 'push' : 'remove'
        if (this.selectedAccounts.indexOf(el) === -1 && method === 'push' && !el.disabled) {
          el.zdCancellationFlag && (el.zdCancellationFlag = '0')
          this.selectedAccounts.push(el)
        } else if (method === 'remove') {
          this.selectedAccounts.remove(el)
        }
      }
      for (let i = 0; i < this.accountCancellationFundAccountList.length; i++) {
        let el = this.accountCancellationFundAccountList[i]
        let method = flag ? 'push' : 'remove'
        if (this.selectedAccounts.indexOf(el) === -1 && method === 'push' && !el.disabled) {
          this.selectedAccounts.push(el)
        } else if (method === 'remove') {
          this.selectedAccounts.remove(el)
        }
      }
      for (let i = 0; i < this.accountCancellationOtcAccountList.length; i++) {
        let el = this.accountCancellationOtcAccountList[i]
        let method = flag ? 'push' : 'remove'
        if (this.selectedAccounts.indexOf(el) === -1 && method === 'push' && !el.disabled) {
          this.selectedAccounts.push(el)
        } else if (method === 'remove') {
          this.selectedAccounts.remove(el)
        }
      }
      // 处理一码通账户
      for (let i = 0; i < this.accountCsdcAcode.length; i++) {
        let el = this.accountCsdcAcode[i]
        let method = flag ? 'push' : 'remove'
        if (this.selectedAccounts.indexOf(el) === -1 && method === 'push' && !el.disabled && this.canSelectYmtAccount()) {
          this.selectedAccounts.push(el)
        } else if (method === 'remove') {
          this.selectedAccounts.remove(el)
        }
      }
    },
    // 获取银行信息
    getBankInfo(fundAccount) {
      const xhBankList = $h.getSession('xhBankList') || []
      const bankInfo = xhBankList.find(bank => bank.fundAccount === fundAccount)
      if (bankInfo && bankInfo.bankAccount) {
        // 银行卡号脱敏处理：只显示前四位和后四位
        const bankAccount = bankInfo.bankAccount
        if (bankAccount.length > 8) {
          const maskedAccount =
            bankAccount.substring(0, 4) +
            ' **** **** ' +
            bankAccount.substring(bankAccount.length - 4)
          return {
            ...bankInfo,
            bankAccount: maskedAccount
          }
        }
      }
      return bankInfo || null
    },
    checkSubmit() {
      if (this.selectedAccounts.length === 0) {
        _hvueToast({
          mes: '请选择账户'
        })
        return false
      }
      return this.selectedAccounts.every(({ accountCancellationType, fundpwd = '' }) => {
        if (accountCancellationType === '1') {
          if (fundpwd === '') {
            _hvueToast({ mes: '请输入资金密码' })
            return false
          } else if (!/^\d{6}$/.test(fundpwd)) {
            _hvueToast({ mes: '请输入六位数字资金密码' })
            return false
          }
          return true
        } else {
          return true
        }
      })
    },
    doCheck() {
      // 执行下一步
      return new Promise((resolve, reject) => {
        if (this.checkSubmit()) {
          rejectChooseAcc({
            serivalId: this.$parent.publicParam.serivalId
          })
            .then(({ error_no, error_info }) => {
              if (error_no === '0') {
                for (let a = 0; a < this.selectedAccounts.length; a++) {
                  let element = this.selectedAccounts[a]
                  if (element.accountCancellationType === '1') {
                    this.thirdBankInput = '1'
                  }
                }
                resolve()
              } else {
                return Promise.reject(new Error(error_info))
              }
            })
            .catch(e => {
              _hvueToast({ mes: e.message })
            })
        }
      })
    },
    /**
     * 请求参数打包
     */
    reqParamPackage() {
      // 业务请求参数封装
      const sm4Key = $hvue.config.sm4Key
      const accountList = cloneDeep(this.selectedAccounts)
      const accountInfo = accountList.map(it => {
        if (it.fundpwd) {
          it.fundpwd = ecbEncrypt(sm4Key.substring(8, sm4Key.length - 8), it.fundpwd)
        }
        return it
      })
      let params = {
        mobile: this.ygtUserInfo.mobile,
        accountInfo: JSON.stringify(accountInfo)
      }
      if (this.thirdBankInput === '1') {
        params.thirdBankInput = '1'
        params.bankAccount = $h.getSession('xhBankAccount')
        params.bankPwd = ''
        params.bankNo = $h.getSession('xhBankNo')
      }
      return params
    },
    // 表单参数打包
    putFormData() {
      const sm4Key = $hvue.config.sm4Key
      let bankCardInfo = []
      bankCardInfo = this.selectedAccounts
        .filter(({ accountCancellationType }) => accountCancellationType === '1')
        .map(item => {
          const xhBankList = $h.getSession('xhBankList') || []
          const bankInfo = xhBankList.filter(({ ...it }) => item.account === it.fundAccount)
          if (bankInfo && bankInfo.length > 0) {
            return {
              bankAccount: bankInfo[0].bankAccount,
              bankPwd: '',
              bankNo: bankInfo[0].bankNo,
              fundAccount: item.account,
              fundAccountPwd: ecbEncrypt(sm4Key.substring(8, sm4Key.length - 8), item.fundpwd)
            }
          } else {
            return {
              bankAccount: '',
              bankPwd: '',
              bankNo: '',
              fundAccount: item.account,
              fundAccountPwd: ecbEncrypt(sm4Key.substring(8, sm4Key.length - 8), item.fundpwd)
            }
          }
        })
      let formData = {
        cancellationChooseAccount: JSON.stringify(this.selectedAccounts)
      }
      if (this.thirdBankInput === '1') {
        formData.bankCardInfo = bankCardInfo
      } else {
        formData.bankCardInfo = []
      }
      return formData
    },
    visibleAndScreen() {
      document.addEventListener('visibilitychange', this.visibilitychange)
    },
    visibilitychange() {
      var isHidden = document.hidden
      if (isHidden) {
        this.clearKeypanel()
      }
    },
    clearKeypanel() {
      for (let ref of this.refLists) {
        this.inputModel[ref].value = ''
        this.$refs[ref] &&
          this.$refs[ref][0] &&
          this.$refs[ref][0].setValue &&
          this.$refs[ref][0].setValue('')
        this.$refs[ref] &&
          this.$refs[ref][0] &&
          this.$refs[ref][0].keyboard &&
          this.$refs[ref][0].keyboard.close()
      }
    },
    /**
     * 检查一码通账户是否可以勾选
     * 1、检测本地证券账户是否全部勾选中登销户，且中登除本地账户外再无其他非销户的证券账户，则一码通账户可勾选。
     * 2、本地证券账户未全部勾选中登销户时，一码通账户置灰不可勾选
     * 3、检测到中登还有其他非销户账户，一码通账户置灰不可勾选
     * @returns {boolean} 是否可以勾选一码通账户
     */
    canSelectYmtAccount() {
      return this.accountCancellationStockAccountList.every(it => {
        if (!['1', '2'].includes(it.exchangeType)) return true
        if (this.selectedAccounts.indexOf(it) === -1) return false
        // buttonAction为6的账户，即使勾选了同步中登销户，也不能勾选一码通账户
        if (it.buttonAction === '6') return false
        console.log(this.selectedAccounts.find(({ account, exchangeType }) => {
            return ['1', '2'].includes(exchangeType) && it.account === account
          }))
        return this.selectedAccounts.find(({ account, exchangeType }) => {
            return ['1', '2'].includes(exchangeType) && it.account === account
          }).zdCancellationFlag === '1'
      })
    },
    /**
     * 检查是否需要自动勾选主资金账户和客户号
     * 当所有相关账户类型都被勾选时，自动勾选主资金账户和客户号
     */
    checkAutoSelectMainAccounts() {
      // 检查是否所有证券账户、基金账户和OTC账户都被选中
      const allStockSelected =
        this.accountCancellationStockAccountList.length > 0 &&
        this.accountCancellationStockAccountList.every(
          stockItem => this.selectedAccounts.indexOf(stockItem) !== -1
        )
      const allFundSelected =
        this.accountCancellationFundAccountList.length > 0 &&
        this.accountCancellationFundAccountList.every(
          fundItem => this.selectedAccounts.indexOf(fundItem) !== -1
        )
      const allOtcSelected =
        this.accountCancellationOtcAccountList.length > 0 &&
        this.accountCancellationOtcAccountList.every(
          otcItem => this.selectedAccounts.indexOf(otcItem) !== -1
        )

      // 检查是否有任何账户类型存在且被全选
      const hasStockAccounts = this.accountCancellationStockAccountList.length > 0
      const hasFundAccounts = this.accountCancellationFundAccountList.length > 0
      const hasOtcAccounts = this.accountCancellationOtcAccountList.length > 0

      // 如果存在的账户类型都被全选，自动勾选主资金账户和客户号
      const shouldAutoSelect = 
        (!hasStockAccounts || allStockSelected) &&
        (!hasFundAccounts || allFundSelected) &&
        (!hasOtcAccounts || allOtcSelected) &&
        (hasStockAccounts || hasFundAccounts || hasOtcAccounts)

      if (shouldAutoSelect) {
        // 自动勾选主资金账户（需要验证是否可以勾选）
        const mainAssetAccount = this.accountCancellationAssetList.find(
          assetItem => assetItem.mainFlag === '1'
        )
        if (mainAssetAccount &&
            this.selectedAccounts.indexOf(mainAssetAccount) === -1 &&
            !mainAssetAccount.disabled) {
          this.selectedAccounts.push(mainAssetAccount)
        }

        // 自动勾选客户号（需要验证是否可以勾选）
        const customerAccount = this.accountCancellationAssetList.find(
          assetItem => assetItem.accountCancellationType === '0'
        )
        if (customerAccount &&
            this.selectedAccounts.indexOf(customerAccount) === -1 &&
            !customerAccount.disabled) {
          this.selectedAccounts.push(customerAccount)
        }

        // 自动勾选一码通账户（需要验证是否可以勾选）
        if (this.canSelectYmtAccount()) {
          this.accountCsdcAcode.forEach(ymtAccount => {
            if (this.selectedAccounts.indexOf(ymtAccount) === -1 && !ymtAccount.disabled) {
              this.selectedAccounts.push(ymtAccount)
            }
          })
        }

        this.isCloseAll = true
      }
    },
    // 检查并取消勾选一码通账户
    checkAndRemoveYmtAccount() {
      // 检查是否还有证券账户、基金账户或OTC账户被选中
      const hasStockAccount = this.selectedAccounts.some(
        account => account.accountCancellationType === '2'
      )
      const hasFundAccount = this.selectedAccounts.some(
        account => account.accountCancellationType === '3'
      )
      const hasOtcAccount = this.selectedAccounts.some(
        account => account.accountCancellationType === '4'
      )

      // 如果没有任何证券、基金或OTC账户被选中，或者不满足一码通账户勾选条件，则取消勾选一码通账户
      if ((!hasStockAccount && !hasFundAccount && !hasOtcAccount) || !this.canSelectYmtAccount()) {
        this.accountCsdcAcode.forEach(ymtAccount => {
          this.selectedAccounts.remove(ymtAccount)
        })
      }
    }
  }
}
</script>

<style scoped>
.error-msg {
  color: #ff4757;
  font-size: 12px;
  margin-top: 5px;
  line-height: 1.4;
}

.disabled {
  opacity: 0.5;
  cursor: not-allowed !important;
}

.disabled .icon_radio {
  cursor: not-allowed !important;
}

.icon_radio.disabled {
  opacity: 0.5;
  cursor: not-allowed !important;
}

.yellow-notice {
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 4px;
  padding: 12px;
  margin-top: 10px;
}

.yellow-notice p {
  margin: 0;
  color: #856404;
  font-size: 14px;
  line-height: 1.5;
}

.bank-info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.bank-info-row .bank-name {
  color: #333;
}

.bank-info-row .bank-account {
  color: #666;
}
</style>
