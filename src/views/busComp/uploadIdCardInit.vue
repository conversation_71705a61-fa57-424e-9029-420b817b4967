<!-- 身份证上传组件 -->
<template>
  <div v-cloak>
    <!--头部 -->
    <!-- <headComponent headerTitle="证件上传"></headComponent> -->
    <div class="content" v-if="!showSelBox">
      <div class="info_ctpage">
        <h5 v-show="!positive.uploaded && !negative.uploaded" class="title">
          请上传身份证影像获取信息
        </h5>
        <h5 v-show="positive.uploaded || negative.uploaded" class="title">
          请核对身份证照片及信息，若有误请点击修改
        </h5>
        <div class="upload_cont">
          <div class="upload_pic">
            <div class="pic">
              <img :src="positive.src" />
            </div>
            <div class="info">
              <a
                v-show="!positive.uploaded"
                class="btn"
                @click.stop="typePop('show', '4')"
                >点击拍摄/上传人像面</a
              >
              <a
                v-show="positive.uploaded"
                class="ret_btn"
                @click.stop="typePop('show', '4')"
              ></a>
            </div>
          </div>

          <div class="input_form" v-show="positive.uploaded">
            <div class="ui field text">
              <span class="ui label">姓名</span>
              <input
                class="ui input"
                type="text"
                maxlength="14"
                v-model.trim="ocrUserInfo.userName"
              />
              <a
                class="txt_close"
                @click.stop="ocrUserInfo.userName = ''"
                v-show="ocrUserInfo.userName != ''"
              ></a>
            </div>
            <div class="ui field text">
              <span class="ui label">身份证号</span>
              <input
                class="ui input"
                type="text"
                maxlength="18"
                v-model.trim="ocrUserInfo.identityNum"
                @input="changeIdnoX"
              />
              <a
                class="txt_close"
                @click.stop="ocrUserInfo.identityNum = ''"
                v-show="ocrUserInfo.identityNum != ''"
              ></a>
            </div>
            <div class="ui field text">
              <span class="ui label">性别</span>
              <input
                class="ui input"
                type="text"
                style="color: #666"
                readonly
                maxlength="2"
                v-model="getSexName"
              />
            </div>
            <div class="ui field text">
              <span class="ui label">民族</span>
              <div class="ui dropdown" @click.stop="showSelBox = true">
                <strong>{{ ocrUserInfo.ethnicName }}</strong>
              </div>
            </div>
            <div class="ui field text">
              <span class="ui label">证件地址</span>
              <multLineInput
                class="teare01 needsclick"
                v-model.trim="ocrUserInfo.papersAddr"
                :maxlength="32"
              ></multLineInput>
            </div>
          </div>
        </div>

        <div class="upload_cont">
          <div class="upload_pic">
            <div class="pic">
              <img :src="negative.src" />
            </div>
            <div class="info">
              <a
                v-show="!negative.uploaded"
                class="btn"
                @click.stop="typePop('show', '5')"
                >点击拍摄/上传国徽面</a
              >
              <a
                v-show="negative.uploaded"
                class="ret_btn"
                @click.stop="typePop('show', '5')"
              ></a>
            </div>
          </div>

          <div class="input_form" v-show="negative.uploaded">
            <div class="ui field text">
              <span class="ui label">签发机关</span>
              <input
                class="ui input"
                type="text"
                maxlength="32"
                v-model.trim="ocrUserInfo.signOffice"
              />
              <a
                class="txt_close"
                @click.stop="ocrUserInfo.signOffice = ''"
                v-show="ocrUserInfo.signOffice != ''"
              ></a>
            </div>
            <div class="ui field text">
              <div class="ui label">有效期</div>
              <div class="date_input">
                <h-datetime
                  class="dropdown"
                  title="起始日期"
                  type="date"
                  v-model.trim="ocrUserInfo.validityBegin"
                  start-year="1900"
                  :end-date="chooseEndDate"
                ></h-datetime>
                <span class="line"></span>
                <h-datetime
                  :readonly="longTimeChecked"
                  class="dropdown"
                  title="结束日期"
                  type="date"
                  v-model.trim="ocrUserInfo.validityEnd"
                  start-year="2020"
                  end-year="3000"
                ></h-datetime>
                <span class="long_span">
                  <span
                    class="icon_check"
                    :class="{ checked: longTimeChecked }"
                    @click.stop="longTimeChecked = !longTimeChecked"
                    >长期</span
                  >
                </span>
              </div>
            </div>
          </div>
        </div>

        <p class="photo_tips">
          <span>提供的证件信息仅用于身份实名验证，我们将严格保护您的个人隐私安全，并仅用于此目的。</span>
        </p>
        <div class="ce_btn" v-show="positive.uploaded && negative.uploaded">
          <a v-throttle class="ui button block rounded" @click.stop="nextStep"
            >确认并提交</a
          >
        </div>
      </div>
      <!-- 选择上传方式 -->
      <getImgBoxThs
          v-if="fromTHSGB"
          ref="getImgBoxThs"
          :showSelImgBox="showSelImgBox"
          :showAlbum="true"
          @getImgCallBack="getImgCallBack"
      ></getImgBoxThs>
      <div v-else v-show="showType" style="display: none">
        <div class="ui dialog-overlay"></div>
        <div class="upload_btn">
          <ul>
            <li>
              <a @click.stop="getImg('pai')">拍摄</a>
            </li>
            <li>
              <a @click.stop="getImg('phone')">从相册选择上传</a>
            </li>
          </ul>
          <div class="cancel_btn">
            <a @click.stop="typePop('hide', '')">取消</a>
          </div>
        </div>
      </div>
      <!-- 上传提示 -->
      <div v-show="isFirstClick">
        <!-- 遮罩层  -->
        <div class="ui dialog-overlay"></div>
        <!-- 弹出层  -->
        <div class="ui dialog fade in" style="width: 88%; left: 6%">
          <div class="ui dialog-cnt">
            <h4>证件上传示例</h4>
            <div>
              <div class="photo_tipbox">
                <img src="@/assets/images/photo_tips.png" />
              </div>
            </div>
          </div>
          <div class="ui dialog-btn">
            <button class="ui button block" @click.stop="isFirstClick = false">
              我知道了
            </button>
          </div>
        </div>
      </div>
    </div>
    <selBox
      v-if="showSelBox"
      v-model.trim="showSelBox"
      title="民族"
      category="ismp.ethnicname"
      :defaultStr="ocrUserInfo.ethnic"
      @selCallback="selCallback"
    ></selBox>
  </div>
</template>

<script>
import { idCardToBirthday, idCardToSex, queryDictionary } from '@/common/util'
import getImgBoxThs from '@/components/getImg_ths'
import headComponent from '@/components/headComponent'; // 头部
import multLineInput from '@/components/multLineInput'
import selBox from '@/components/selBox'
import { uploadImg } from '@/service/comServiceNew'
import { getPlatformValue } from 'thinkive-hvue'

export default {
  components: {
    headComponent,
    selBox,
    multLineInput,
    getImgBoxThs
  },
  props: ['pageParam'],
  name: 'uploadIdCardInit',
  data () {
    return {
      chooseEndDate: new Date().format('yyyy-MM-dd'), // 可选起始日期的限制到今天
      serivalId: this.$parent.publicParam.serivalId,
      isFirstClick: true, // 是否第一次点击上传
      platform: '', // 操作渠道
      showType: false, // 是否展示上传方法选择框
      imgType: '', // 上传图片类型 4上传人像面 5上传国徽面
      mediaCode: '',

      showSelBox: false, // 民族
      positive: {
        src: require('@/assets/images/id_icon01.png'),
        uploaded: false
      },
      negative: {
        src: require('@/assets/images/id_icon02.png'),
        uploaded: false
      },
      isEndDate: false,
      longTimeChecked: false,
      ocrUserInfo: $h.getSession('ocrUserInfo') || {},
      backParam207: {},
      backParam208: {},
      backParam: [],
      fromTHSGB: $h.getSession('fromTHSGB'), // 渠道同花顺公版
      showSelImgBox: 0 // 是否显示 1正面 2反面
    }
  },
  activated () {
    // 清空页面数据
    this.backParam207 = {}
    this.backParam208 = {}
    this.backParam = []
    this.ocrUserInfo = {}
    this.positive = {
      src: require('@/assets/images/id_icon01.png'),
      uploaded: false
    }
    this.negative = {
      src: require('@/assets/images/id_icon02.png'),
      uploaded: false
    }
    this.isEndDate = false
    this.longTimeChecked = false
    // this.$store.commit('updateIsShowHead', false) // 隐藏头部
    this.$store.commit('updateBusinessNextBtnStatus', false) // 隐藏下一步按钮
    window.phoneBackBtnCallBack = this.pageBack
    window.imgCallBack = this.getImgCallBack
  },
  computed: {
    getSexName () {
      let identityNum = this.ocrUserInfo.identityNum
      this.$set(this.ocrUserInfo, 'birthday', idCardToBirthday(identityNum))
      this.$set(this.ocrUserInfo, 'sex', idCardToSex(identityNum))
      return this.ocrUserInfo.sex === 1 ? '女' : '男'
    }
  },
  watch: {
    longTimeChecked (newVal) {
      if (newVal) {
        if (this.ocrUserInfo.validityEnd != '3000-12-31') {
          this.$set(
            this.ocrUserInfo,
            'orcValidityEnd',
            this.ocrUserInfo.validityEnd
          )
        }
        this.$set(this.ocrUserInfo, 'validityEnd', '3000-12-31')
      } else {
        this.$set(
          this.ocrUserInfo,
          'validityEnd',
          this.ocrUserInfo.orcValidityEnd
        )
      }
    }
  },
  mounted () {
    window.phoneBackBtnCallBack = this.pageBack
    window.imgCallBack = this.getImgCallBack
    this.platform = getPlatformValue()
  },
  methods: {
    doCheck () {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        if (this.checkSubmit()) {
          // 可以下一步
          resolve()
        }
      })
    },
    /**
     * 请求参数打包
     */
    reqParamPackage () {
      let params = this.ocrUserInfo // 提交需要的参数
      return params
    },
    putFormData () {
      let formData = {
        uploadIdCard: [this.backParam207, this.backParam208],
        ocrUserInfo: this.ocrUserInfo
      } // 需要保存的表单数据
      return formData
    },
    changeIdnoX () {
      if (this.ocrUserInfo.identityNum) {
        this.ocrUserInfo.identityNum = this.ocrUserInfo.identityNum.replace(
          'x',
          'X'
        )
      }
    },
    typePop (isShow, type) {
      this.imgType = type
      this.mediaCode = type == '4' ? '207' : '208'
      // 同花顺公版走同花顺上传
      if (this.fromTHSGB) {
        this.$refs.getImgBoxThs.getImg(type)
      } else {
        this.showType = isShow == 'show'
      }
    },
    checkSubmit () {
      if (!this.positive.uploaded || !this.negative.uploaded) {
        _hvueToast({
          mes: '请上传身份证'
        })
        return false
      }
      if (!this.ocrUserInfo.userName) {
        _hvueToast({
          mes: '姓名不能为空'
        })
        return false
      }
      if (
        !/^[\u4E00-\u9FA5]{2,14}([·•]?[\u4E00-\u9FA5]+)*$/.test(
          this.ocrUserInfo.userName
        )
      ) {
        _hvueToast({
          mes: '姓名格式不正确'
        })
        return false
      }
      if (!this.ocrUserInfo.identityNum) {
        _hvueToast({
          mes: '身份证号不能为空'
        })
        return false
      }
      if (!/^([\d]{17}[\dXx]|[\d]{15})$/.test(this.ocrUserInfo.identityNum)) {
        _hvueToast({
          mes: '身份证号格式不正确'
        })
        return false
      }

      if (!this.ocrUserInfo.ethnic) {
        _hvueToast({
          mes: '民族不能为空'
        })
        return false
      }
      if (!this.ocrUserInfo.papersAddr) {
        _hvueToast({
          mes: '住址不能为空'
        })
        return false
      }
      if (
        !/^[\u4E00-\u9FA5\w\d\-\s/()（）]{8,100}$/.test(
          this.ocrUserInfo.papersAddr
        )
      ) {
        _hvueToast({
          mes: '住址格式不正确'
        })
        return false
      }
      if (!this.ocrUserInfo.signOffice) {
        _hvueToast({
          mes: '发证机关不能为空'
        })
        return false
      }
      if (!$h.isCn(this.ocrUserInfo.signOffice)) {
        _hvueToast({
          mes: '发证机关格式不正确'
        })
        return false
      }

      let beginDate = this.ocrUserInfo.validityBegin
      let endDate = this.ocrUserInfo.validityEnd
      let dateExp = /\d{4}-\d{2}-\d{2}/
      // 判断有效期格式是否正确
      if (
        !dateExp.test(beginDate) ||
        !dateExp.test(endDate) ||
        Date.parse(beginDate.replace(/\./g, '-')) > Date.now() ||
        beginDate === endDate
      ) {
        _hvueToast({
          mes: '请设置正确的身份证有效期限'
        })
        return false
      }
      // 判断身份证是否过期
      if (Date.parse(endDate.replace(/\./g, '-')) < Date.now()) {
        _hvueToast({
          mes: '您的身份证已过期'
        })
        return false
      }
      return true
    },
    selCallback (a) {
      this.$set(this.ocrUserInfo, 'ethnicName', a.data.value)
      this.$set(this.ocrUserInfo, 'ethnic', a.data.key)
    },
    getImg (actionType) {
      // 调用壳子的上传图片组件
      let param = {
        funcNo: '60013', // 1：服务器是否需要ocr识别(is_orc_funcno等于60013)   0：不需要(is_orc_funcno等于60014)
        action: actionType, // phone：相册；pai:拍照界面
        imgType: this.imgType, // 需上传图片类型 3大头照 4 身份证正面 5 反面
        requestParam:
          'funcNo=1005378&version=1&isOcr=1&userId=' +
          $h.getSession('ygtUserInfo', { decrypt: false }).userId,
        url: SERVER_URL + ';jsessionid=' + $h.getSession('jsessionid') + '?',
        userId: $h.getSession('ygtUserInfo', { decrypt: false }).userId,
        isUpload: 0, // 件照图片上传方式（默认1由原生上传）
        moduleName: 'open' // 必须为open
      }
      let result = $h.callMessageNative(param)
      this.showType = false
      if (result.error_no != 0) {
        _hvueToast({ mes: result.error_info })
      }
    },
    getImgCallBack (imgInfo) {
      // 原生拍照后回调h5的方法
      if (imgInfo.ocrInfo) {
        // app端 已由原生上传图片
        // this.echoOrcInfo(imgInfo.base64, imgInfo.ocrInfo);
      } else {
        // h5本地上传base64到bus
        imgInfo.base64 = this.filterBase64Pre(
          imgInfo.frontBase64 || imgInfo.backBase64 || imgInfo.base64
        )
        let param = {
          file_type: 'image',
          file_name:
            $h.getSession('ygtUserInfo', { decrypt: false }).userId + '.jpg',
          isOcr: 1,
          mediaCode: this.mediaCode,
          file_data: encodeURIComponent(imgInfo.base64),
          serivalId: this.serivalId,
          businessCode: this.$route.query.type,
          flow_current_step: 'uploadIdCard',
          ocrQuality: $hvue.config.ocrQuality
        }
        uploadImg(param)
          .then((data) => {
            if (data.error_no === '0') {
              if (this.mediaCode == '207') {
                this.backParam207 = data.uploadInfo[0]
              } else {
                this.backParam208 = data.uploadInfo[0]
              }
              this.echoOrcInfo(data.ocrInfo[0].cropImgId, data.ocrInfo[0])
            } else {
              return Promise.reject(new Error(data.error_info))
            }
          })
          .catch((e) => {
            _hvueToast({ mes: e.message })
          })
      }
    },
    // 将bus返回ocr识别信息做回显
    echoOrcInfo (base64, result) {
      if (result.identityNum) {
        this.$set(this.ocrUserInfo, 'identityNum', result.identityNum)
        this.$set(
          this.ocrUserInfo,
          'birthday',
          idCardToBirthday(result.identityNum)
        )
        this.$set(this.ocrUserInfo, 'sex', idCardToSex(result.identityNum))
        this.$set(this.ocrUserInfo, 'userName', result.name)
        let ethnicName =
          result.ethnicname.indexOf('族') > -1
            ? result.ethnicname
            : result.ethnicname
              ? result.ethnicname + '族'
              : '汉族'
        this.$set(this.ocrUserInfo, 'ethnicName', ethnicName)
        let that = this
        queryDictionary(
          { type: 'ismp.ethnicname', value: this.ocrUserInfo.ethnicName },
          function (d) {
            that.$set(that.ocrUserInfo, 'ethnic', d.key)
            // that.ocrUserInfo.ethnic = d.key;
          }
        )
        this.$set(this.ocrUserInfo, 'papersAddr', result.papersAddr)
        this.positive.uploaded = true
        this.positive.src = 'data:image/jpeg;base64,' + base64
      } else if (
        result.validityBegin ||
        result.validityEnd ||
        result.signOffice
      ) {
        this.$set(this.ocrUserInfo, 'signOffice', result.signOffice)
        this.$set(
          this.ocrUserInfo,
          'validityBegin',
          result.validityBegin.replace(/\./g, '-')
        )
        this.$set(
          this.ocrUserInfo,
          'validityEnd',
          result.validityEnd.replace(/\./g, '-')
        )

        this.negative.uploaded = true
        this.negative.src = 'data:image/jpeg;base64,' + base64
      } else {
        _hvueToast({ mes: '未识别的身份证图片' })
      }
    },
    filterBase64Pre (ndata) {
      let arr = ndata.split('base64,')
      return arr[arr.length - 1]
    },
    // 返回
    pageBack () {
      _hvueConfirm({
        mes: '身份信息确认未完成，是否返回上一步？',
        opts: [
          {
            txt: '取消',
            color: false
          },
          {
            txt: '确定',
            color: true,
            callback: () => {
              // 调用父类返回
              this.$parent.back()
            }
          }
        ]
      })
    },
    // 调用父组件的下一步事件
    nextStep () {
      this.$parent.emitNextEvent()
    }
  }
}
</script>
<style>
.hui-datetime-input {
  width: auto;
}
</style>
