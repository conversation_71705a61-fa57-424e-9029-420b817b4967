<template>
  <div>
    <div v-if="!showSelBox" class="pword_box">
      <h5 class="title">当前资金账户： {{ygtUserInfo.fundAccount}}</h5>
      <div class="input_form">
        <div class="ui field text">
          <label class="ui label">密码类型</label>
          <div class="ui dropdown" @click.stop="showSelBox = true">
            <strong>{{pwdTypeVal}}</strong>
          </div>
        </div>
      </div>
      <div class="input_form mt10">
        <div class="ui field text pword">
          <label class="ui label">新密码</label>
          <input
            v-model.trim="pwdInputModel.newPwd.value"
            :maxlength="pwdInputModel.newPwd.maxlength"
            :type="pwdType"
            class="ui input"
            placeholder="请输入新密码"
          />
          <a
            class="txt_close"
            @click.stop="pwdInputModel.newPwd.value=''"
            v-show="pwdInputModel.newPwd.value!=''"
          ></a>
          <a class="icon_eye" :class="{show: pwdShow}" @click.stop="pwdShow = !pwdShow"></a>
        </div>
        <div class="ui field text">
          <label class="ui label">再次输入</label>
          <input
            v-model.trim="pwdInputModel.reNewPwd.value"
            :maxlength="pwdInputModel.reNewPwd.maxlength"
            :type="pwdType"
            class="ui input"
            placeholder="请再次输入新密码"
          />
          <a
            class="txt_close"
            @click.stop="pwdInputModel.reNewPwd.value=''"
            v-show="pwdInputModel.reNewPwd.value!=''"
          ></a>
        </div>
      </div>
      <div class="ce_btn mt20">
        <a v-throttle class="ui button block rounded" @click.stop="verifyPwdInput">确认修改</a>
      </div>
    </div>
    <selBox
      v-if="showSelBox"
      v-model.trim="showSelBox"
      title="密码类型"
      category="ismp.pwdType"
      :defaultStr="pwdTypeVal"
      @selCallback="selCallback"
    ></selBox>
  </div>
</template>

<script>
import selBox from '@/components/selBox' // 选择器
import { checkInput } from '@/common/util'
export default {
  props: ['pageParam'],
  components: { selBox },
  data () {
    return {
      ygtUserInfo: $h.getSession('ygtUserInfo', {decrypt: false}),
      loginType: $h.getSession('loginType') || '1', // 账号类型 1普通账号 2信用账号
      pwdShow: false, // 密码输入框是否明文展示
      pwdTypeKey: '', // 密码类型数据字典值
      pwdTypeVal: '', // 密码类型中文描述
      showSelBox: false, // 是否展示选择器
      pwdInputModel: {
        newPwd: {
          name: '新密码',
          value: '',
          minlength: 6,
          maxlength: 6,
          format: 'pwd'
        },
        reNewPwd: {
          name: '再次输入密码',
          value: '',
          minlength: 6,
          maxlength: 6,
          format: 'num'
        }
      },
      pwdRefs: ['newPwd', 'reNewPwd']
    }
  },
  computed: {
    pwdType () {
      return this.pwdShow ? 'tel' : 'password'
    }
  },
  activated () {
    this.$store.commit('updateBusinessNextBtnStatus', false) // 隐藏下一步按钮
    // 清空输入框
    for (const key in this.pwdInputModel) {
      if (this.pwdInputModel.hasOwnProperty(key)) {
        const element = this.pwdInputModel[key]
        element.value = ''
      }
    }
  },
  methods: {
    verifyPwdInput () {
      if (this.pwdTypeVal === '' || this.pwdTypeKey === '') {
        _hvueToast({ mes: '请选择需要重置密码的类型' })
        return
      }
      let flag = checkInput(this.pwdInputModel.newPwd)
      if (flag != '') {
        return
      }
      flag = checkInput(this.pwdInputModel.reNewPwd)
      if (flag != '') {
        return
      }
      if (
        this.pwdInputModel.newPwd.value == this.pwdInputModel.reNewPwd.value
      ) {
        this.$parent.emitNextEvent()
      } else {
        _hvueToast({ mes: '两次输入的密码不一致' })
      }
    },
    doCheck () {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        resolve()
      })
    },
    /**
     * 请求参数打包
     */
    reqParamPackage () {
      // 业务请求参数封装
      let params = {
        passwordType: this.pwdTypeKey,
        newPassword: this.pwdInputModel.newPwd.value
      } // 提交需要的参数
      return params
    },
    putFormData () {
      let formData = {
        pwdReset: {
          bussinessType: this.loginType,
          passwordType: this.pwdTypeKey,
          newPassword: this.pwdInputModel.newPwd.value
        }
      } // 需要保存的表单数据
      return formData
    },
    selCallback (a) {
      this.pwdTypeKey = a.data.key
      this.pwdTypeVal = a.data.value
    }
  }
}
</script>
