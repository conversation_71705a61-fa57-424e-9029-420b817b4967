<!-- 撤指定-选择账户 -->
<template>
  <div>
      <div class="agree_main">
          <h5 class="title">请选择您想要撤指定的账户</h5>
          <ul class="account_list">
              <li
                  :class="{
                    disable: item.holderStatus != '0',
                  }"
                  v-for="(item, index) in pageParam"
                  :key="index"
              >
                  <p>
                    <span>
                        沪A：{{ item.stockAccount }}
                    </span>
                  </p>
                  <span
                      v-if="item.holderStatus !== '0'"
                      class="status"
                  >
                      {{
                          item.holderStatus | filterAccStatus(item.holderStatus)
                      }}
                  </span>
                  <span
                      v-else
                      class="status icon_radio checked"
                  >
                      {{item.itemName}}
                  </span>
              </li>
          </ul>
      </div>
  </div>
</template>

<script>
export default {
  props: ['pageParam'],
  data () {
    return {
        selectAccountList: [],
        nextFlag: false,
    }
  },
  activated () {
    let list = this.pageParam
    this.selectAccountList = [];
    list.map(item => {
      if (item.holderStatus === '0') {
        this.selectAccountList.push({market: item.stkbd, stockAccount: item.stockAccount, trdacctExcls: item.trdacctExcls})
      }
    })
  },
  methods: {

    doCheck () {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        // 可以下一步
        resolve()
      })
    },
    /**
     * 请求参数打包
     */
    reqParamPackage () {
      // 业务请求参数封装
      let params = {
          selectAccountList: JSON.stringify(this.selectAccountList),
      }
      return params
    },
    putFormData () {
      let formData = {
          chooseAccount: this.selectAccountList,
      }
      return formData
    },
  },
}
</script>
<style scoped>
.account_list li .icon_radio:before{
    right:0;
}
</style>
