<!-- 结果页组件 -->
<template>
  <div>
    <h5 class="com_title">您的销户原因（可多选）</h5>
    <div class="cancel_reason">
        <ul
          v-for="(item,index) in reasonList"
          :key="index">
            <li>
                <span class="icon_radio" :class="{checked:item.isChecked}" @click.stop="changeCheck(item)">{{item.value}}</span>
            </li>
        </ul>
    </div>
    <div class="input_form" v-show="showReasonHtml">
        <div class="ui field text note">
            <label class="ui label">原因说明</label>
            <divEditable v-model="reasonHtmlText" placeholder="请输入其它原因"></divEditable>
        </div>
    </div>
  </div>
</template>

<script>
import { queryDictionary } from '@/common/util'
import divEditable from '@/components/divEditable' // div,contenteditable输入器
export default {
  name: 'businessResult',
  props: {
    pageParam: {
      type: Array
    }
  },
  components: {
    divEditable
  },
  data () {
    return {
      reasonList: [],
      showReasonHtml: false,
      reasonHtml: '',
      reasonHtmlText: ''

    }
  },
  mounted () {
    // 查询短信类型数据字典
    queryDictionary(
      { type: 'ismp.cancelAccountReason' },
      data => {
        let i = 0
        data.forEach(el => {
          i++
          el.isChecked = false
          this.reasonList.push(el)
          if (i == data.length) {
            this.reasonList.push({
              value: '其它原因', isChecked: false
            })
          }
        })
      }
    )

  },
  methods: {
    changeCheck (item) {
      item.isChecked = !item.isChecked
      if (item.value == '其它原因') {
        this.showReasonHtml = item.isChecked
      }
    },
    checkSubmit () {
      let reason = ''
      let isChecked = false
      for (let index = 0; index < this.reasonList.length; index++) {
        const element = this.reasonList[index]
        if (element.isChecked) {
          isChecked = true
          reason += ',' + element.value
        }
      }
      if (!isChecked) {
        _hvueAlert({ title: '温馨提示', mes: '请先勾选原因' })
        return false
      }
      if (this.showReasonHtml && this.reasonHtmlText == '') {
        _hvueAlert({ title: '温馨提示', mes: '请输入其它原因' })
        return false
      } else if (!this.showReasonHtml) {
        this.reasonHtmlText = ''
      }

      if(this.reasonHtmlText.length > 100){
        _hvueAlert({ title: '温馨提示', mes: '销户其他原因补充文字不允许超过100个。' })
        return false
      }

      reason += this.reasonHtmlText == '' ? '' : ',' + this.reasonHtmlText
      this.reasonHtml = reason.substr(1);
      return true
    },
    doCheck () {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        if (this.checkSubmit()) {
          // 可以下一步
          resolve()
        }
      })
    },
    /**
     * 请求参数打包
     */
    reqParamPackage () {
      // 业务请求参数封装
      let params = {
        reason: this.reasonHtml
      }
      return params
    },
    putFormData () {
      let formData = {
      }
      return formData
    }
  },
  activated () {

  },
  computed: {
  }
}
</script>
