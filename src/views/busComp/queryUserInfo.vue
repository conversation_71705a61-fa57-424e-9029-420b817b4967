/*
* @Author: li<PERSON><PERSON>
* @Date: 2020-05-12 11:22:18
 * @Last Modified by: Way
 * @Last Modified time: 2025-04-29 11:12:43
*/
<template>
    <div>
        <div v-show="showUserInfo">
            <headComponent headerTitle="个人资料修改"></headComponent>
            <div class="ste_cont">
                <ul>
                    <li class="spel">
                        <span class="tit">姓名</span>
                        <div class="cont">
                            <span>{{ ygtUserInfo.name }}</span>
                        </div>
                    </li>
                    <li class="spel">
                        <span class="tit">身份证号</span>
                        <div class="cont">
                            <span>{{
                                    ygtUserInfo.identityNum | formatIdno
                                }}</span>
                        </div>
                    </li>
                    <li class="spel">
                        <span class="tit">有效期</span>
                        <div class="cont">
                            <span
                            >{{
                                    ygtUserInfo.validityBegin
                                        | formatDate('yyyy.MM.dd')
                                }}-{{
                                    ygtUserInfo.validityEnd
                                        | formatDate('yyyy.MM.dd')
                                }}</span
                            >
                        </div>
                    </li>
                    <li class="spel">
                        <span class="tit">证件地址</span>
                        <div class="cont">
                            <span>{{ ygtUserInfo.papersAddr }}</span>
                        </div>
                    </li>
                </ul>
            </div>
            <div class="ste_cont">
                <ul>
                    <li @click.stop="boxClick('mobile')">
                        <span class="tit">预留手机号</span>
                        <div class="cont">
                            {{ selData.mobile.value | formatMobileNo }}
                        </div>
                        <span class="lk"></span>
                    </li>
                    <li>
                        <span class="tit">固定电话</span>
                        <div class="cont" @click.stop="boxClick('telePhone')">
                            <span v-if="!selData.telePhone.value" class="default">地区码-电话号码</span>
                            <span v-else>{{ selData.telePhone.value }}</span>
                        </div>
                        <span class="lk"></span>
                    </li>
                    <li @click.stop="toRisk">
                        <span class="tit">风险测评</span>
                        <div class="cont">
                            <span>{{
                                    ygtUserInfo.riskLevelDesc
                                        | fillRiskLevel(ygtUserInfo.isEffective)
                                }}</span>
                        </div>
                        <span class="lk"></span>
                    </li>
                </ul>
            </div>
            <div class="ste_cont">
                <ul>
                    <li>
                        <span class="tit">学历</span>
                        <div
                            class="cont"
                            @click.stop="boxClick('education', '学历')"
                        >
                            <span :class="{ default: selData.education.value === '请选择' }">{{
                                    selData.education.value
                                }}</span>
                        </div>
                        <span class="lk"></span>
                    </li>
                    <li>
                        <span class="tit">职业</span>
                        <div
                            class="cont"
                            @click.stop="boxClick('occupation', '职业')"
                        >
                            <span v-if="selData.otherOccu.value != '' && selData.occupation.id == '99'"
                                  :class="{ default: selData.occupation.value === '请选择' }">{{
                                    selData.occupation.value + '-' + selData.otherOccu.value
                                }}</span>
                            <span v-else :class="{ default: selData.occupation.value === '请选择' }">{{
                                    selData.occupation.value
                                }}</span>
                        </div>
                        <span class="lk"></span>
                    </li>
                    <li>
                        <span class="tit">工作单位</span>
                        <div class="cont" @click.stop="boxClick('companyName')">
                            <span>{{ selData.companyName.value }}</span>
                        </div>
                        <span class="lk"></span>
                    </li>
                    <li>
                        <span class="tit">邮编</span>
                        <div class="cont" @click.stop="boxClick('postcode')">
                            <span>{{ selData.postcode.value }}</span>
                        </div>
                        <span class="lk"></span>
                    </li>
                    <li>
                        <span class="tit">年收入(元)</span>
                        <!--                        <div class="cont" @click.stop="boxClick('income')">-->
                        <!--                            <span v-if="parseFloat(selData.income.value) == ''" class="default">请填写</span>-->
                        <!--                            <span v-else>{{ selData.income.value | formatMoney }}</span>-->
                        <!--                        </div>-->
                        <div
                            class="cont"
                            @click.stop="boxClick('income', '年收入')"
                        >
                            <span :class="{ default: selData.income.value === '请选择' }">
                                {{ selData.income.value }}
                            </span>
<!--                            <span v-else :class="{ default: selData.income.value === '请选择' }">-->
<!--                                {{'50万元以上' }}-->
<!--                            </span>-->
                        </div>
                        <span class="lk"></span>
                    </li>
                    <li v-if="+selData.income.id === 1">
                        <span class="tit">年收入具体金额</span>
                        <div class="cont" @click.stop="boxClick('incomeExt')">
                            <span v-if="selData.incomeExt.value === '请输入具体金额'" class="default">
                                （元）
                            </span>
                            <span v-else>
                                {{ selData.incomeExt.value | formatMoney }}（元）
                            </span>
                        </div>
                        <span class="lk"></span>
                    </li>
                    <li>
                        <span class="tit">特定自然人标识</span>
                        <div
                            class="cont"
                            @click.stop="boxClick('identityCategory', '特定自然人标识')"
                        >
                            <span v-if="selData.identityCategory.value === '请选择'" class="default"></span>
                            <span v-else>
                                {{selData.identityCategory.id === '1' ? '否' : '是' }}
                                ({{ selData.identityCategory.value }})
                            </span>
                        </div>
                        <span class="lk"></span>
                    </li>
                    <li>
                        <span class="tit">电子邮箱</span>
                        <div class="cont" @click.stop="boxClick('email')">
                            <span>{{ selData.email.value }}</span>
                        </div>
                        <span class="lk"></span>
                    </li>
                    <li>
                        <span class="tit">紧急联系人</span>
                        <div class="cont" @click.stop="boxClick('secRelation')">
                            <span :class="{ default: secRelationShowValue === '请填写' }"
                            >{{ secRelationShowValue }}</span
                            >
                        </div>
                        <span class="lk"></span>
                    </li>
                    <li>
                        <span class="tit">现居地址</span>
                        <div class="cont" @click.stop="boxClick('address')">
                            <span :class="{ default: selData.address.value === '请填写' }">{{
                                    selData.address.value
                                }}</span>
                        </div>
                        <span class="lk"></span>
                    </li>
                </ul>
            </div>
            <div class="ste_cont">
                <ul>
                    <li>
                        <span class="tit">账户实际控制人</span>
                        <div
                            class="cont"
                            @click.stop="boxClick('controlPerson')"
                        >
                            <span
                                v-if="selData.controlPerson.value === ''"
                                class="default"
                            >请完善</span
                            >
                            <span v-else
                            >{{
                                    selData.controlPerson.value ==
                                    ygtUserInfo.name
                                        ? '本人'
                                        : '非本人'
                                }}({{ selData.controlPerson.value }})</span
                            >
                        </div>
                        <span class="lk"></span>
                    </li>
                    <li>
                        <span class="tit">账户实际受益人</span>
                        <div
                            class="cont"
                            @click.stop="boxClick('benefitPerson')"
                        >
                            <span
                                v-if="selData.benefitPerson.value === ''"
                                class="default"
                            >请完善</span
                            >
                            <span v-else
                            >{{
                                    selData.benefitPerson.value ==
                                    ygtUserInfo.name
                                        ? '本人'
                                        : '非本人'
                                }}({{ selData.benefitPerson.value }})</span
                            >
                        </div>
                        <span class="lk"></span>
                    </li>
                </ul>
            </div>
            <div class="ste_cont">
                <ul>
                    <li>
                        <span class="tit">不良诚信记录</span>
                        <div
                            class="cont"
                            @click.stop="boxClick('creditRecord')"
                        >
                            <span
                                :class="{ default: selData.creditRecord.value === '请选择' }"
                            >{{ selData.creditRecord.value }}</span
                            >
                        </div>
                        <span class="lk"></span>
                    </li>
                    <!-- <li>
              <span class="tit">涉税信息</span>
              <div class="cont" @click.stop="boxClick('taxStatus')">
                <span :class="{default: selData.taxStatus.value=='请选择'}">{{selData.taxStatus.value}}</span>
              </div>
              <span class="lk"></span>
          </li>-->
                </ul>
            </div>
        </div>

        <inputBox
            v-if="inputBox.show"
            v-model="inputBox.show"
            :title="inputBox.title"
            :checkType="inputBox.checkType"
            :maxLength="inputBox.maxLength"
            :value="inputBox.value"
            :placeholder="inputBox.placeholder"
            @selCallback="selCallback"
        ></inputBox>
        <selBox
            v-if="selBox.show"
            v-model="selBox.show"
            :title="selBox.title"
            :defaultStr="selBox.defaultStr"
            :category="selBox.category"
            :initData="selBox.initData"
            @selCallback="selCallback"
        ></selBox>
        <provincesPicker
            v-if="pickerShow"
            v-model="pickerShow"
            :address="selData.address.value"
            :province="selData.address.province"
            :city="selData.address.city"
            :area="selData.address.area"
            :addrProvince="selData.address.addrProvince"
            :addrCity="selData.address.addrCity"
            :addrArea="selData.address.addrArea"
            :detailed="selData.address.detailed"
            @selCallback="selCallback"
        ></provincesPicker>
        <secRelation
            v-if="secRelationShow"
            v-model="secRelationShow"
            :name="selData.secRelation.name"
            :relation="selData.secRelation.value"
            :mobile="selData.secRelation.mobile"
            @secRelationCallback="secRelationCallback"
        ></secRelation>
        <selBox v-model="controlPersonBoxShow" title="实际控制人">
            <div class="wtclass_main">
                <ul class="wtclass_list">
                    <li>
                        <span>账户实际控制人是本人</span>
                        <div
                            class="ui switch"
                            @click.stop="controlPersonCall('controlPerson')"
                            :class="{
                                checked: selData.controlPerson.selected === 0
                            }"
                        >
                            <div class="ui switch-inner">
                                <div class="ui switch-arrow"></div>
                            </div>
                        </div>
                    </li>
                    <li>
                        <span>账户实际受益人是本人</span>
                        <div
                            class="ui switch"
                            @click.stop="controlPersonCall('benefitPerson')"
                            :class="{
                                checked: selData.benefitPerson.selected === 0
                            }"
                        >
                            <div class="ui switch-inner">
                                <div class="ui switch-arrow"></div>
                            </div>
                        </div>
                    </li>
                </ul>
            </div>
            <p class="bot_tips">
                根据监管要求，账户实际控制人和实际受益人必须为客户本人，无法线上修改。
            </p>
            <div class="ce_btn mt20">
                <a v-throttle class="ui button rounded block" @click.stop="selCallback"
                >确定</a
                >
            </div>
        </selBox>
        <selCreditRecord
            v-if="showCreditRecordBox"
            v-model="showCreditRecordBox"
            @selCallback="selCallback"
            :title="selBox.title"
            :defaultStr="selBox.defaultStr"
            :category="selBox.category"
            :initData="selBox.initData"
        ></selCreditRecord>
        <selBox v-model="showTaxBox" title="选择税收居民身份">
            <div class="tax_checklist">
                <ul>
                    <li
                        v-for="(item, index) in taxTypeData"
                        :class="{ checked: index === 0 }"
                        v-throttle
                        @click.stop="selTaxClick(index)"
                        :key="index"
                    >
                        <h5>{{ item.type }}</h5>
                        <p>{{ item.info }}</p>
                    </li>
                </ul>
            </div>
            <div class="rule_check">
                <span
                    class="icon_check"
                    :class="{ checked: selTaxChecked }"
                    @click.stop="selTaxChecked = !selTaxChecked"
                ></span>
                <label
                >本人确认_上述信息真实、准确和完整，且在这些信息发生变更时，将在30日内通知贵机构，否则，本人讲承担由此造成的不利后果。</label
                >
            </div>
            <div class="ce_btn mt20">
                <a v-throttle class="ui button rounded block" @click.stop="selCallback">确定</a>
            </div>
            <div class="tax_infotips">
                <p>说明:</p>
                <p>
                    中国税收居民是指在中国境内有住所，或者无住所而在境内居住满一年的个人，不包括香港、澳门、台湾地区的税收居民。本表所称非居民是指中国税收居民以外的个人。《非居民金融账户涉税信息尽职调查管理办法》相关信息详见国家税务总局网站(
                    <a
                        href="http://www.chinatax.gov.cn/n810341/n810755/c2623078/content.html"
                    >http://www.chinatax.gov.cn/n810341/n810755/c2623078/content.html</a
                    >)
                </p>
            </div>
        </selBox>
        <mobile-modify
            v-if="mobileModifyShow"
            v-model="mobileModifyShow"
            @selCallback="selCallback"
        ></mobile-modify>
    </div>
</template>

<script>
import { closeYgt } from '@/common/sso'
import headComponent from '@/components/headComponent'; // 头部
import inputBox from '@/components/inputBox'; // 输入器
import mobileModify from '@/components/mobileModify'; // 手机号码
import provincesPicker from '@/components/provincesPicker'; // 家庭地址
import secRelation from '@/components/secRelation'; // 第二联系人
import selBox from '@/components/selBox'; // 选择器
import selCreditRecord from '@/components/selCreditRecord'; // 不良诚信记录

import { queryDictionary } from '@/common/util'

export default {
  props: {
    pageParam: {
      type: Array
    }
  },
  components: {
    headComponent,
    selBox,
    inputBox,
    provincesPicker,
    secRelation,
    selCreditRecord,
    mobileModify
  },
  data () {
    return {
      userInfoModify: {}, // 提交表单的参数
      ygtUserInfo: $h.getSession('ygtUserInfo', {decrypt: false}),
      writeIndex: '',
      selData: {
        education: {
          name: '学历',
          value: '请选择',
          type: 'ismp.adapter',
          selected: 0,
          id: ''
        },
        occupation: {
          name: '职业',
          value: '请选择',
          type: 'ismp.occupational',
          selected: 0,
          id: ''
        },
        identityCategory: {
          name: '特定自然人',
          value: '请选择',
          type: 'ismp.identitycategory',
          selected: 0,
          id: ''
        },
        otherOccu: {
          title: '职业',
          placeholder: '请输入10位数以内的职业',
          value: '', // 手输职业,职业选择为其他时需拼接手输职业
          checkType: 'cnEnNum',
          maxLength: 10
        },
        postcode: {
          title: '邮编',
          placeholder: '请输入6位数邮编',
          value: '',
          checkType: 'postcode',
          maxLength: 6
        },
        companyName: {
          title: '工作单位',
          placeholder: '请输入工作单位名称',
          value: '',
          checkType: 'cnEnNum',
          maxLength: 30
        },
        incomeExt: {
          title: '年收入',
          placeholder: '请输入具体金额',
          value: '请输入具体金额',
          checkType: 'income',
          maxLength: 12
        },
        income: {
          name: '年收入',
          value: '请选择',
          type: 'ismp.income',
          selected: 0,
          id: ''
          // title: '年收入',
          // placeholder: '请输入年收入，单位元',
          // value: '',
          // checkType: 'income',
          // maxLength: 12,
        },
        email: {
          title: '电子邮箱',
          placeholder: '请输入电子邮箱',
          value: '',
          checkType: 'email',
          maxLength: 30
        },
        address: {
          name: '联系地址',
          value: '请填写',
          type: 'address',
          selected: 0,
          id: '',
          province: '',
          city: '',
          area: '',
          detailed: '',
          addrProvince: '',
          addrCity: '',
          addrArea: ''
        },
        secRelation: {
          title: '紧急联系人关系',
          name: '',
          mobile: '',
          value: '',
          type: 'ismp.relation',
          selected: 0,
          id: ''
        },
        controlPerson: {
          name: '账户实际控制人',
          value: '',
          type: 'controlPerson',
          selected: 0,
          id: ''
        },
        benefitPerson: {
          name: '账户实际受益人',
          value: '',
          type: 'benefitPerson',
          selected: 0,
          id: ''
        },
        creditRecord: {
          name: '诚信记录',
          value: '请选择',
          type: 'ismp.credit',
          selected: 0,
          id: ''
        },
        taxStatus: {
          name: '选择税收居民身份',
          value: '请选择',
          type: 'taxStatus',
          selected: 0,
          id: ''
        },
        telePhone: {
          title: '固定电话',
          placeholder: '地区码-电话号码',
          value: '',
          checkType: 'telePhone',
          maxLength: 13
        },
        mobile: {
          title: '修改手机号',
          value: '',
          type: 'mobile'
        }
      },
      selBox: {
        show: false,
        title: '',
        category: '',
        defaultStr: '',
        initData: []
      },
      inputBox: {
        show: false,
        title: '',
        placeholder: '',
        value: '',
        checkType: '',
        maxLength: ''
      },
      secRelationShow: false,
      pickerShow: false,
      controlPersonBoxShow: false,
      taxTypeData: [
        {
          type: '中国税收居民',
          info: '中国境内有住所，或者无住所而在境内居住满一年的个人'
        },
        {type: '非居民', info: '中国税收居民以外的个人'},
        {
          type: '既是中国税收居民又是其他税收管辖区居民',
          info: '中国境内有住所，同时在国外境内也有住所的个人'
        }
      ],
      showControlPersonBox: false,
      showCreditRecordBox: false,
      showTaxBox: false,
      selTaxChecked: false,
      mobileModifyShow: false
    }
  },
  computed: {
    showUserInfo () {
      if (!this.selBox.show && !this.inputBox.show && !this.pickerShow && !this.secRelationShow && !this.showCreditRecordBox && !this.showTaxBox && !this.controlPersonBoxShow && !this.mobileModifyShow) {
        this.$store.commit('updateBusinessNextBtnStatus', true) // 显示下一步按钮
        return true
      } else {
        return false
      }
    },
    secRelationShowValue () {
      let html = ''
      html += this.selData.secRelation.name + ' ' + this.selData.secRelation.value + ' ' + this.selData.secRelation.mobile
      if (html.trim() === '') {
        return '请填写'
      }
      return this.selData.secRelation.name + ' ' + this.selData.secRelation.value + ' ' + this.selData.secRelation.mobile
    }
  },
  activated () {
    // 隐藏头部
    this.$store.commit('updateIsShowHead', false)
    // 更改下一步按钮的文字
    this.$store.commit('updateNextBtnText', '提交保存')
    let synchronizedUserInfo = JSON.parse(
      this.pageParam[0].synchronizedUserInfo
    )
    let custTypeCheck = JSON.parse(this.pageParam[0].custTypeCheck)
    Object.assign(this.ygtUserInfo, synchronizedUserInfo[0])
    Object.assign(this.ygtUserInfo, custTypeCheck[0])
    this.ygtUserInfo.riskLevelDesc = custTypeCheck[0].riskLevelDesc
    $h.setSession('ygtUserInfo', this.ygtUserInfo, {encrypt: false})
    this.reloadData() // 填充页面数据
  },
  methods: {
    reloadData () {
      let _selData = this.selData
      let _this = this
      if (_this.ygtUserInfo.education) {
        queryDictionary(
          {type: 'ismp.adapter', key: _this.ygtUserInfo.education},
          function (d) {
            Object.assign(_selData['education'], {
              value: d.value,
              id: d.key,
              selected: d.index
            })
          }
        )
      }
      if (_this.ygtUserInfo.occupation) {
        queryDictionary(
          {
            type: 'ismp.occupational',
            key: _this.ygtUserInfo.occupation
          },
          function (d) {
            Object.assign(_selData['occupation'], {
              value: d.value,
              id: d.key,
              selected: d.index
            })
          }
        )
      }
      if (_this.ygtUserInfo.secRelationType) {
        queryDictionary(
          {
            type: 'ismp.relation',
            key: _this.ygtUserInfo.secRelationType
          },
          function (d) {
            Object.assign(_selData['secRelation'], {
              value: d.value,
              id: d.key,
              selected: d.index
            })
          }
        )
      }
      if (_this.ygtUserInfo.creditRecord) {
        let creditRecords = []
        let values = _this.ygtUserInfo.creditRecord.split(',')
        queryDictionary({type: 'ismp.credit'}, function (d) {
          for (let i = 0; i < values.length; i++) {
            for (let n = 0; n < d.length; n++) {
              let c = d[n]
              if (values[i] === c.value) {
                creditRecords.push(c)
                break
              }
            }
          }
          let val = []
          let key = []
          let index = []
          for (let s = 0; s < creditRecords.length; s++) {
            let el = creditRecords[s]
            val.push(el.value)
            key.push(el.key)
            index.push(el.index)
          }
          Object.assign(_selData['creditRecord'], {
            value: val.join(','),
            id: key.join(','),
            selected: index.join(',')
          })
        })
      }
      if (_this.ygtUserInfo.secRelationName) {
        Object.assign(_selData['secRelation'], {
          name: _this.ygtUserInfo.secRelationName
        })
      }
      if (_this.ygtUserInfo.secRelationPhone) {
        Object.assign(_selData['secRelation'], {
          mobile: _this.ygtUserInfo.secRelationPhone
        })
      }
      if (_this.ygtUserInfo.postcode) {
        Object.assign(_selData['postcode'], {
          value: _this.ygtUserInfo.postcode
        })
      }
      if (_this.ygtUserInfo.companyName) {
        Object.assign(_selData['companyName'], {
          value: _this.ygtUserInfo.companyName
        })
      }
      if (_this.ygtUserInfo.email) {
        Object.assign(_selData['email'], {
          value: _this.ygtUserInfo.email
        })
      }
      if (_this.ygtUserInfo.address) {
        Object.assign(_selData['address'], {
          value: _this.ygtUserInfo.address,
          detailed: _this.ygtUserInfo.address,
          addrProvince: _this.ygtUserInfo.addrProvince,
          addrCity: _this.ygtUserInfo.addrCity,
          addrArea: _this.ygtUserInfo.addrArea
        })
      }
      if (_this.ygtUserInfo.controlPerson === _this.ygtUserInfo.name) {
        Object.assign(_selData['controlPerson'], {
          value: _this.ygtUserInfo.controlPerson
        })
      }
      if (_this.ygtUserInfo.benefitPerson === _this.ygtUserInfo.name) {
        Object.assign(_selData['benefitPerson'], {
          value: _this.ygtUserInfo.benefitPerson
        })
      }
      // 手机号
      if (_this.ygtUserInfo.mobile) {
        Object.assign(_selData['mobile'], {
          value: _this.ygtUserInfo.mobile
        })
      }
      // 固定电话
      if (_this.ygtUserInfo.telePhone) {
        Object.assign(_selData['telePhone'], {
          value: _this.ygtUserInfo.telePhone
        })
      }
      // 年收入
      if (_this.ygtUserInfo.income) {
        queryDictionary(
          {
            type: 'ismp.income',
            key: _this.ygtUserInfo.income
          },
          function (d) {
            Object.assign(_selData['income'], {
              value: d.value,
              id: d.key,
              selected: d.index
            })
          }
        )
        if (_this.ygtUserInfo.income === '1') {
          _selData['incomeExt'].value = _this.ygtUserInfo.incomeExt
        }
      }
      // 特定自然人
      if (_this.ygtUserInfo.identityCategory) {
        queryDictionary(
          {
            type: 'ismp.identitycategory',
            key: _this.ygtUserInfo.identityCategory
          },
          function (d) {
            Object.assign(_selData['identityCategory'], {
              value: d.value,
              id: d.key,
              selected: d.index
            })
          }
        )
      }
    },
    checkSubmit () {
      let _selData = this.selData
      if (!_selData.education.id) {
        _hvueToast({
          mes: '请选择学历'
        })
        return false
      }
      if (!_selData.occupation.id) {
        _hvueToast({
          mes: '请选择职业'
        })
        return false
      }
      // if (!_selData.postcode.value) {
      //   _hvueToast({
      //     mes: '请完善邮编'
      //   })
      //   return false
      // }
      if (!_selData.income.id || (_selData.income.id === '1' && _selData.incomeExt.value === '请输入具体金额')) {
        _hvueToast({
          mes: '请完善年收入'
        })
        return false
      }
      // if (!_selData.email.value) {
      //   _hvueToast({
      //     mes: '请完善电子邮箱'
      //   })
      //   return false
      // }
      if (!_selData.identityCategory.id) {
        _hvueToast({
          mes: '请完善特定自然人标识'
        })
        return false
      }
      if (
        !_selData.secRelation.name ||
                !_selData.secRelation.value ||
                !_selData.secRelation.mobile
      ) {
        _hvueToast({
          mes: '请完善紧急联系人信息'
        })
        return false
      }
      if (
        $h.isEmptyString(_selData.address.addrProvince) ||
                $h.isEmptyString(_selData.address.addrCity) ||
                $h.isEmptyString(_selData.address.addrArea)
      ) {
        _hvueToast({ mes: '请完善现居地址的格式' })
        return
      }
      if (_selData.address.value.length < 8) {
        _hvueToast({
          mes: '家庭住址长度不能少于8位'
        })
        return false
      }
      if (
        _selData.controlPerson.value !== this.ygtUserInfo.name ||
                _selData.benefitPerson.value !== this.ygtUserInfo.name
      ) {
        _hvueToast({
          mes: '根据监管要求，账户实际控制人和实际受益人必须为客户本人'
        })
        return false
      }
      if (!_selData.creditRecord.id) {
        _hvueToast({
          mes: '请完善不良诚信记录'
        })
        return false
      }
      let userinfo = this.ygtUserInfo
      let submitParam = {}
      if (_selData.mobile.value !== userinfo.mobile) {
        submitParam.mobile = _selData.mobile.value
      }
      if (_selData.telePhone.value !== userinfo.telePhone) {
        submitParam.telePhone = _selData.telePhone.value
      }
      if (_selData.education.id !== userinfo.education) {
        submitParam.education = _selData.education.id
      }
      if (_selData.occupation.id !== userinfo.occupation) {
        submitParam.occupation = _selData.occupation.id
      }
      if (_selData.otherOccu.value !== userinfo.otherOccu) {
        submitParam.otherOccu = _selData.otherOccu.value
      }
      if (_selData.postcode.value !== userinfo.postcode) {
        submitParam.postcode = _selData.postcode.value
      }
      if (_selData.companyName.value !== userinfo.companyName) {
        submitParam.companyName = _selData.companyName.value
      }

      if (_selData.income.id !== userinfo.income) {
        submitParam.income = _selData.income.id
      }
      if (_selData.incomeExt.value !== '请输入具体金额' && _selData.incomeExt.value !== userinfo.incomeExt) {
        submitParam.incomeExt = _selData.incomeExt.value
        submitParam.income = _selData.income.id
      }

      if (_selData.email.value !== userinfo.email) {
        submitParam.email = _selData.email.value
      }
      if (_selData.secRelation.name !== userinfo.secRelationName) {
        submitParam.secRelationName = _selData.secRelation.name
      }
      if (_selData.secRelation.id !== userinfo.secRelationType) {
        submitParam.secRelationType = _selData.secRelation.id
      }
      if (_selData.secRelation.mobile !== userinfo.secRelationPhone) {
        submitParam.secRelationPhone = _selData.secRelation.mobile
      }
      if (_selData.address.value !== userinfo.address ||
                _selData.address.addrProvince !== userinfo.addrProvince ||
                _selData.address.addrCity !== userinfo.addrCity ||
                _selData.address.addrArea !== userinfo.addrArea
      ) {
        submitParam.address = _selData.address.value
        submitParam.addrDetail = _selData.address.detailed
        submitParam.addrProvince = _selData.address.addrProvince
        submitParam.addrCity = _selData.address.addrCity
        submitParam.addrArea = _selData.address.addrArea
      }
      if (_selData.controlPerson.value !== userinfo.controlPerson) {
        submitParam.controlPerson = _selData.controlPerson.value
      }
      if (_selData.benefitPerson.value !== userinfo.benefitPerson) {
        submitParam.benefitPerson = _selData.benefitPerson.value
      }
      if (_selData.creditRecord.value !== userinfo.creditRecord) {
        submitParam.creditRecord = _selData.creditRecord.value
      }
      if (_selData.identityCategory.id !== userinfo.identityCategory) {
        submitParam.identityCategory = _selData.identityCategory.id
      }
      if (JSON.stringify(submitParam) === '{}') {
        _hvueToast({
          mes: '您未做资料修改，无需提交'
        })
        return false
      }
      submitParam.addrIdentical = '0'
      submitParam.clientAddressType = 'a'
      submitParam.idKind = '0'
      submitParam.idNo = userinfo.identityNum
      Object.assign(this.userInfoModify, submitParam, {})
      return true
    },
    doCheck () {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        if (this.checkSubmit()) {
          // 可以下一步
          resolve()
        }
      })
    },
    /**
         * 请求参数打包
         */
    reqParamPackage () {
      // 业务请求参数封装
      let params = this.userInfoModify
      return params
    },
    /**
         * 表单参数打包
         */
    putFormData () {
      let formData = {
        userInfoModify: this.userInfoModify
      }
      return formData
    },
    // 打开涉税信息组件
    selTaxClick (index) {
      this.$store.commit('updateBusinessNextBtnStatus', false) // 隐藏下一步按钮
      if (index !== 0) {
        _hvueAlert({
          title: '温馨提示',
          mes: '若您的税收居民身份为非中国税收居民，请您携带有效身份证件前往我司营业部网点办理涉税信息声明。',
          opts: [{txt: '我知道了'}]
        })
      }
    },
    // 打开选择器和输入组件
    boxClick (type, title) {
      this.$store.commit('updateBusinessNextBtnStatus', false) // 隐藏下一步按钮
      this.writeIndex = type
      if (type === 'education' || type === 'occupation' || type === 'income' || type === 'identityCategory') {
        if (this.selData[type].selectAble === false) {
          return
        }
        let _type = this.selData[type].type
        // 学历职业，打开选择器
        this.selBox.title = title
        this.selBox.category = _type
        this.selBox.show = true
        this.selBox.defaultStr = this.selData[type].id
      } else if (
        type === 'companyName' ||
        type === 'postcode' ||
                type === 'otherOccu' ||
                type === 'email' ||
                type === 'incomeExt' ||
                type === 'telePhone'
      ) {
        // 打开邮编，电子邮箱，年收入输入器
        this.inputBox.show = true
        this.inputBox.title = this.selData[type].title
        this.inputBox.placeholder = this.selData[type].placeholder
        this.inputBox.checkType = this.selData[type].checkType
        this.inputBox.value = this.selData[type].value
        this.inputBox.maxLength = this.selData[type].maxLength
        this.inputBox.tips = this.selData[type].tips
        if (type === 'incomeExt') {
          this.inputBox.value = this.inputBox.value === '请输入具体金额' ? '' : parseFloat(this.selData[type].value).toString()
          // this.inputBox.value = parseFloat(this.selData[type].value).toString()
        }
      } else if (type === 'secRelation') {
        // 紧急联系人
        this.secRelationShow = true
      } else if (type === 'address') {
        // 家庭地址
        this.pickerShow = true
      } else if (type === 'mobile') {
        this.mobileModifyShow = true
      } else if (type === 'controlPerson' || type === 'benefitPerson') {
        // 账户实际控制人、受益人
        this.controlPersonBoxShow = true
      } else if (type === 'creditRecord') {
        // 不良诚信记录
        let _type = this.selData[type].type
        // 打开选择器
        this.selBox.title = title
        this.selBox.category = _type
        this.selBox.defaultStr = this.selData[type].id
        this.showCreditRecordBox = true
      } else if (type === 'taxStatus') {
        // 涉税信息
        this.showTaxBox = true
      }
    },
    // 选择器和输入器组件回调方法
    selCallback (d) {
      this.$store.commit('updateBusinessNextBtnStatus', true) // 显示下一步按钮
      let type = this.writeIndex
      if (type === 'education' || type === 'occupation' || type === 'income' || type === 'identityCategory') {
        let value = d.data.value
        // 学历职业返回
        Object.assign(this.selData[this.writeIndex], {
          value: value,
          id: d.data.key,
          selected: d.index
        })

        // 职业选择其他时，打开职业输入器
        if (type === 'occupation' && d.data.key == '99') {
          this.boxClick('otherOccu')
        }
      } else if (
        type === 'companyName' ||
        type === 'postcode' ||
                type === 'otherOccu' ||
                type === 'email' ||
                type === 'incomeExt' ||
                type === 'telePhone'
      ) {
        // 返回邮编电子邮箱年收入，职业输入
        Object.assign(this.selData[this.writeIndex], {
          value: d.value
        })
      } else if (type === 'address') {
        // 家庭地址
        Object.assign(this.selData[this.writeIndex], {
          value: d.address,
          province: d.province,
          city: d.city,
          area: d.area,
          detailed: d.detailed,
          addrProvince: d.addrProvince,
          addrCity: d.addrCity,
          addrArea: d.addrArea
        })
      } else if (type === 'mobile') {
        // 手机号
        Object.assign(this.selData[this.writeIndex], {
          value: d.mobile
        })
      } else if (type === 'controlPerson' || type === 'benefitPerson') {
        // 账户实际控制人、受益人
        Object.assign(this.selData['controlPerson'], {
          value: this.ygtUserInfo.name
        })
        Object.assign(this.selData['benefitPerson'], {
          value: this.ygtUserInfo.name
        })
        this.controlPersonBoxShow = false
      } else if (type === 'creditRecord') {
        // 不良诚信记录
        this.showCreditRecordBox = false
        let val = []
        let key = []
        let index = []
        for (let i = 0; i < d.length; i++) {
          let el = d[i]
          val.push(el.value)
          key.push(el.key)
          index.push(el.index)
        }
        Object.assign(this.selData[this.writeIndex], {
          value: val.join(','),
          id: key.join(','),
          selected: index.join(',')
        })
      } else if (type === 'taxStatus') {
        // 涉税信息
        if (!this.selTaxChecked) {
          _hvueToast({mes: '请勾选本人确认'})
          return
        }
        Object.assign(this.selData[this.writeIndex], {
          value: '中国税收居民'
        })
        this.showTaxBox = false
      }
    },
    // 第二联系人组件回调方法
    secRelationCallback (d) {
      this.$store.commit('updateBusinessNextBtnStatus', true) // 显示下一步按钮
      Object.assign(this.selData[this.writeIndex], {
        name: d.name,
        mobile: d.mobile,
        value: d.relation,
        id: d.relationKey
      })
    },
    // 控制人受益人组件回调方法
    controlPersonCall (type) {
      if (type === 'controlPerson') {
        if (this.selData.controlPerson.selected === 0) {
          // 账户实际控制人、受益人
          _hvueAlert({
            title: '抱歉！无法修改',
            mes: '根据监管要求，账户实际控制人和实际受益人必须为客户本人，无法线上修改'
          })
          return
        } else {
          this.selData.controlPerson.selected = 0
        }
      }
      if (type === 'benefitPerson') {
        if (this.selData.benefitPerson.selected === 0) {
          // 账户实际控制人、受益人
          _hvueAlert({
            title: '抱歉！无法修改',
            mes: '根据监管要求，账户实际控制人和实际受益人必须为客户本人，无法线上修改'
          })
          return
        } else {
          this.selData.benefitPerson.selected = 0
        }
      }
      this.$store.commit('updateBusinessNextBtnStatus', true) // 显示下一步按钮
    },
    // 跳转到风险测评
    toRisk () {
      this.$store.commit('updateBusinessNextBtnStatus', true) // 显示下一步按钮
      this.$router.push({
        name: 'business',
        query: {type: 'fxcp', name: '风险测评'}
      })
    },
    toModifyMobile () {
      // this.$store.commit('updateBusinessNextBtnStatus', true) // 显示下一步按钮
      // this.$router.push({
      //     name: 'business',
      //     query: {type: 'xgsjh', name: '修改手机号'},
      // })
    },
    back () {
      // 如果是从交易跳转过来的，返回到交易
      let source = this.$route.query.source
      if (source && source === 'trade') {
        closeYgt(0, '1A', 0, 1, this.$router)
        return
      }
      this.$router.go(-1)
    }
  }
}
</script>
