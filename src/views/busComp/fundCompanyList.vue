<template>
  <div>
    <div class="tab_nav">
      <ul>
        <li :class="{ active: !showOpenAccount }">
          <a @click.stop="showOpenAccount = !showOpenAccount">
            <span>未开通</span>
          </a>
        </li>
        <li :class="{ active: showOpenAccount }">
          <a @click.stop="showOpenAccount = !showOpenAccount">
            <span>已开通</span>
          </a>
        </li>
      </ul>
    </div>

    <article v-show="!showOpenAccount && isShow" class="content">
      <div class="tab_content">
        <div class="fund_acctbox">
          <ul ref="fund_account_list">
            <template v-for="(it, index) in pageParam">
              <li v-if="it.openStatus === '0'" :key="index">
                <span
                  class="icon_radio"
                  @click.stop="accSelEvent(it)"
                  :class="{ checked: selectedAccounts.indexOf(it) != -1 }"
                  >{{ it.exchangeName }}</span
                >
              </li>
            </template>
          </ul>
        </div>
        <p class="no_moretips">
          <span>没有更多内容啦</span>
        </p>
      </div>
    </article>
    <article v-show="showOpenAccount" class="content">
      <div class="tab_content">
        <div class="fund_openedlist">
          <ul>
            <template v-for="(it, index) in pageParam">
              <li v-if="it.openStatus === '1'" :key="index">
                <p>{{ it.exchangeName }}</p>
                <em>{{ it.transAccount }}</em>
              </li>
            </template>
          </ul>
        </div>
        <p class="no_moretips">
          <span>没有更多内容啦</span>
        </p>
      </div>
    </article>
  </div>
</template>
<script>
export default {
  props: ['pageParam'],
  data() {
    return {
      chooseAccount: [], // 需要保存的表单数据
      selectedAccounts: [], // 已选择的账户
      showOpenAccount: false,
      allClickFlag: false,
      isShow: true, // 展示协议列表
    }
  },
  activated() {
    if (this.pageParam.length < 1) {
      _hvueAlert({ mes: '未查询到基金列表', title: '提示' })
    }
    this.$store.commit('updateBusinessNextBtnStatus', false) // 隐藏下一步按钮
    this.$store.commit('updateAgreementIsFixed', true) // 协议勾选固定在页面底部
    this.$store.commit('updateSelectedAccountsLength', 0) // 已勾选账户总数，存在vuex中
    this.selectedAccounts = []
    // 注册 通过vuebus 接受agreementContent按钮组件的 参数修改账户勾选状态
    this.$bus.on('changeAllFlag', (allFlag) => {
      this.allClickFlag = allFlag
      let a = this.pageParam
      for (let r = 0; r < a.length; r++) {
        let accounts = a[r]
        if (accounts.openStatus === '0') {
          if (allFlag) {
            this.accSelEvent(accounts, 'open')
          } else {
            this.accSelEvent(accounts, 'close')
          }
        }
      }
    })
    // 注册vuebus方法 接受agreementContent按钮组件，是否去展示协议详情
    this.$bus.on('showProtocolFlag', (isShow) => {
      this.isShow = !isShow
    })
  },
  watch: {
    showOpenAccount(val) {
      this.$bus.emit('showOpenAccountFlag', val) // 通过vuebus调用agreementContent组件的方法修改是否展示已开通账户
    },
  },
  methods: {
    accSelEvent(it, type) {
      if (type === 'close') {
        if (this.selectedAccounts.indexOf(it) != -1) {
          this.selectedAccounts.remove(it)
        }
      } else if (type === 'open') {
        if (this.selectedAccounts.indexOf(it) == -1) {
          this.selectedAccounts.push(it)
        }
      } else {
        if (this.selectedAccounts.indexOf(it) != -1) {
          this.selectedAccounts.remove(it)
          this.$store.commit('updateAllFundCheckClickFlag', false) // 是否全选，存在vuex中
        } else {
          this.selectedAccounts.push(it)
        }
      }
      // 是否全选，存在vuex中
      if (
        this.selectedAccounts.length ==
        this.$refs.fund_account_list.children.length
      ) {
        this.$store.commit('updateAllFundCheckClickFlag', true)
      }
      this.$store.commit(
        'updateSelectedAccountsLength',
        this.selectedAccounts.length
      ) // 以勾选账户总数，存在vuex中
    },
    checkSubmit() {
      if (this.selectedAccounts.length == 0) {
        _hvueToast({
          mes: '请选择账户',
        })
        return false
      }
      return true
    },
    doCheck() {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        if (this.checkSubmit()) {
          // 可以下一步
          resolve()
        }
      })
    },
    /**
     * 请求参数打包
     */
    reqParamPackage() {
      // 业务请求参数封装
      let accountList = this.selectedAccounts
      let accountType = ''
      let arr = []
      accountList.forEach((a) => {
        accountType += ',' + a.fundCompany
        arr.push({
          fundCompany: a.fundCompany,
          fundCompanyName: a.exchangeName,
        })
      })
      this.chooseAccount = arr
      let params = {
        fundCompany: accountType,
      } // 提交需要的参数
      return params
    },
    putFormData() {
      let formData = { chooseAccount: this.chooseAccount } // 需要保存的表单数据
      return formData
    },
  },
  destroyed() {
    this.$store.commit('updateAgreementIsFixed', false) // 协议勾选恢复默认：不固定在页面底部
    this.$store.commit('updateSelectedAccountsLength', 0) // 已勾选账户总数，存在vuex中
    this.$bus.off('changeAllFlag') // 事件销毁，防止多次触发
    this.$bus.off('showProtocolFlag') // 事件销毁，防止多次触发
  },
}
</script>
