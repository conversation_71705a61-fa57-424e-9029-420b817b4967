<template>
  <div>
    <div class="agree_main">
      <h5 class="title">请选择要办理的账户</h5>
      <ul class="account_list">
        <li
          :class="{
            disable: item.openStatus == '1' || item.holderStatus != '0',
          }"
          v-for="(item, index) in pageParam"
          :key="index"
        >
          <p>
            <span :class="accountStyle(item)" @click.stop="accSelEvent(item)"
              >{{
                item.market == '10' && item.trdacctExcls == '4'
                  ? '沪基金'
                  : item.market == '00' && item.trdacctExcls == '4'
                  ? '深基金'
                  : item.marketName
              }}
              {{ item.stockAccount }}</span
            >
          </p>
          <span
            v-if="
              (item.openStatus != '0' || item.holderStatus != '0') &&
              type !== 'jggdh'
            "
            class="status"
            >{{
              item.holderStatus | filAccountState(item.regFlag, item.openStatus, item.holderName)
            }}</span
          >
          <!-- 科创板成长层权限开通业务：科创板未开通提示 -->
          <div
            v-if="type === 'kcbczc' && item.holderName === '科创板未开通'"
            class="kcb_unopened_tip"
          >
            <p class="tip_text">该账户不存在科创板权限，开通后可办理。</p>
            <p class="tip_link" @click="goToKcbOpen">前往开通 >></p>
          </div>
        </li>
      </ul>
    </div>
    <div class="cond_tips" v-show="allOpened">
      <p>抱歉，您在我司没有可选的账户，暂时无法办理</p>
    </div>
    <div class="cond_tips" v-show="type == 'jggdh'">
      <p>注意：只展示可加挂的账户，其它无法加挂的账户不进行展示</p>
    </div>
  </div>
</template>
<script>
export default {
  props: ['pageParam'],
  data() {
    return {
      type: this.$route.query.type, // 业务类型
      hasOpenedAccount: false, // 有已经开通权限的账户
      openedList: [], // 已经开通的市场
      selectedIndexes: [],
      submitAccountList: [],
      allOpened: true, // 是否已全部开通（一般业务都在前置步骤拦截了，创业板需求不拦截，单独在选择账户步骤拦截）
      bName: {
        'kcbczc': '科创板成长层权限',
        'kcbkt': '科创板权限',
        'cybkt': '创业板权限',
        'gszqqxkt': '债券权限'
      }
    }
  },
  mounted() {
    this.$on('putFormData', () => {
      return this.putFormData()
    })
    this.$on('reqParamPackage', () => {
      return this.reqParamPackage()
    })
  },
  activated() {
    if (this.pageParam.length < 1) {
      _hvueAlert({ mes: '未查询到账号列表', title: '提示' })
    }
    $h.clearSession('isOpenCredit')
    this.allOpened = true
    this.selectedIndexes = []
    this.submitAccountList = []
    this.hasOpenedAccount = false
    this.openedList = [];
    this.pageParam.forEach((element) => {
      if (element && element.openStatus === '1') {
        // 有开通权限
        this.hasOpenedAccount = true
        this.openedList.push(element.market)
      } else {
        this.allOpened = false
      }
    })
    if (this.allOpened) {
      this.$store.commit('updateNextBtnText', '返回首页')
    }
  },
  methods: {
    accSelEvent(item) {
      if (this.type === 'kcbkt' || this.type === 'cybkt') {
        // 判断选择账户的顺序 科创板/创业板开通只能先选择普通股东账户
        if (item.trdacctExcls === '2') {
          // 信用账户，判断选中的列表中有没有选中普通股东账户
          let flag = false

          if (this.selectedIndexes.length) {
            for (let index = 0; index < this.selectedIndexes.length; index++) {
              const element = this.selectedIndexes[index]

              if (this.type !== 'gszqqxkt') {
                if (element.trdacctExcls === '0') {
                  flag = true
                  break
                }
              } else { // 债券权限必须同市场的普通勾选后，才能选择信用
                if (
                    (item.market === '14' && element.market === '10') ||
                    (item.market === '24' && element.market === '00')
                ) {
                  flag = true
                  break
                }
              }
            }
          } else {
            if (this.type === 'gszqqxkt') {
              if (
                  (item.market === '14' && this.openedList.includes('10')) ||
                  (item.market === '24' && this.openedList.includes('00'))
              ) {
                flag = true
              }
            }
          }
          if (
              (!flag && !this.hasOpenedAccount && this.type !== 'gszqqxkt') ||
              (!flag && this.type === 'gszqqxkt')
          ) {
            let bName = this.bName[this.type];
            _hvueToast({
              mes: '开通信用账户' + bName + '，需要您先开通普通账户的' + bName,
            })
            return
          }
        }
      } else if (this.type === 'kcbczc') {
        if (item.trdacctExcls === '2') {
          // 信用账户
          let regularMarket = ''
          if (item.market === '14') {
            regularMarket = '10'
          } else if (item.market === '24') {
            regularMarket = '00'
          }
          if (regularMarket) {
            const isRegularSelected = this.selectedIndexes.some(
              (el) => el.market === regularMarket && el.trdacctExcls === '0'
            )
            const isRegularOpened = this.pageParam.some(
              (el) =>
                el.market === regularMarket &&
                el.trdacctExcls === '0' &&
                el.openStatus === '1'
            )
            if (!isRegularSelected && !isRegularOpened) {
              _hvueToast({
                mes: '开通信用账户' + this.bName[this.type] + '，需要您先勾选或已开通普通账户的' + this.bName[this.type],
              })
              return
            }
          }
        }
      }
      if (this.selectedIndexes.indexOf(item) !== -1) {
        // 如果是点击取消普通股东账户的勾选（并且没有已开通的权限） 则也要将信用账户的选择取消
        // 如果是债券业务，取消普通户会自动取消当前市场下的信用账户
        if (item.trdacctExcls === '0' && !this.hasOpenedAccount) {
          for (let index = 0; index < this.selectedIndexes.length; index++) {
            const element = this.selectedIndexes[index]
            if (element.trdacctExcls === '2') {
              this.selectedIndexes.remove(element)
            }
          }
        }
        this.selectedIndexes.remove(item)
      } else {
        this.selectedIndexes.push(item)
      }
    },
    accountStyle(item) {
      let _class = ''
      if (item.openStatus === '1') {
        _class = ''
      } else if (item.openStatus === '0') {
        _class = 'icon_radio'
      } else {
        if (item.holderStatus !== '0') {
          _class = ''
        } else {
          if (item.regFlag !== '1' && item.regFlag !== '2') {
            _class = ''
          }
        }
      }
      // 加挂账户接口已做过滤，直接可以选择
      if (this.type === 'jggdh') {
        _class = 'icon_radio'
      }
      if (this.selectedIndexes.indexOf(item) !== -1) {
        _class += ' checked'
      }
      return _class
    },
    checkSubmit() {
      if (this.selectedIndexes.length === 0) {
        _hvueToast({
          mes: '请选择账户',
        })
        return false
      }

      if (this.type === 'kcbczc') {
        const selectedCreditAccounts = this.selectedIndexes.filter(
          (item) => item.trdacctExcls === '2'
        )

        for (const creditAcct of selectedCreditAccounts) {
          let regularMarket = ''
          if (creditAcct.market === '14') {
            regularMarket = '10'
          } else if (creditAcct.market === '24') {
            regularMarket = '00'
          }

          if (regularMarket) {
            const isRegularSelected = this.selectedIndexes.some(
              (item) => item.market === regularMarket && item.trdacctExcls === '0'
            )
            const isRegularOpened = this.pageParam.some(
              (item) =>
                item.market === regularMarket &&
                item.trdacctExcls === '0' &&
                item.openStatus === '1'
            )

            if (!isRegularSelected && !isRegularOpened) {
              _hvueToast({
                mes: '开通信用账户' + this.bName[this.type] + '，需要您先勾选或已开通普通账户的' + this.bName[this.type],
              })
              return false
            }
          }
        }
      }

      this.submitAccountList = []
      for (let i = 0; i < this.selectedIndexes.length; i++) {
        const el = this.selectedIndexes[i]
        let account = {
          market: el.market,
          stockAccount: el.stockAccount,
          trdacctExcls: el.trdacctExcls,
        }
        this.submitAccountList.push(account)
        if (el.trdacctExcls === '2') {
          // 已勾选信用账户
          $h.setSession('isOpenCredit', 1)
        }
      }
      return true
    },
    doCheck() {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        if (this.allOpened) {
          this.$router.push({ name: 'index' })
          return
        }
        if (this.checkSubmit() === true) {
          // 可以下一步
          resolve()
        }
      })
    },
    /**
     * 请求参数打包
     */
    reqParamPackage() {
      // 业务请求参数封装
      let params = {
        stockAccountInfo: JSON.stringify(this.submitAccountList),
      }
      return params
    },
    putFormData() {
      let formData = {
        chooseAccount: this.submitAccountList,
      }
      return formData
    },
    /**
     * 跳转到科创板权限开通业务
     */
    goToKcbOpen() {
      this.$router.push({
        name: 'business',
        query: {
          type: 'kcbkt'
        }
      })
    },
  },
}
</script>
<style scoped>
  .account_list li .status {
    z-index: 0;
  }

  /* 科创板未开通提示样式 */
  .kcb_unopened_tip {
    margin-top: 8px;
    padding: 10px;
    background-color: #fff7e6;
    border: 1px solid #ffd591;
    border-radius: 4px;
    font-size: 12px;
  }

  .kcb_unopened_tip .tip_text {
    color: #fa8c16;
    margin: 0 0 5px 0;
    line-height: 1.4;
  }

  .kcb_unopened_tip .tip_link {
    color: #1890ff;
    margin: 0;
    cursor: pointer;
    text-decoration: underline;
    line-height: 1.4;
  }

  .kcb_unopened_tip .tip_link:hover {
    color: #40a9ff;
  }
</style>
