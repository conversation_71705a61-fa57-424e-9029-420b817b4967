<!-- 三方存管-变更/添加银行卡-提交表单 -->
<template>
  <div>
    <div class="card_wbox" v-if="!bankListBoxShow && flowName =='cgyhbg'">
      <ul class="sele_card">
        <li>
          <div class="card_cont">
            <span class="card_spel">主卡</span>
            <dl class="card_info">
              <dt>
                <img :src="cardDetails.smallImg" />
              </dt>
              <dd>
                <h5>{{cardDetails.bankFullName}}</h5>
                <p>储蓄卡</p>
              </dd>
            </dl>
            <div class="card_number">{{cardDetails.bankAccount|formatBankCardNo}}</div>
          </div>
          <div class="card_shadow">
            <span>
              <strong>原有银行卡信息</strong>
            </span>
          </div>
        </li>
      </ul>
    </div>
    <div class="user_mian" v-if="!bankListBoxShow">
      <h5 class="com_title">请绑定持卡人本人的银行卡</h5>
      <div class="input_form">
        <div class="ui field text">
          <label class="ui label">持卡人</label>
          <input type="text" class="ui input" readonly="readonly" v-model="ygtUserInfo.name" />
        </div>
        <div class="ui field text">
          <label class="ui label">选择银行</label>
          <div class="ui dropdown bank_select" @click.stop="bankListBoxShow = true">
            <strong v-if="!cardDetailsNew.bankNo" class="default">请选择银行</strong>
            <strong v-else>
              <img :src="cardDetailsNew.smallImg" />
              {{cardDetailsNew.bankFullName}}
            </strong>
          </div>
        </div>
      </div>
      <div class="input_form mt10">
        <div class="ui field text" v-show="needCardNo">
          <label class="ui label">银行卡号</label>
          <input
            type="text"
            class="ui input"
            placeholder="请输入银行卡号"
            :maxlength="inputModel.card.maxlength"
            v-model="inputModel.card.value"
          />
<!--          <a-->
<!--            class="txt_close"-->
<!--            @click.stop="inputModel.card.value=''"-->
<!--            v-show="inputModel.card.value!=''"-->
<!--          ></a>-->
          <a v-if="!fromTHSGB" @click.stop="bankOcrClick" class="photo_btn"></a>
        </div>
        <div class="ui field text pword" v-show="needPassword">
          <label class="ui label">查询密码</label>
          <input
            class="ui input"
            placeholder="请输入查询密码"
            :type="pwdType"
            :maxlength="inputModel.pwd.maxlength"
            v-model="inputModel.pwd.value"
          />
          <a
            class="txt_close"
            @click.stop="inputModel.pwd.value=''"
            v-show="inputModel.pwd.value!=''"
          ></a>
          <a class="icon_eye" :class="{show: pwdShow}" @click.stop="pwdShow = !pwdShow"></a>
        </div>
      </div>
    </div>
    <div class="rule_check" v-if="!bankListBoxShow">
      <span
        class="icon_check"
        @click.stop="signProtocolCheck = !signProtocolCheck"
        :class="{ checked: signProtocolCheck }"
      ></span>
      <label>
        我已阅读并同意
        <template v-for="it in protocolList">
          <a @click.stop="toDetail(it.agreementId,it.agreeName)">《{{it.agreeName}}》</a>
        </template>
      </label>
    </div>

    <selBox v-model="bankListBoxShow" title="选择银行">
      <ul class="bank_list">
        <li v-for="(it,index) in bankList" v-throttle @click.stop="selClick(it)" :key="index">
          <img :src="it.smallImg" />
          {{it.bankFullName}}
        </li>
      </ul>
    </selBox>

<!--    <tpbankConfirm-->
<!--        @confirmCallBack="bankOcrCallBack"-->
<!--        ref="tpbankConfirm"-->
<!--        v-model="showTpbankConfirm"-->
<!--        ocrData="ocrData"-->
<!--        v-show="showTpbankConfirm"-->
<!--    ></tpbankConfirm>-->
    <getImgBoxBrowser
        v-if="platform == '0'"
        ref="getImgBoxBrowser"
        :userCapture="true"
        @getImgCallBack="getImgCallBack"
    ></getImgBoxBrowser>
  </div>
</template>

<script>
import selBox from '@/components/selBox' // 选择器
import { checkInput } from '@/common/util'
import { getProtocolByType, bankOcr } from '@/service/comServiceNew'
import getImgBoxBrowser from '@/components/getImg_browser'
// import tpbankConfirm from '@/views/tpbankConfirm';

export default {
  components: {
    selBox,
    getImgBoxBrowser
    // tpbankConfirm
  },
  props: ['pageParam'],
  data () {
    return {
      ygtUserInfo: $h.getSession('ygtUserInfo', {decrypt: false}),
      loginType: $h.getSession('loginType') || '1', // 账号类型 1普通账号 2信用账号
      flowName: this.$route.query.type,
      cardDetails: $h.getSession('cardDetails') || {}, // 旧银行卡信息
      cardDetailsNew: {}, // 新银行卡信息
      bankList: [], // 银行卡list
      bankListBoxShow: false, // 展示银行卡选择器
      pwdShow: false, // 是否明文展示
      needCardNo: true,
      needPassword: false,
      inputModel: {
        card: {
          minlength: '15',
          maxlength: '19',
          name: '银行卡号',
          value: '',
          format: 'bankCard'
        },
        pwd: {
          maxlength: '6',
          minlength: '6',
          name: '查询密码',
          value: '',
          format: 'num'
        }
      },
      signProtocolCheck: false,
      protocolList: {}, // 协议列表
      platform: '', // 平台
      fromTHSGB: $h.getSession('fromTHSGB'), // 渠道同花顺公版
    }
  },
  computed: {
    pwdType () {
      return this.pwdShow ? 'tel' : 'password'
    }
  },
  created () {
    if (this.pageParam.length < 1) {
      _hvueAlert({ mes: '未查询到可绑定银行列表', title: '提示' })
    } else {
      // 填充银行列表
      for (let j = 0; j < this.pageParam.length; j++) {
        let menu = this.pageParam[j]
        let flag = this.isValidUUID(menu.smallImg)
        if (flag) {
          menu.smallImg = $hvue.config.imgUrl + menu.smallImg
        } else {
          menu.smallImg =
        (process.env.NODE_ENV == 'development' ? '' : ROUTER_BASE) +
        '/static/images' +
        menu.smallImg
        }
      }
      this.bankList = this.pageParam
      this.platform = $hvue.platform
    }
  },
  activated () {},
  methods: {
    // 选择银行点击事件
    selClick (it) {
      // 新旧银行相同时，提示 用户
      if (it.bankNo == this.cardDetails.bankNo) {
        _hvueAlert({
          title: '暂不能修改同个存管银行',
          mes: '如果你要修改卡号 ，请前往该存管银行营业点或通银行APP修改卡号'
        })
        return
      }
      this.needCardNo = it.needCard != '0'
      this.needPassword = it.isPwd != '0'
      this.cardDetailsNew = it
      this.bankListBoxShow = false
      // 查询协议
      this.getProtocol(it.bankNo)
    },
    // 查询协议
    getProtocol (agreeType) {
      Object.assign(this.protocolList, {})
      getProtocolByType(
        {
          userId: this.ygtUserInfo.userId,
          queryType: '0',
          queryValue: agreeType
        },
        {}
      ).then(
        res => {
          if (res.error_no === '0') {
            let results = res.results || res.DataSet
            for (let i = 0; i < results.length; i++) {
              let el = results[i]
              el.isShow = true
              el.isRead = false
              this.$set(this.protocolList, i, el)
            }
          } else {
            _hvueToast({
              icon: 'error',
              mes: res.error_info
            })
          }
        },
        err => {
          console.log(err)
        }
      )
    },
    // 查看协议详情
    toDetail (agreementId, agreementName) {
      this.$router.push({
        name: 'agreementDetail',
        query: { agreementId: agreementId, agreementTitle: agreementName }
      })
    },
    checkSubmit () {
      if (!this.cardDetailsNew.bankNo) {
        _hvueToast({ mes: '您还未选择银行' })
        return false
      }
      if (this.needCardNo && !this.inputModel.card.value) {
        _hvueToast({ mes: '请输入银行卡号' })
        return false
      }
      if (
        this.needCardNo &&
        !/^[\d\s]{11,23}$/.test(this.inputModel.card.value)
      ) {
        _hvueToast({ mes: '请输入正确的银行卡号' })
        return false
      }
      if (this.needPassword) {
        let flag = checkInput(this.inputModel.pwd)
        if (flag != '') {
          return false
        }
      }
      if (!this.signProtocolCheck) {
        _hvueToast({ mes: '您还未勾选同意协议' })
        return false
      }
      return true
    },
    doCheck () {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        if (this.checkSubmit()) {
          // 可以下一步
          resolve()
        }
      })
    },
    /**
     * 请求参数打包
     */
    reqParamPackage () {
      let param = {} // 提交需要的参数

      let agreeIds = []
      let list2 = Object.values(this.protocolList)
      if (list2.length > 0) {
        for (let j = 0; j < list2.length; j++) {
          if (list2[j].isShow) {
            agreeIds.push(list2[j].agreementId)
          }
        }
      }

      if (this.flowName == 'cgyhbg') {
        param.oldBankNo = this.cardDetails.bankNo // 原银行编号
        param.oldMoneyType = this.cardDetails.moneyType // 原币种类型
        param.oldBankAccount = this.cardDetails.bankAccount // 原银行卡号
        param.opAccount = this.cardDetails.fundAccount // 原银行卡资金账户
      }
      param.agreementId = agreeIds.join(',') // 协议编号
      param.bankNo = this.cardDetailsNew.bankNo // 银行编号
      param.moneyType = this.cardDetailsNew.moneyType // 币种类型
      param.bankAccount = this.inputModel.card.value // 银行卡号
      param.bankPwd = this.inputModel.pwd.value // 银行卡密码
      return param
    },
    putFormData () {
      let bankCardInfo = {
        bankNo: this.cardDetailsNew.bankNo, // 银行编号
        bankAccount: this.inputModel.card.value // 银行卡号
      }
      if (this.flowName == 'cgyhbg') {
        bankCardInfo.oldBankNo = this.cardDetails.bankNo // 原银行编号
        bankCardInfo.oldBankAccount = this.cardDetails.bankAccount // 原银行卡号
      }
      let formData = {
        // bussinessType: this.loginType,
        signProtocol: Object.values(this.protocolList),
        bankCardInfo: bankCardInfo
      } // 需要保存的表单数据
      return formData
    },
    isValidUUID (uuid) {
      // UUID校验
      if (uuid == null) {
        return false
      }
      let regex = '^[0-9a-f]{8}[0-9a-f]{4}[0-9a-f]{4}[0-9a-f]{4}[0-9a-f]{12}$'
      if (uuid.match(regex)) {
        return true
      }
      return false
    },
    bankOcrClick () {
      if (this.platform !== '0') {
        window.bankOcrCallBack = this.bankOcrCallBack
        let result = $h.callMessageNative({
          funcNo: '60016'
        })
        if (result.error_no !== 0) {
          console.log(result.error_info)
        }
      } else {
        this.$refs.getImgBoxBrowser.getImg()
      }
    },
    getImgCallBack (data) {
      _hvueLoading.openLoading('识别中')
      bankOcr(
        {
          is_ocr: 1,
          file_type: 'image',
          file_name: `${this.ygtUserInfo.userId}.jpeg`,
          image_type: 'jpeg',
          file_data: encodeURIComponent(data.base64)
        },
        { loading: false }
      )
        .then(data => {
          if (data.error_no === '0') {
            let result = data.bankCardInfo[0]
            if (result && result.bankNo) {
              result.cardNumber = result.bankNo
              this.bankOcrCallBack(result)
            }
          } else {
            _hvueToast({ mes: data.error_info })
          }
        })
        .finally(() => {
          _hvueLoading.closeLoading()
        })
    },
    bankOcrCallBack (data) {
      if (typeof data === 'string') {
        data = JSON.parse(data)
      }
      this.inputModel.card.value = (data.cardNumber || data.number).replace(/ /g, '')

      for (let item of this.bankList) {
        if (data.bankName.indexOf(item.bankName) > -1) {
          this.selClick(item)
          break
        }
      }
    }
  },
  deactivated () {
    this.$store.commit('updateIsShowHead', true) // 隐藏头部
  }
}
</script>
