<template>
  <div>
    <div class="cond_box">
      <h5 class="title">请选择您想要办理的业务类型：</h5>
      <ul>
        <li
          v-for="(item,index) in showBusiList"
          :key="index"
        >
          <div class="tit">
            <p>{{item.name}}</p>
            <em>{{item.desc}}</em>
            <a class="a_link" @click.stop="toBusi(item)" >前往办理</a>
          </div>
        </li>
      </ul>
    </div>
  </div>
</template>
<script>
export default {
  props: ['pageParam'],
  data () {
    return {
      // 业务列表
      showBusiList: [{
        name: '证券账户增开',
        desc: '在沪深市场未开满的情况下，选择继续新开账户。',
        type: 'zkgdh',
        isBreakPoint: '0'
      }, {
        name: '证券账户加挂',
        desc: '将其他证券经营机构开通的证券账户加挂至我司进行交易。',
        type: 'jggdh',
        isBreakPoint: '0'
      }]
    }
  },
  activated () {
    this.$store.commit('updateBusinessNextBtnStatus', false) // 隐藏下一步按钮
  },
  methods: {
    toBusi (item) {
      this.$router.push({name: 'business', query: {type: item.type, name: item.name, isBreakPoint: item.isBreakPoint}})
    },
    doCheck () {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        resolve()
      })
    },
    /**
     * 请求参数打包
     */
    reqParamPackage () {
      // 业务请求参数封装
      let params = {}
      return params
    },
    putFormData () {
      let formData = {}
      return formData
    }
  }
}
</script>
<style>
.a_link {
  height: 0.24rem;
  line-height: 0.24rem;
  position: absolute;
  top: 0.11rem;
  right: 0.15rem;
  font-size: 0.13rem;
  color: #285fc1;
}
</style>
