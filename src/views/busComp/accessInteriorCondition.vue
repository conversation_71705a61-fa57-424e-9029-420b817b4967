<template>
  <div class="investor_condition">
    <article class="content">
      <div class="cond_box">
        <h5 class="title">{{businessName}}</h5>
        <ul class="condition_box">
          <li :key="index" v-for="(id, index) in checkItem" :class="{'ok': itemValue[index] === '1', 'error':['0', '2'].includes(itemValue[index])}" :id="id">
            <div class="tit">
              <p class="asset_info">
                <span>{{itemDesc.split('|')[index]}}</span>
                <a class="icon_help" @click.prevent="showTip(warmTips[id])" href="javascript:void(0);"></a>
              </p>
              <div class="tips_warming" v-if="itemValue[index] === '0' || itemValue[index] === '2'">
                {{reasonDesc.split('|')[index]}}
              </div>
            </div>
          </li>
        </ul>
      </div>
      <div class="cond_tips" v-if="nextFlag">
        <p class="error">您满足内部资产条件，可提交业务办理</p>
      </div>

      <div class="ce_btn">
        <a
            v-if="isFlag"
            ref="nextBtn"
            class="ui button block rounded"
            v-throttle
            @click.stop="nextStep"
        >提交外部证明材料</a>
        <a v-if="!nextFlag" v-throttle class="ui button block rounded border mt10" @click.stop="toIndex">放弃办理</a>
      </div>
    </article>
  </div>
</template>
<script>
export default {
  components: {
  },
  props: ['pageParam'],
  data () {
    return {
      assetBalance: '0', // 资产
      tradeTimeFlag: '0', // 经验,
      checkItem: [],
      itemDesc: '',
      reasonDesc: '',
      itemValue: [],
      warmTips: {
        assetBalance: '内部资产指您在我司开通的普通账户、信用账户及衍生品等账户的资产总和。',
        asset: '内部资产指您在我司开通的普通账户、信用账户及衍生品等账户的资产总和。',
        firstStatus: '交易经验以您在中登记录的首次交易日期开始计算'
      },
      nextFlag: false,
      businessCode: this.$route.query.type,
      businessName: '',
      isFlag: true
    };
  },
  activated () {
    if(this.businessCode === 'zytzzrd'){
      this.businessName = '办理此业务需要满足以下条件：'
    }else{
      this.businessName = '成为合格投资者需要满足以下内部条件：'
    }

    this.itemDesc = this.pageParam[0].itemDesc;
    this.reasonDesc = this.pageParam[0].reasonDesc;
    this.itemValue = this.pageParam[0].itemValue.split('|');
    this.checkItem = this.pageParam[0].checkItem.split('|');
    this.$store.commit('updateBusinessNextBtnStatus', false) // 隐藏下一步按钮
    this.isFlag = true;

    if (
        ['zytzzrd'].includes(this.businessCode) && this.itemValue.includes('2') && !this.itemValue.includes('0') ||
        !['zytzzrd'].includes(this.businessCode) && this.itemValue.includes('0')
    ) {
      this.nextFlag = false;
      this.$refs.nextBtn.innerText = '提交外部证明材料';
    }else if(['zytzzrd'].includes(this.businessCode) && this.itemValue.includes('0')){
      this.nextFlag = false;
      this.isFlag = false;
    }else {
      this.$refs.nextBtn.innerText = '下一步';
      this.nextFlag = true;
    }
  },
  methods: {
    toIndex () {
      this.$router.push({ name: 'index', params: {} })
    },
    // 调用父组件的下一步事件
    nextStep () {
      this.$parent.emitNextEvent()
    },
    doCheck () {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        // 可以下一步
        resolve();
      });
    },
    /**
     * 请求参数打包
     */
    reqParamPackage () {
      // 业务请求参数封装
      let params = {
        param1: '',
        param2: '',
      };
      return params;
    },
    putFormData () {
      let formData = {};
      return formData;
    },
    // 展示提示
    showTip (info) {
      _hvueAlert({ title: '温馨提示', mes: info })
    },
  },
};
</script>
<style scoped lang="css">
  .tips_warming{
    font-size:0.12rem;
    padding-left:0.3rem;
    color:#c62e30;
  }
</style>
