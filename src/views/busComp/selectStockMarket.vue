<!-- 增开股东户-勾选市场 -->
<template>
  <div>
    <div class="agree_main">
      <h5 class="title">请选择要开通的账户</h5>
      <ul class="account_list">
        <li v-for="(item,index) in pageParam" :key="index" v-show="item.status=='1'">
          <p>
            <!-- todo市场是否满3，满3不能勾选 -->
            <span
              class="icon_radio"
              @click.stop="accSelEvent(item.itemValue)"
              :class="{ checked: selectedIndexes.indexOf(item.itemValue)!=-1}"
            >{{item.itemName}}</span>
          </p>
        </li>
      </ul>
    </div>
  </div>
</template>
<script>
export default {
  props: ['pageParam'],
  data () {
    return {
      selectedIndexes: []
    }
  },
  activated () {
    this.selectedIndexes = []
  },
  methods: {
    accSelEvent (item) {
      if (this.selectedIndexes.indexOf(item) != -1) {
        this.selectedIndexes.remove(item)
      } else {
        this.selectedIndexes.push(item)
      }
    },
    checkSubmit () {
      if (this.selectedIndexes.length == 0) {
        _hvueToast({
          mes: '请选择开通市场'
        })
        return false
      }
      return true
    },
    doCheck () {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        if (this.checkSubmit()) {
          // 可以下一步
          resolve()
        }
      })
    },
    /**
     * 请求参数打包
     */
    reqParamPackage () {
      // 业务请求参数封装
      let params = {
        accountType: this.selectedIndexes.join(',')
      }
      return params
    },
    putFormData () {
      let arr = []
      for (let a = 0; a < this.selectedIndexes.length; a++) {
        let element = this.selectedIndexes[a]
        arr.push({ market: element })
      }
      let formData = {
        chooseAccount: arr
      }
      return formData
    }
  }
}
</script>
