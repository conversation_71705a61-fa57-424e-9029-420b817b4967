/*
* 两融额度调整组件
* @Author: Way
* @Date: 2020-09-18 09:44:45
 * @Last Modified by: Way
 * @Last Modified time: 2020-12-17 17:20:23
*/

<template>
    <div class="amount_main">
        <div class="max_amount">
            <ul>
                <li>
                    <span class="tit">我的额度</span>
                    <p class="spel">￥{{oldLimit}}万元</p>
                </li>
                <li>
                    <span class="tit">最大可调整额度</span>
                    <p>￥{{maxLimit}}万元</p>
                </li>
            </ul>
        </div>
        <!-- <p class="amount_tips"><span class="fl">净资产：{{netAsset}}</span><span class="fr">保证金比例：{{proportion}}%</span></p> -->
        <div class="amount_info">
            <h5 class="tit">拟调整授信额度</h5>
            <div class="amount_input">
                <i class="icon">￥</i>
                <input class="t1" type="text" placeholder="请输入整数" v-model="limit" :maxlength="inputLimit.maxlength">
                <span class="unit">万元</span>
            </div>
            <div v-show="showTips" class="tips"><span>超过最大额度</span>，请输入小于{{maxLimit}}的整数</div>
        </div>
    </div>
</template>

<script>
import {
  checkInput
} from '@/common/util'
export default {
  props: {
    pageParam: {
      type: Array
    }
  },
  data () {
    return {
      oldLimit: '', // 我的额度
      maxLimit: '', // 最大可调整额度
      netAsset: '', // 净资产
      proportion: '', // 保证金比例
      limit: '',
      inputLimit: {
        name: '拟调整授信额度',
        value: '',
        maxlength: '15',
        minlength: '1',
        format: 'num'
      },
      showTips: false,
      nextBtnDisabled: true
    }
  },
  activated () {
    this.$store.commit('updateNextBtnDisabled', this.nextBtnDisabled) // 下一步按钮置灰
    this.maxLimit = this.pageParam[0].maxCrdtAsset
    this.oldLimit = this.pageParam[0].totalMaxQuota
  },
  watch: {
    limit (val) {
      this.inputLimit.value = val
      if (parseInt(val) > parseInt(this.maxLimit)) {
        this.showTips = true
        this.nextBtnDisabled = true
      } else {
        this.showTips = false
        this.nextBtnDisabled = false
      }
    },
    nextBtnDisabled (val) {
      this.$store.commit('updateNextBtnDisabled', val) // 下一步按钮置灰
    }
  },
  methods: {
    checkSubmit () {
      if (this.nextBtnDisabled) {
        return false
      }
      this.inputLimit.value = this.limit
      let flag = checkInput(this.inputLimit)
      if (flag !== '') {
        return false
      }
      // 必须为10万元的倍数
      if (this.limit % 10 != 0) {
        _hvueAlert({
          mes: '规定只能设置成取整10万，请重新输入',
          title: '提示'
        })
        return false
      }
      return true
    },
    doCheck () {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        if (this.checkSubmit()) {
          // 可以下一步
          resolve()
        }
      })
    },
    /**
             * 请求参数打包
             */
    reqParamPackage () {
      // 业务请求参数封装
      let params = {
        totalApplyQuota: this.inputLimit.value
      }
      return params
    },
    putFormData () {
      let formData = {
        quotaApply: {
          clientId: $h.getSession('ygtUserInfo').clientId,
          fundAccount: this.$parent.flow.fundAccount,
          maxLimit: this.maxLimit,
          applyAmount: this.inputLimit.value
        }
      }
      return formData
    }
  },
  deactivated () {
    this.$store.commit('updateNextBtnDisabled', false) // 下一步按钮置灰
  }
}
</script>

<style>

</style>
