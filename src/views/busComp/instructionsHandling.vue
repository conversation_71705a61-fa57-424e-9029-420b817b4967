<template>
  <div v-if="flowName == 'kcbkt'">
    <div class="kcb_page">
      <div class="kcb_msutbox">
        <div class="title">
          <h5>开通条件</h5>
        </div>
        <ul class="list">
          <li><p>已开通沪A账户，且账户状态正常已指定交易</p></li>
          <li><p>20日日均资产大于等于50万元</p></li>
          <li><p>交易经验已满两年</p></li>
          <li><p>指定的沪A账户未开通科创板权限</p></li>
          <li><p>客户风险承受能力与业务风险等级匹配</p></li>
        </ul>
      </div>
      <div class="kcb_risktips">
        <p>
          风险揭示:
          投资者应当充分知悉和了解科创板股票交易风险事项、法律法规和交易所业务规则，结合自身风险认知和承受能力，审慎判断是否参与科创板。市场有风险，投资需谨慎。
        </p>
      </div>
    </div>
    <div class="kcb_cebtn">
      <a
        v-throttle
        class="ui button block"
        title="nextStep"
        @click.stop="nextStep($event)"
        >立即开通</a
      >
    </div>
  </div>
  <div v-else-if="flowName == 'xsbkt'">
    <div class="xsb_infopage">
      <div class="banner"><img src="@/assets/images/xsb_banner.png" /></div>
        <div v-html="instructionsHandling"></div>
<!--      <p class="base">-->
<!--        请您提前知悉以下股转合格投资者开通准备条件，若确认无误请点击开始办理按钮-->
<!--      </p>-->
<!--      <div class="xsb_detail">-->
<!--        <h5 class="title"><em>*</em>新三板权限开通办理条件说明</h5>-->
<!--        <dl>-->
<!--          <dt>一类交易权限</dt>-->
<!--          <dd>*可交易精选层、创新层、基础层股票</dd>-->
<!--          <dd>*日均资产200万元以上（前10个交易日）</dd>-->
<!--        </dl>-->
<!--        <dl>-->
<!--          <dt>二类交易权限</dt>-->
<!--          <dd>*可交易精选层、创新层股票</dd>-->
<!--          <dd>*日均资产150万元以上（前10个交易日）</dd>-->
<!--        </dl>-->
<!--        <dl>-->
<!--          <dt>三类交易权限</dt>-->
<!--          <dd>*可交易精选层股票</dd>-->
<!--          <dd>*日均资产100万元以上（前10个交易日）</dd>-->
<!--        </dl>-->
<!--      </div>-->
<!--      <p class="xsb_tips">-->
<!--        股转市场具有比较高的风险属性，参与交易前请您提前了解股转市场的风险及业务知识，认真评估是否可承受市场所带来的较高风险。-->
<!--      </p>-->
    </div>
  </div>
  <div v-else-if="flowName == 'lrhyzq'" v-html="instructionsHandling"></div>
  <div v-else-if="flowName == 'jcjjqxkt'">
    <div class="jcjjqxkt_page" v-html="instructionsHandling"></div>
  </div>
  <div v-else-if="flowName == 'tsgpkt'">
    <div class="tsgpky_page" v-html="instructionsHandling"></div>
  </div>
  <div v-else-if="flowName == 'jsjyqx'">
    <div v-html="instructionsHandling"></div>
  </div>
    <div v-else-if="flowName == 'bjstzz'">
        <div class="xsb_infopage">
            <div class="banner"><img src="@/assets/images/xsb_banner.png" /></div>
            <div v-html="instructionsHandling">
                <p class="base">
                    请您提前知悉以下权限开北交所投资者权限开通准备条件，若确认无误请点击开始办理按钮
                </p>
                <div class="xsb_detail">
                    <h5 class="title"><em>*</em>北交所投资者权限开通办理条件说明</h5>
                    <dl>
                        <dt>北交所投资者权限</dt>
                        <dd>*交易经验是否满两年</dd>
                        <dd>*前20个交易日日均资产50万以上</dd>
                        <dd>*无不良诚信记录</dd>
                        <dd>*不在关注名单内（包括沪、深交易所重点监控账户名单、中登实名制重点账户名单、反洗钱黑名单）</dd>
                        <dd>*客户风险承受能力不是最低等级</dd>
                        <dd>*无资产异常变化的情况</dd>
                    </dl>
                </div>
                <p class="xsb_tips">
                    北交所交易市场具有比较高的风险属性，参与交易前请您提前了解北交所交易市场的风险及业务知识，认真评估是否可承受市场所带来的较高风险。
                </p>
            </div>
        </div>
    </div>
  <div v-else-if="flowName == 'tzakt'">
    <div class="xsb_infopage">
      <div class="banner"><img src="@/assets/images/tza_banner.png" /></div>
      <div class="tza_info" v-html="instructionsHandling">
<!--        <p class="base">-->
<!--          请您提前知悉以下权限开北交所投资者权限开通准备条件，若确认无误请点击开始办理按钮-->
<!--        </p>-->
<!--        <div class="xsb_detail">-->
<!--          <h5 class="title"><em>*</em>北交所投资者权限开通办理条件说明</h5>-->
<!--          <dl>-->
<!--            <dt>北交所投资者权限</dt>-->
<!--            <dd>*交易经验是否满两年</dd>-->
<!--            <dd>*前20个交易日日均资产50万以上</dd>-->
<!--            <dd>*无不良诚信记录</dd>-->
<!--            <dd>*不在关注名单内（包括沪、深交易所重点监控账户名单、中登实名制重点账户名单、反洗钱黑名单）</dd>-->
<!--            <dd>*客户风险承受能力不是最低等级</dd>-->
<!--            <dd>*无资产异常变化的情况</dd>-->
<!--          </dl>-->
<!--        </div>-->
<!--        <p class="xsb_tips">-->
<!--          北交所交易市场具有比较高的风险属性，参与交易前请您提前了解北交所交易市场的风险及业务知识，认真评估是否可承受市场所带来的较高风险。-->
<!--        </p>-->
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: ['pageParam'],
  data() {
    return {
      flowName: this.$parent.flowName,
      instructionsHandling: '',
      // 协议最低阅读秒数
      readSeconds: 5,
      nextBtnDisabled: false,
    }
  },
  activated() {
    if (this.flowName == 'jsjyqx') {
      this.$store.commit('updateNextBtnText', '我已知晓上述业务风险(5s)') //
      this.$store.commit('updateNextBtnDisabled', true) // 下一步按钮置灰
      this.nextBtnDisabled = true
    } else {
      this.$store.commit('updateNextBtnText', '开始办理') // 修改下一步按钮
    }
    this.instructionsHandling = this.pageParam[0].instructionsHandling
    if (this.instructionsHandling.indexOf('button') > 0) {
      // 有自己的按钮
      this.$store.commit('updateBusinessNextBtnStatus', false) // 隐藏下一步按钮
    }
    let me = this
    me.readSeconds = 5
    //强制阅读
    if (me.readSeconds > 0) {
      me.interval = window.setInterval(() => {
        me.readSeconds--
        if (this.flowName == 'jsjyqx') {
          this.$store.commit('updateNextBtnDisabled', true) // 下一步按钮置灰
          this.nextBtnDisabled = true
          this.$store.commit(
            'updateNextBtnText',
            '我已知晓上述业务风险' + '(' + this.readSeconds + 's)'
          ) // 修改下一步按钮
        }
        if (me.readSeconds === 0) {
          window.clearInterval(me.interval)
          this.$store.commit('updateNextBtnText', '开始办理') // 修改下一步按钮
          this.$store.commit('updateNextBtnDisabled', false) // 下一步按钮置灰
          this.nextBtnDisabled = false
        }
      }, 1000)
    }
    //强制阅读
  },
  methods: {
    doCheck() {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        // 可以下一步
        if (!this.nextBtnDisabled) {
          resolve()
        }
      })
    },
    /**
     * 请求参数打包
     */
    reqParamPackage() {
      let params = {} // 提交需要的参数
      return params
    },
    putFormData() {
      let formData = {} // 需要保存的表单数据
      return formData
    },
    // 调用父组件的下一步事件
    nextStep(event) {
      if (event.target.title == 'nextStep') {
        // 如果点击到了
        // 拿着id参数执行着相关的操作
        this.$parent.emitNextEvent()
      }
    },
  },
  deactivated() {
    this.$store.commit('updateNextBtnDisabled', false) // 下一步按钮置灰
  },
}
</script>
<style scoped>
.kcb_cebtn {
  position: fixed;
  bottom: 0;
  width: 100%;
  z-index: 99999;
}
.tza_info{
  padding: 0.1rem 0 0 0.2rem;
  ul > li:first-child{
    position: relative;
    font-size: 0.16rem;
    padding: 0.05rem 0.25rem;

    & > p{
        color: #000;
      }

    &::after {
       width: 0.1rem;
       height: 0.1rem;
       content: "";
       background: #ec656b;
       display: inline-block;
       border-radius: 50%;
       position: absolute;
       left: 0;
       top: 0;
       bottom: 0;
       margin: auto;
     }
  }

  p{
    color: #727272;
  }
}
</style>
