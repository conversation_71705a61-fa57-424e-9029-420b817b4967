<template>
  <div class="user_main">
    <h5 class="com_title">请填写以下信息</h5>
    <div class="input_form">
      <div class="ui field text">
        <label class="ui label">资金账号</label>
        <input
          v-model.trim="inputModel.account.value"
          :maxlength="inputModel.account.maxlength"
          type="text"
          class="ui input"
          :placeholder="inputModel.account.placeholder"
        />
        <a
          class="txt_close"
          @click.stop="inputModel.account.value=''"
          v-show="inputModel.account.value!=''"
        ></a>
      </div>
      <div class="ui field text">
        <label class="ui label">姓名</label>
        <input
          v-model.trim="inputModel.name.value"
          :maxlength="inputModel.name.maxlength"
          type="text"
          class="ui input"
          placeholder="请输入姓名"
        />
        <a
          class="txt_close"
          @click.stop="inputModel.name.value=''"
          v-show="inputModel.name.value!=''"
        ></a>
      </div>
      <div class="ui field text">
        <label class="ui label">身份证号</label>
        <input
          v-model.trim="inputModel.idno.value"
          :maxlength="inputModel.idno.maxlength"
          type="text"
          class="ui input"
          placeholder="请输入身份证号"
        />
        <a
          class="txt_close"
          @click.stop="inputModel.idno.value=''"
          v-show="inputModel.idno.value!=''"
        ></a>
      </div>
      <div class="ui field text">
        <label class="ui label">验证码</label>
        <input
          v-model.trim="inputModel.code.value"
          :maxlength="inputModel.code.maxlength"
          type="text"
          class="ui input"
          placeholder="请输入图形验证码"
        />
        <a
          class="txt_close"
          @click.stop="inputModel.code.value=''"
          v-show="inputModel.code.value!=''"
        ></a>
        <a class="code_img" @click.stop="getImgCode">
          <img :src="codeImgUrl" />
        </a>
      </div>
    </div>
    <div class="bottom_check">
      <p class="tips">提供的证件信息仅用于身份实名验证，我们将严格保护您的个人隐私安全，并仅用于此目的。</p>
    </div>
    <div class="ce_btn mt20">
      <a v-throttle class="ui button block rounded" @click.stop="verifyAccountInput">下一步</a>
    </div>
  </div>
</template>

<script>
import { checkInput } from '@/common/util'
import { getImgCode, verifyImgCode } from '@/service/comServiceNew'
export default {
  data () {
    return {
      handleResult: true,
      loginType: $h.getSession('loginType') || '1', // 账号类型 1普通账号 2信用账号
      ygtUserInfo: {},
      inputModel: {
        account: {
          name: '资金账户',
          value: '',
          maxlength: '15',
          format: 'num',
          placeholder: ''
        },
        name: {
          name: '姓名',
          value: '',
          maxlength: '15',
          minlength: '2',
          format: 'name'
        },
        idno: {
          name: '身份证号',
          value: '',
          minlength: '18',
          maxlength: '18',
          format: 'idno'
        },
        code: {
          name: '图片验证码',
          value: '',
          maxlength: '4',
          minlength: '4',
          format: 'enNum'
        }
      },
      codeImgUrl: '', // 图片验证码图片url
      codeImgKey: ''
    }
  },
  activated () {
    this.$store.commit('updateBusinessNextBtnStatus', false) // 隐藏下一步按钮
    this.getImgCode() // 获取验证码
    // 清空输入框
    for (const key in this.inputModel) {
      if (this.inputModel.hasOwnProperty(key)) {
        const element = this.inputModel[key]
        element.value = ''
      }
    }
    this.inputModel.account.placeholder = this.loginType == '1' ? '请输入资金账户' : '请输入信用资金账户'
  },
  methods: {
    // 获取图片验证码
    getImgCode () {
      getImgCode({}, {}).then(
        res => {
          if (res.error_no === '0') {
            let results = res.results
              ? res.results[0]
              : res.generateVerifyCode[0]
            this.codeImgUrl = results.imageCode
            this.codeImgKey = results.mobileKey
            this.inputModel.code.value = ''
          } else {
            _hvueToast({
              icon: 'error',
              mes: res.error_info
            })
          }
        },
        err => {
          console.log(err)
        }
      )
    },
    // 校验图片验证码
    verifyImgCode () {
      verifyImgCode(
        { mobileKey: this.codeImgKey, imageCode: this.inputModel.code.value },
        {}
      ).then(
        res => {
          if (res.error_no === '0') {
            // 调用父组件的下一步事件
            this.$parent.emitNextEvent()
          } else {
            _hvueToast({
              icon: 'error',
              mes: res.error_info
            })
            this.getImgCode()
          }
        },
        err => {
          console.log(err)
        }
      )
    },
    verifyAccountInput () {
      let inputs = Object.values(this.inputModel)
      let flag = ''
      for (let s = 0; s < inputs.length; s++) {
        let el = inputs[s]
        flag = checkInput(el)
        if (flag != '') {
          break
        }
      }
      if (flag == '') {
        this.verifyImgCode()
      }
    },
    doCheck () {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        // 可以下一步
        resolve()
      })
    },
    /**
     * 请求参数打包
     */
    reqParamPackage () {
      // 业务请求参数封装
      let params = {
        fundAccount: this.inputModel.account.value,
        userName: this.inputModel.name.value,
        identityNum: this.inputModel.idno.value,
        code: this.inputModel.code.value
      } // 提交需要的参数
      return params
    },
    putFormData () {
      let formData = {
        verifyAccountInfo: {
          bussinessType: this.loginType,
          fundAccount: this.inputModel.account.value,
          userName: this.inputModel.name.value,
          identityNum: this.inputModel.idno.value,
          code: this.inputModel.code.value
        }
      } // 需要保存的表单数据
      return formData
    },
    // 提交后对结果进行处理的方法
    async handleResultFunc (res) {
      this.ygtUserInfo = res.checkIdno[0]
      this.ygtUserInfo.name = this.inputModel.name.value
      Object.assign(this.$parent.publicParam, this.ygtUserInfo)
      this.$parent.publicParam.serivalId = res.results[0].serivalId
      this.$parent.flow.serivalId = res.results[0].serivalId
      $h.setSession('ygtUserInfo', this.ygtUserInfo, {encrypt: false})
      // 1、查询表单状态
      // let formStatusResult = await queryUserFlowStatus({
      //   userId: this.ygtUserInfo.userId,
      //   businessCode: this.$router.query.type
      // })
      // if (formStatusResult.error_no === '0') {
      //   let formStatus = formStatusResult.results[0].formStatus
      // } else {
      //   _hvueAlert({ mes: formStatusResult.error_info })
      // }
      this.$parent.$emit('init')
    }
  }
}
</script>
