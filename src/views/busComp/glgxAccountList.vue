<!-- 增开股东户-账户查询 -->
<template>
  <div class="glgxAccountList">
    <headComponent headerTitle="确认账户"></headComponent>
    <h5 class="com_title">将为您的下列账户办理关联关系确认：</h5>
    <article>
      <h5 class="glgx_tit">深市账户</h5>
      <ul class="glgx_ul">
        <li>
          <div>
            <p><span>深A：3131</span></p>
            <p class="font12 redcol"><span>需上传无余额证明</span></p>
          </div>
          <a class="ui button block rounded" @click.stop="setIdInRedis">上传资料</a>
        </li>
        <li>
          <div>
            <p><span>深A：3131</span></p>
            <p class="font12 redcol"><span>需上传无余额证明</span></p>
          </div>
          <a class="ui button block rounded" @click.stop="setIdInRedis">开始视频</a>
        </li>
        <li>
          <div>
            <p><span>深A：3131</span></p>
          </div>
        </li>
      </ul>
      <h5 class="glgx_tit">沪市账户</h5>
      <ul class="glgx_ul">
        <li>
          <div>
            <p><span>深A：3131</span></p>
            <p class="font12 redcol"><span>需上传无余额证明</span></p>
          </div>
          <a class="ui button block rounded" @click.stop="setIdInRedis">上传资料</a>
        </li>
        <li>
          <div>
            <p><span>深A：3131</span></p>
            <p class="font12 redcol"><span>需上传无余额证明</span></p>
          </div>
          <a class="ui button block rounded" @click.stop="setIdInRedis">开始视频</a>
        </li>
        <li>
          <div>
            <p><span>深A：3131</span></p>
          </div>
        </li>
      </ul>
      <div class="cond_tips textleft">
        <p>温馨提示：</p>
        <p>未建立委托交易关系的证券账户，需要上传账户无余额的证明材料才能办理关联关系确认。</p>
      </div>
    </article>

  </div>
</template>

<script>
import headComponent from '@/components/headComponent'

export default {
  components: {
    headComponent,
  },
  props: ['pageParam'],
  data () {
    return {
      szaAccountList: [],
      shaAccountList: [],
      nextFlag: false
    }
  },
  activated () {
    let list = this.pageParam
    this.szaAccountList = []
    this.shaAccountList = []
    list.forEach(item => {
      if (item.market === '00') {
        this.szaAccountList.push(item)
      } else if (item.market === '10') {
        this.shaAccountList.push(item)
      }
    })
  },
  methods: {
    doCheck () {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        // 可以下一步
        resolve()
      })
    },
    /**
     * 请求参数打包
     */
    reqParamPackage () {
      // 业务请求参数封装
      let params = {}
      return params
    },
    putFormData () {
      let formData = {}
      return formData

    }
  }
}
</script>
<style>
  .glgx_tit{
    padding: 0.15rem;
  }
  .glgx_ul{
    width:95%;
    margin:auto;
    background:#fff;
  }
  .glgx_ul li{
    height:0.6rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 0.15rem;
    border-bottom:1px solid #eee;
  }
</style>
