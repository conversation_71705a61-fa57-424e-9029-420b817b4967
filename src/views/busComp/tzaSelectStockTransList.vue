<template>
  <div>
    <div class="agree_main" v-if="stockListLocal.length">
      <h5 class="title">本地深圳市场账户（建议选择本地账户开立）</h5>
      <ul class="account_list">
        <li
            :class="{
            disable: item.holderStatus != '0'
          }"
            v-for="(item, index) in stockListLocal"
            :key="index"
        >
          <p>
            <span :class="accountStyle(item)" @click.stop="accSelEvent(item)"
            >
              {{ `${item.stkbdName}：${item.stockAccount}` }}</span
            >
            <span v-if="item.holderStatus !== '0'" class="acc_errortips">当前账户状态异常，请联系开户营业部或投资经理协助规范账户后，重新申请办理</span>
          </p>
          <span
              class="status acc_status"
          >{{
              item.holderStatus | filterAccStatus
            }}</span
          >
          <span
            class="status open_status"
          >未登记</span>
        </li>
      </ul>
    </div>
    <div class="agree_main" v-if="stockListZd.length">
      <h5 class="title">中登深圳市场账户</h5>
      <ul class="account_list">
        <li
            :class="{
            disable: item.holderStatus != '0'
          }"
            v-for="(item, index) in stockListZd"
            :key="index"
        >
          <p>
            <span :class="accountStyle(item)" @click.stop="accSelEvent(item)"
            >
              {{ `${item.stkbdName}：${item.stockAccount}` }}</span
            >
            <span v-if="item.holderStatus !== '0'" class="acc_errortips">当前账户状态异常，请联系开户营业部或投资经理协助规范账户后，重新申请办理</span>
          </p>
          <span
              class="status acc_status"
          >{{
              item.holderStatus | filterAccStatus
            }}</span
          >
          <span
              class="status open_status"
          >未登记</span>
        </li>
      </ul>
    </div>

    <div class="agree_main" v-if="stockListGz.length">
      <h5 class="title">股转市场账户</h5>
      <ul class="account_list">
        <li
            :class="{
            disable: true,
          }"
            v-for="(item, index) in stockListGz"
            :key="index"
        >
          <p>
            <span>
              {{ `${item.stkbdName}：${item.stockAccount}` }}
            </span>
          </p>
<!--          <span class="status">已登记</span>-->
          <span
            class="status acc_status"
          >{{
              item.holderStatus | filterAccStatus
            }}</span
          >
          <span
            class="status open_status"
          >已登记</span>
        </li>
      </ul>
    </div>
    <div class="cond_tips" v-if="stockListSz.length || stockListGz.length">
      <p>温馨提示：</p>
      <p>1、可登记持有退市股票的深A、深B账户</p>
      <p>2、若账户状态异常，请先将账户调整至正常状态再进行登记</p>
    </div>
    <div class="cond_tips" v-else>
      <p>
        您本地还没有可办理业务的账户，请先前往开通
        <router-link
            :to="{name: 'business',query: {type: 'zkgdh'}}"
            class="link"
        >去开通</router-link>
      </p>
    </div>
  </div>
</template>
<script>
export default {
  props: ['pageParam'],
  data() {
    return {
      type: this.$route.query.type, // 业务类型
      // openedList: [], // 已经开通的市场
      selectedIndexes: [],
      submitAccountList: [],
      acodeAccount: '',
      stockAccount: [],
      isNext: false, // 是否能继续下一步
      bName: {
        'kcbkt': '科创板权限',
        'cybkt': '创业板权限',
        'gszqqxkt': '债券权限'
      },
      stockListSz: [],
      stockListGz: [],
      stockListLocal: [],
      stockListZd: []
    }
  },
  mounted() {
    this.$on('putFormData', () => {
      return this.putFormData()
    })
    this.$on('reqParamPackage', () => {
      return this.reqParamPackage()
    })
  },
  activated() {
    this.isNext = false
    this.selectedIndexes = []
    this.submitAccountList = []

    let list = this.pageParam
    this.stockListSz = list[0].stockListSz
    this.stockListGz = list[0].stockListGz

    this.stockListSz && typeof this.stockListSz === 'string' && (this.stockListSz = JSON.parse(this.stockListSz))
    this.stockListGz && typeof this.stockListGz === 'string' && (this.stockListGz = JSON.parse(this.stockListGz))

    this.stockListLocal = this.stockListSz.filter(item => item.zdFlag === '1')
    this.stockListZd = this.stockListSz.filter(item => item.zdFlag === '0')

    this.isNext = this.stockListSz.some(item => item.holderStatus === '0')
    if (!this.isNext) {
      this.$store.commit('updateNextBtnText', '返回首页')
    }
  },
  methods: {
    accSelEvent(item) {
      this.type === 'tzakt' && this.selectedIndexes.length && (this.selectedIndexes = [])
      if (this.selectedIndexes.indexOf(item) !== -1) {
        this.selectedIndexes.remove(item)
      } else {
        this.selectedIndexes.push(item)
      }
    },
    accountStyle(item) {
      let _class = ''
      if (item.holderStatus === '0') {
        _class = 'icon_radio'
      }
      if (this.selectedIndexes.indexOf(item) !== -1) {
        _class += ' checked'
      }
      return _class
    },
    checkSubmit() {
      if (this.selectedIndexes.length === 0) {
        _hvueToast({
          mes: '请选择账户',
        })
        return false
      }
      this.submitAccountList = []
      this.stockAccount = []
      this.acodeAccount = ''
      for (let i = 0; i < this.selectedIndexes.length; i++) {
        const el = this.selectedIndexes[i]
        let account = {
          stkbd: el.stkbd,
          market: el.market,
          stockAccount: el.stockAccount,
          trdacctExcls: el.trdacctExcls,
        }
        this.submitAccountList.push(account)
        this.stockAccount.push(el.stockAccount)
        this.acodeAccount = el.acodeAccount
      }
      return true
    },
    doCheck() {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        if (!this.isNext) {
          this.$router.push({ name: 'index' })
          return
        }
        if (this.checkSubmit() === true) {
          // 可以下一步
          resolve()
        }
      })
    },
    /**
     * 请求参数打包
     */
    reqParamPackage() {
      // 业务请求参数封装
      let params = {
        stockAccountInfo: JSON.stringify(this.submitAccountList),
        stockAccount: this.stockAccount.join(','),
        acodeAccount: this.acodeAccount
      }
      return params
    },
    putFormData() {
      let formData = {
        chooseAccount: this.submitAccountList,
      }
      return formData
    },
  },
}
</script>
<style scoped>
.cond_tips{
  text-align: left;
}
.account_list li .status {
  z-index: 0;
}

.account_list li .acc_status{
  left: 1.7rem;
  right: auto;
  padding: 0.01rem 0.04rem;
  border-radius: 0.06rem;
  border: 1px solid #caa634;
  color: #caa634;
}
.account_list li .open_status {
  left: auto;
  right: 0.15rem;
}

.account_list li .acc_errortips {
  display: inline-block;
  font-size: 0.13rem;
  line-height: 0.2rem;
  color: #ba0105;
}

.account_list li .icon_radio {
  display: block;
  padding: 0.11rem 0.4rem 0.11rem 0.25rem;
}
.account_list li .icon_radio:before {
  top: 50%;
  margin-top: -0.09rem;
  left: 0;
  right: 0;
}
</style>
