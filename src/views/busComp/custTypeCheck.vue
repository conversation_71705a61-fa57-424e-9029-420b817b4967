<template>
  <article class="content">
    <div class="test_result">
      <div class="test_level risk">
        <div class="test_canvas">
          <span style="display:none;" id="testData">{{pageParam[0].paperScore}}</span>
          <div class="info">
            <strong :class="{error : pageParam[0].isEffective==0}">{{pageParam[0].riskLevelDesc|fillRiskLevel(pageParam[0].isEffective)}}</strong>
            <!-- <span>{{pageParam[0].riskLevel}}</span> -->
          </div>
          <canvas id="testCanvas" width="320" height="320"></canvas>
        </div>
        <p>
          <span
            class="state"
            v-show="pageParam[0].isEffective==1"
            :class="{error : pageParam[0].isEffective==0 ,warn : pageParam[0].isEffective==2}"
          >您的测评有效期至 {{pageParam[0].corpEndDate}}</span>
        </p>
        <span class="state error" v-show="pageParam[0].isEffective==0">您的测评已过期，请重新测评</span>
      </div>
      <div class="approp_info spel">
        <ul>
          <li>
            <span class="tit">拟投资期限</span>
            <p>{{pageParam[0].investTermDesc|fillLine}}</p>
          </li>
          <li>
            <span class="tit">拟投资品种</span>
            <p>{{pageParam[0].investTypeDesc|fillLine}}</p>
          </li>
          <!-- <li>
              <span class="tit">建议风险承受能力</span>
              <p>{{pageParam[0].riskLevel}}</p>
          </li>-->
        </ul>
      </div>
    </div>
    <div class="ce_btn">
      <a v-throttle class="ui button block rounded" @click.stop="reTest">重新测评</a>
      <a v-throttle class="ui button block rounded border mt20" @click.stop="pageBack">返回</a>
    </div>
  </article>
</template>

<script>
export default {
  props: ['pageParam'],
  name: 'custTypeCheck',
  data () {
    return { }
  },
  activated(){
    // 隐藏business.vue的下一步按钮
    this.$store.commit('updateBusinessNextBtnStatus', false) // 隐藏下一步按钮
  },
  methods: {
    // 重新测评
    reTest () {
      // 调用父组件提交事件方法
      this.$parent.next({}).then(res => {
        this.$parent.$emit('init', {})
      })
    },
    doCheck () {
      // 执行下一步前的校验
      new Promise((resolve, reject) => {
        if (this.totalFlag) {
          // 可以下一步
          resolve()
        } else {
          // 返回业务办理首页;
          this.$router.push({ name: 'index' })
        }
      })
    },
    /**
     * 请求参数打包
     */
    reqParamPackage () {
      // 业务请求参数封装
      let params = {
        param1: '',
        param2: ''
      }
      return params
    },
    putFormData () {
      let formData = {}
      return formData
    },
    pageBack () {
      // 调用父组件返回上一步方法
      this.$parent.back({})
    }
  }
}
</script>
