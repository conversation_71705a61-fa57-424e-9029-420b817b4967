<!-- 资质查询（准入条件查询）组件 -->
<template>
  <div v-cloak>
    <div class="cond_box" v-if="openAccountFlag != '0'">
      <h5 class="title">办理{{ businessName }}，需要满足以下条件</h5>
      <ul v-for="(item, index) in checkData" :key="index">
        <li
          :class="{
            error:
              item.value == '0' ||
              (item.value == '2' && item.key === 'openAccountFlag'),
            ok: item.value == '1',
            warn: item.value == '2' && item.key != 'openAccountFlag',
          }"
        >
          <div class="tit">
            <p>{{ item.desc }}</p>
            <a
              class="link"
              @click.stop="toAccountDetail"
              v-show="item.key === 'openAccountFlag'"
              >账户详细</a
            >
            <a
              class="link"
              @click.stop="toregFlag"
              v-show="item.key === 'regFlag' && item.value != '1'"
              >去撤指定</a
            >
            <a
              class="link"
              @click.stop="totatalRows"
              v-show="item.key === 'tatalRows' && item.value != '1'"
              >去转托管</a
            >
            <a
              class="link"
              @click.stop="toRisk"
              v-show="item.key === 'minRankFlag' && item.value != '1'"
              >重新测评</a
            >
            <span v-show="item.value !== '1'" class="tips">{{
              item.reasonDesc
            }}</span>
          </div>
        </li>
      </ul>
    </div>
    <div class="cond_tips">
      <p v-if="!totalFlag">抱歉，您办理条件未满足暂时不能办理</p>
      <p v-else>恭喜您，满足全部基本条件</p>
    </div>

    <!-- 账户已全部开通accountViewList-->
    <accountViewList
      v-if="openAccountFlag == '0'"
      :flowNameCn="businessName"
      :initData="pageParam[0].accountList ? pageParam[0].accountList : []"
    ></accountViewList>
  </div>
</template>
<script>
import accountViewList from '@/views/common/accountViewList' // 账户已全部开通
export default {
  components: { accountViewList },
  props: ['pageParam'],
  data() {
    return {
      businessName: this.$parent.businessName, // 业务中文名
      checkData: [], // 检查列表
      totalFlag: false, // 整体条件是否满足
      openAccountFlag: '', // 账户是否满足：0已全部开通，1满足，2不满足
    }
  },
  activated() {
    this.$store.commit('updateNextBtnText', '下一步')
    this.totalFlag = false
    this.checkData = []
    let flag = true // 整体条件是否满足
    // 处理接口返回的结果集，拼装成前端展示用的数组结构checkData
    let key = this.pageParam[0].checkItem.split('|') // 检查项key
    let value = this.pageParam[0].itemValue.split('|') // 检查项value
    let desc = this.pageParam[0].itemDesc.split('|') // 检查项标题
    let reasonDesc = this.pageParam[0].reasonDesc.split('|') // 检查项描述
    for (var i = 0; i < key.length; i++) {
      let json = {
        key: key[i],
        value: value[i],
        desc: desc[i],
      }
      // 检查项描述,当value状态为2时，展示tips的提示语,默认展示reasonDesc
      if (value[i] === '2') {
        let tips = this.pageParam[0].unknownItemDesc.split('|') // 检查项描述,当itemValue状态为2时，展示这里的提示语
        json.reasonDesc = tips[i]
      } else {
        json.reasonDesc = reasonDesc[i]
      }
      this.checkData.push(json)
      // 除了openAccountFlag为2是不满足，其他为0是不满足
      if (
        value[i] === '0' ||
        (value[i] === '2' && key[i] === 'openAccountFlag')
      ) {
        // 不满足办理条件
        flag = false
        // 修改按钮文字
        this.$store.commit('updateNextBtnText', '返回首页')
      }
      if (key[i] === 'openAccountFlag') {
        this.openAccountFlag = value[i]
      }
    }
    this.totalFlag = flag
    console.log(this.checkData)
    if (this.openAccountFlag === '0') {
      this.$store.commit('updateBusinessNextBtnStatus', false) // 隐藏下一步按钮
    }
  },
  methods: {
    // 跳转账户详细
    toAccountDetail() {
      this.$router.push({
        name: 'accountDetail',
        query: {
          accountList: this.pageParam[0].accountList,
          openAccountFlag: this.openAccountFlag,
        },
      })
    },
    // 跳转到风险测评
    toRisk() {
      this.$router.push({
        name: 'business',
        query: { type: 'fxcp', name: '风险测评' },
      })
    },
    // 跳转到撤指定
    toregFlag() {
      this.$router.push({
        name: 'business',
        query: { type: 'shczd', name: '上海撤指定' },
      })
    },
    // 跳转到转托管
    totatalRows() {
      this.$router.push({
        name: 'business',
        query: { type: 'szztg', name: '深圳转托管' },
      })
    },
    doCheck() {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        // 开发调试环境测试可以下一步
        if (this.totalFlag) {
          // 可以下一步
          resolve()
        } else {
          // 返回业务办理首页;
          this.$router.push({ name: 'index' })
        }
      })
    },
    /**
     * 请求参数打包
     */
    reqParamPackage() {
      // 业务请求参数封装
      let params = {}
      return params
    },
    // 表单参数打包
    putFormData() {
      let formData = {}
      return formData
    },
  },
}
</script>
