<!-- 协议列表组件1 常用在账户列表选择组件下 -->
<template>
  <div v-show="isShow" :class="{ bottom_check: isFixed, white_bg: isFixed }">
    <div v-show="!showProtocolParam.isShow" class="agree_main">
      <ul class="rule_list">
        <li :class="item.isRead ? 'read' :'unread'" v-for="(item, index) in pageParam" :key="index">
          <a @click.prevent="showProtocolClick(item, index)">《{{ item.agreeName }}》</a>
        </li>
      </ul>
      <div class="rule_check">
        <span
            class="icon_check"
            :class="{ checked: isChecked }"
            @click.stop="readProtocolCheck"
        ></span>
        <label>我已阅读并同意</label>
      </div>
    </div>
    <div
      v-show="isShowFundBtn && !showProtocolParam.isShow"
      class="fund_botbtn"
    >
      <div class="left">
        <span
          class="icon_radio"
          :class="{ checked: allClickFlag }"
          @click.stop="chooseAllClick"
          >全选（{{ selectedAccountsLength }}）</span
        >
      </div>
      <div class="right">
        <a v-throttle class="ui button block" @click.stop="nextStep"
          >立即开通</a
        >
      </div>
    </div>
    <showProtocol
      v-if="showProtocolParam.isShow"
      v-model="showProtocolParam.isShow"
      :agreementId="showProtocolParam.agreementId"
      :isRead="showProtocolParam.isRead"
      :readSeconds="showProtocolParam.readSeconds"
      @showProtocolCall="showProtocolCall"
    ></showProtocol>
  </div>
</template>
<script>
import showProtocol from '@/components/showProtocol' // 协议详情
export default {
  props: {
    pageParam: {
      type: Array,
    },
  },
  components: { showProtocol },
  data() {
    return {
      isShow: true,
      isChecked: false,
      agreementIds: [],
      businessCode: this.$route.query.type,
      // 协议详情组件参数
      showProtocolParam: {
        isShow: false, // 是否展示
        title: '', // 协议名称
        agreementId: '',
        agreementIndex: '',
        isRead: false, // 是否已阅读
        readSeconds: 0, //协议阅读时间
        needCopy: '', //是否需要抄录
        copyContent: '' //抄录内容
      },
      // 协议必读的业务和对应秒数配置
      readSecondsArr: {
        tsgpkt: 15,
        jcjjqxkt: 30,
      },
    }
  },
  activated() {
    if (this.pageParam.length < 1) {
      _hvueAlert({ mes: '未查询到协议', title: '提示' })
    }
    // 注册vuebus方法 接受fundCompanyList组件的是否展示已开通账户状态
    this.$bus.on('showOpenAccountFlag', (isShow) => {
      this.isShow = !isShow
    })
    // 已阅读协议
    this.$bus.on('isReadAgreement', (agreementIndex) => {
      this.isRead(agreementIndex)
    })
    let hasRead = $h.getSession('agreementIds') || ''
    for (let index = 0; index < this.pageParam.length; index++) {
      const element = this.pageParam[index]
      // 是否已阅读，退市整理股开通以外的业务默认为已阅读
      if (
        hasRead.includes(element.agreementId) &&
        this.businessCode !== ('tsgpkt' && 'jcjjqxkt')
      ) {
        element.isRead = true
      } else {
        element.isRead = false
      }
      this.$set(this.pageParam, index, element)
      this.agreementIds.push(element.agreementId)
    }
  },
  computed: {
    // 是否全选，保存在vuex中
    allClickFlag() {
      return this.$store.state.allFundCheckClickFlag
    },
    // 下一步按钮是否固定在底部，保存在vuex中
    isFixed() {
      return this.$store.state.agreementIsFixed
    },
    // 已勾选账户总数，保存在vuex中
    selectedAccountsLength() {
      return this.$store.state.selectedAccountsLength
    },
    // 是否展示基金户的下一步按钮
    isShowFundBtn() {
      // 如果公用的下一步按钮隐藏了，则展示基金的下一步按钮
      let businessNextBtnShow = this.$store.state.businessNextBtnShow
      if (businessNextBtnShow) {
        return false
      } else {
        return true
      }
    },
  },
  watch: {
    'showProtocolParam.isShow': {
        handler (newVal) {
            this.$bus.emit('showProtocolFlag', newVal) // 通过vuebus调用fundCompanyList组件的方法修改是否未开通的基金账户
        },
    },
  },
  methods: {
    toDetail(agreement, index) {
      // 查看协议详情
      this.$router.push({
        name: 'agreementDetail',
        query: {
          agreementId: agreement.agreementId,
          agreementTitle: agreement.agreementName,
          agreementIndex: index,
          type: this.$route.query.type,
        },
      })
    },
    isRead(agreementIndex) {
      this.$set(this.pageParam[agreementIndex], 'isRead', true)
    },
    readProtocolCheck() {
      for (let index = 0; index < this.pageParam.length; index++) {
        const element = this.pageParam[index]
        if (!element.isRead) {
          this.isChecked = false
          _hvueToast({ mes: '请先阅读完所有协议' })
          return
        }
      }
      this.isChecked = !this.isChecked
    },
    // 展示协议详情
    showProtocolClick(agree, index) {
      this.showProtocolParam.isShow = true
      this.showProtocolParam.isRead = agree.isRead
      this.showProtocolParam.agreementId = agree.agreementId
      this.showProtocolParam.agreementIndex = index
      //强制阅读时间配置
      let hasRead = $h.getSession('agreementIds') || ''
      let type = this.$route.query.type
      if (hasRead.indexOf(agree.agreementId) === -1) {
        this.showProtocolParam.readSeconds = this.readSecondsArr[type] || 0
      } else {
        this.showProtocolParam.readSeconds = 0
      }
      //强制阅读时间配置
      //基金开户打开协议时，取消固定按钮在底部
      if (type === 'kfsjjkh') {
        this.$store.state.agreementIsFixed = false
      }
    },
    // 展示协议详情返回
    showProtocolCall(agree) {
      this.showProtocolParam.needCopy = agree.needCopy
      this.showProtocolParam.copyContent = agree.copyContent
      this.showProtocolParam.isShow = false
      this.isRead(this.showProtocolParam.agreementIndex)
      // 标记已经阅读过的协议
      let readAgreements = $h.getSession('agreementIds') || ''
      let keyWords = $h.getSession('keyWords') ? JSON.parse($h.getSession('keyWords')) : {}
      keyWords = Object.assign(keyWords, agree.keyWords)
      if (readAgreements.indexOf(this.showProtocolParam.agreementId) < 0) {
        $h.setSession(
          'agreementIds',
          readAgreements
            ? readAgreements + ',' + this.showProtocolParam.agreementId
            : this.showProtocolParam.agreementId
        )

        if (this.showProtocolParam.copyContent) {
          keyWords[`declareContent_${this.showProtocolParam.agreementId}`] = this.showProtocolParam.copyContent
          $h.setSession('keyWords', JSON.stringify(keyWords))
        }
      }
      //基金开户打开协议时，取消固定按钮在底部
      if (this.$route.query.type === 'kfsjjkh') {
        this.$store.state.agreementIsFixed = true
      }
    },
    checkSubmit() {
      if (!this.isChecked) {
        _hvueAlert({ title: '温馨提示', mes: '请先勾选同意按钮' })
        return false
      }
      this.agreementIds = []
      for (let index = 0; index < this.pageParam.length; index++) {
        const element = this.pageParam[index]
        this.agreementIds.push(element.agreementId)
      }
      return true
    },
    doCheck() {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        if (this.checkSubmit()) {
          // 可以下一步
          resolve()
        }
      })
    },
    /**
     * 请求参数打包
     */
    reqParamPackage() {
      let keyWords = $h.getSession('keyWords') || ''
      // 业务请求参数封装
      let params = {
        agreementId: this.agreementIds.join(','),
        keyWords: keyWords
      }
      return params
    },
    putFormData() {
      let keyWords = $h.getSession('keyWords') || ''
      let formData = {
        signProtocol: { agreementId: this.agreementIds.join(','), keyWords: keyWords },
      }
      return formData
    },
    // 选择全部点击事件
    chooseAllClick() {
      let allFlag = this.$store.state.allFundCheckClickFlag
      this.$store.commit('updateAllFundCheckClickFlag', !allFlag) // 是否全选，存在vuex中
      this.$bus.emit('changeAllFlag', !allFlag) // 通过vuebus调用账户组件的方法修改账户勾选状态
    },
    // 调用父组件的下一步事件
    nextStep() {
      this.$parent.emitNextEvent()
    },
  },
  destroyed() {
    this.$bus.off('showOpenAccountFlag') // 事件销毁，防止多次触发
    this.$bus.off('isReadAgreement')
  },
}
</script>
<style scoped>
.bottom_check {
  position: fixed;
  bottom: 0;
  border-top: 0.05rem solid #f9f9f9;
  z-index: 99999;
  background: white;
  width: 100%;
}
</style>
