/*
* 两融合约展期
* @Author: Way
* @Date: 2020-09-22 09:52:10
 * @Last Modified by: Way
 * @Last Modified time: 2020-12-17 17:22:03
*/
<template>
    <div>
        <div class="extension_content">
            <div class="Extension_title" ref="Extension_title">
                <ul ref="ul_title" style="transform: translate(0px, 0px)">
                    <li class="name">名称/代码</li>
                    <li class="date">开仓/到期日期</li>
                    <li class="num">总负债(元)</li>
                    <li class="num">负债利息(元)</li>
                    <li class="num" style="width: 30%;">状态</li>
                </ul>
            </div>
            <div v-if="pageParam.length>0" class="ext_cont_scroll" :style="{height:scrollHeight + 'px'}">
                <div class="extension_list" ref="bscroll" style="height:100%;width:100%">
                    <ul>
                        <li :class="{ checked: selectedAccounts.indexOf(item)!=-1}" @click.stop="accSelEvent(item)"
                            v-for="(item,index) in pageParam" :key="index">
                            <div class="item_td name">
                                <strong>{{getStockName(item.stockName)}}</strong>
                                <em>{{item.stockCode}}</em>
                            </div>
                            <div class="item_td date">
                                <p>{{item.openDate}}</p>
                                <p>{{item.retEndDate}}</p>
                            </div>
                            <div class="item_td num">
                                <p>{{item.totalDebit}}</p>
                            </div>
                            <div class="item_td num">
                                <p>{{item.realCompactPreInterest}}</p>
                            </div>
                            <div class="item_td num" style="width: 30%;">
                                <p>{{getState(item.compactPostponeStatus)}}</p>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>
            <p v-else class="no_moretips">
                <span>暂无数据</span>
            </p>
        </div>

        <div class="bottom_check" ref="bottom_check">
            <p class="tips">温馨提示: 合约到期前30天可以申请合约展期，单次申请延长到期时间6个月</p>
            <div class="fund_botbtn">
                <div class="left"><span class="icon_radio" @click.stop="allClick" :class="{checked:allCheck}">全选（{{selectedAccounts.length}}）</span></div>
                <div class="right"><a v-throttle class="ui button block" @click.stop="checkSubmit">一键展期</a></div>
            </div>
        </div>

        <template v-if="showAlert">
            <!-- 遮罩层  -->
            <div class="ui dialog-overlay"></div>
            <!-- 弹出层  -->
            <div class="ui dialog fade in" style="width: 88%; left: 6%;">
                <div class="ui dialog-cnt">
                    <h4>合约展期确认</h4>
                    <div>
                        <div class="com_extension">
                            <h5 class="title">共选择 <strong>{{selectedAccounts.length}}</strong> 只</h5>
                            <ul>
                                <li v-for="(item,index) in selectedAccounts" :key="index">
                                    <p>编号<span>{{item.compactId}}</span></p>
                                    <span class="name">{{item.stockName}}</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="ui dialog-btn"><button v-throttle class="ui button block cancel" @click.stop="showAlert=false">取消</button><button
                        v-throttle class="ui button block" @click.stop="nextStep">确定</button></div>
            </div>
        </template>
    </div>
</template>

<script>
import BScroll from 'better-scroll'
import headComponent from '@/components/headComponent' // 头部
export default {
  components: {
    headComponent
  },
  props: {
    pageParam: {
      type: Array
    }
  },
  data () {
    return {
      selectedAccounts: [], // 已选择的账户
      allCheck: false,
      showAlert: false,
      scroller: null,
      scrollHeight: 0
    }
  },
  activated () {
    var sc_h = window.innerHeight - 44 - this.$refs.bottom_check.offsetHeight - this.$refs.Extension_title.offsetHeight
    this.scrollHeight = sc_h
    this.selectedAccounts = []
    this.showAlert = false
    this.allCheck = false
    // this.$store.commit('updateIsShowHead', false) // 隐藏头部
    this.$store.commit('updateBusinessNextBtnStatus', false) // 隐藏下一步按钮
    this.$nextTick(() => {
      this.InitScroll()
    })
  },
  mounted () {},
  methods: {
    InitScroll () {
      const that = this
      let position = {
        preventDefault: false, // 阻止浏览器默认行为
        probeType: 3, // 1:非实时派发scroll事件; 2:在屏幕滑动的过程中实时的派发 scroll 事件; 3:不仅在屏幕滑动的过程中，而且在 momentum 滚动动画运行过程中实时派发 scroll 事件。
        scrollX: true, // 启动x轴滚动
        scrollY: true, // 启动y轴滚动
        momentum: false, // 是否开启快速滑动
        directionLockThreshold: 0, // 斜滑时，锁定一个方向滚动
        stopPropagation: true,
        bounce: { // 滑动到边缘时出现回弹动画
          top: false,
          bottom: false,
          left: false,
          right: false
        }
      }
      this.scroller = new BScroll(this.$refs.bscroll, position)
      this.scroller.on('scroll', (pos) => {
        // 滚动函数
        that.$refs.ul_title.style.transform = `translate(${pos.x}px, 0px)`
      })
      this.scroller.on('scrollEnd', (pos) => {
        // 滚动函数
        that.$refs.ul_title.style.transform = `translate(${pos.x}px, 0px)`
      })
    },
    getStockName (s) {
      if (s !== '') {
        return s
      } else {
        return '其他合约'
      }
    },
    getState (s) {
      // compactPostponeStatus   0-可申请  1-已申请 2-审批通过
      if (s === '0') {
        return '可申请'
      } else if (s === '1') {
        return '已申请'
      } else if (s === '2') {
        return '审批通过'
      } else if (s === '3') {
        return '审批不通过'
      } else if (s === '4') {
        return '不可申请'
      }
    },
    allClick () {
      let allFlag = this.allCheck
      let a = this.pageParam
      for (let r = 0; r < a.length; r++) {
        let it = a[r]
        if (!allFlag) {
          this.accSelEvent(it, 'open')
        } else {
          this.accSelEvent(it, 'close')
        }
      }
    },
    accSelEvent (it, type) {
      if (['1', '2'].includes(it.compactPostponeStatus)) {
        return
      }
      if (type === 'close') {
        if (this.selectedAccounts.indexOf(it) != -1) {
          this.selectedAccounts.remove(it)
        }
      } else if (type === 'open') {
        if (this.selectedAccounts.indexOf(it) == -1) {
          this.selectedAccounts.push(it)
        }
      } else {
        if (this.selectedAccounts.indexOf(it) != -1) {
          this.selectedAccounts.remove(it)
        } else {
          this.selectedAccounts.push(it)
        }
      }
      // 是否全选
      if (this.selectedAccounts.length == this.pageParam.length) {
        this.allCheck = true
      } else {
        // 是否全选
        this.allCheck = false
      }
    },
    /** 提交前检测 */
    checkSubmit () {
      if (this.selectedAccounts.length == 0) {
        _hvueAlert({
          mes: '您还未勾选',
          title: '提示'
        })
        return
      }

      _hvueConfirm({
        mes: '申请展期时，需同时归还该笔合约应付利息，若可用资金不足以支付应付利息，则不足部分的利息自动转为其他负债（其他负债为一笔新合约），并与展期合约共同计收利息，合约展期操作正常进行。',
        title: '注意',
        opts: [{
          txt: '取消',
          color: false
        },
        {
          txt: '确定',
          color: true,
          callback: () => {
            // 确定之后的回调
            this.$parent.emitNextEvent()
          }
        }
        ]
      })
      // this.showAlert = true
    },
    doCheck () {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        // 可以下一步
        resolve()
      })
    },
    /**
             * 请求参数打包
             */
    reqParamPackage () {
      // 业务请求参数封装
      let accountList = this.selectedAccounts
      let accountType = ''
      accountList.forEach(a => {
        accountType += ',' + a.compactId
      })
      let params = {
        compactIdStr: accountType.substr(1)
      }
      return params
    },
    putFormData () {
      let formData = {}
      return formData
    },
    // 调用父组件的下一步事件
    nextStep () {
      _hvueAlert({
        mes: '申请展期时，需同时归还该笔合约应付利息，若可用资金不足以支付应付利息，则不足部分的利息自动转为其他负债（其他负债为一笔新合约），并与展期合约共同计收利息，合约展期操作正常进行。',
        title: '注意',
        callback: () => {
          // 确定之后的回调
          this.$parent.emitNextEvent()
        }
      })
    },
    pageBack () {
      if (this.$parent.pageBack) {
        this.$parent.pageBack()
      } else {
        this.$parent.back()
      }
    }
  },
  destroyed () {
    this.$refs.bscroll && this.$refs.bscroll.destroy()
  }
}
</script>

<style scoped>
    .bottom_check {
        position: fixed;
        bottom: 0;
        border-top: 0.05rem solid #f9f9f9;
        z-index: 99999;
        background: white;
        width: 100%;
    }

    .extension_list {
        position: absolute;
        left: 0;
        top: 0;
        overflow: hidden;
    }

    .ext_cont_scroll {
        overflow: hidden;
    }
</style>
