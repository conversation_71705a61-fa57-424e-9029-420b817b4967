<!-- 北交所-选择开通交易权限类型 -->
<template>
  <div>
    <div class="level_title">
      <h5>请选择您需要开通的北交所交易权限</h5>
      <p class="imp" v-show="kcbFlag != 1">您的前20个交易日日均资产：{{assets}} 万元</p>
    </div>
    <ul class="select_levellist">
      <li>
        <div class="tit" @click.stop.prevent="titClick('50',8)">
          <h5>北交所交易权限</h5>
          <span
            :class="{state:classType['8']>0,error:classType['8']>1,icon_check:classType['8']==0,checked : selectedType==8}"
          >{{getState('50',8)}}</span>
        </div>
        <div v-show="kcbFlag != 1" class="cont">
          <p>可交易精选层股票</p>
          <p>日均资产50万元以上（前20个交易日）</p>
        </div>
      </li>
    </ul>
  </div>
</template>
<script>
export default {
  props: ['pageParam'],
  data () {
    return {
      classType: { '8': 0 }, // 分别为北交所权限办理情况，0可以办理，1当前权限，2不满足
      assets: 0,
      openedType: 0, // 当前权限，3  4  5 分别对应一类 二类 三类
      selectedType: 0, // 选择的权限类型，3  4  5 分别对应一类 二类 三类
      changeFlag: '0', // 是否是调整新三板权限
      allowNext: true, // 是否允许下一步
      kcbFlag: 0 // 科创板是否开通
    }
  },
  activated () {
    this.classType = { '8': 0 }
    this.assets = 0
    this.openedType = 0
    this.selectedType = 0
    this.changeFlag = '0'
    this.allowNext = true

    if (this.pageParam.length > 0) {
      this.openedType = this.pageParam[0].openType
      this.assets = parseFloat(this.pageParam[0].assets).toFixed(2)
      this.kcbFlag = this.pageParam[0].kcbFlag || 0
      if ((this.assets < 50 && this.kcbFlag == 0) || this.openedType === '8') {
        this.classType['8'] = 2
        // 都不滿足 放弃办理
        // 更新下一步按钮文字
        this.$store.commit('updateNextBtnText', '放弃办理')
        this.allowNext = false
      }
      if (this.openedType != '') {
        this.classType[this.openedType] = 1
        this.changeFlag = '1'
      }
    }
  },
  methods: {
    getState (ass, type) {
      if (type == this.openedType) {
        return '当前权限'
      }
      if (parseFloat(ass) > parseFloat(this.assets) && this.kcbFlag == 0) {
        return '不满足'
      }
      return ''
    },
    titClick (ass, type) {
      if (parseFloat(ass) > parseFloat(this.assets) && this.kcbFlag == 0) {
        return
      }
      if (type == this.openedType) {
        return
      }
      this.selectedType = this.selectedType == type ? 0 : type
    },
    checkSubmit () {
      if (this.selectedType == 0 && this.allowNext) {
        _hvueToast({
          mes: '请选择交易权限'
        })
        return false
      } else if (this.selectedType == 0 && !this.allowNext) {
        // 返回首页
        this.$router.push({ name: 'index' })
        return false
      }
      if (this.selectedType == this.openedType) {
        _hvueToast({
          mes: '当前选择的权限已开通，请勿重新申请'
        })
        return false
      }
      return true
    },
    doCheck () {
      // 执行下一步前的校验
        return new Promise((resolve, reject) => {
        if (this.checkSubmit()) {
            // 可以下一步
            resolve()
        }
      })
    },
    /**
     * 请求参数打包
     */
    reqParamPackage () {
      let params = {
        oldOpenType: this.openedType,
        changeFlag: this.changeFlag,
        newOpenType: this.selectedType
      } // 提交需要的参数
      return params
    },
    putFormData () {
      let formData = {
        qualityInspectionInfo: {
          transFlag: this.pageParam[0].transFlag,
          oldOpenType: this.openedType,
          changeFlag: this.changeFlag,
          newOpenType: this.selectedType,
          asset_ten: this.assets
        }
      } // 需要保存的表单数据
      return formData
    }
  }
}
</script>
