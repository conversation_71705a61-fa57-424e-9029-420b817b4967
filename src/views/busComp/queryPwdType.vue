<template>
  <div class="nav_list">
    <ul>
      <li v-for="(it,index) in typeList" :key="index" @click.stop="nextStep(it.type)">
        <i class="icon">
          <img src="@/assets/images/nav/nav_xgmm.png" />
        </i>
        <h5>{{it.name}}</h5>
        <p>{{it.info}}</p>
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  props: ["pageParam"],
  data() {
    return {
      passwordType: "",
      typeList: [
        {
          type: "1",
          name: "仅修改资金密码",
          info: "资金密码用于银证转账校验"
        },
        {
          type: "2",
          name: "仅修改交易密码",
          info: "交易密码用于交易登录"
        },
        {
          type: "1,2",
          name: "同时修改交易密码、资金密码",
          info: "可同时修改两个密码"
        }
      ]
    };
  },
  activated() {
    this.$store.commit("updateBusinessNextBtnStatus", false); // 隐藏下一步按钮
  },
  methods: {
    nextStep(type) {
      this.passwordType = type;
      this.$parent.emitNextEvent();
    },
    doCheck() {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        // 可以下一步
        resolve();
      });
    },
    /**
     * 请求参数打包
     */
    reqParamPackage() {
      // 业务请求参数封装
      let params = {
        passwordType: this.passwordType
      }; // 提交需要的参数
      return params;
    },
    putFormData() {
      let formData = {}; // 需要保存的表单数据
      return formData;
    }
  }
};
</script>
