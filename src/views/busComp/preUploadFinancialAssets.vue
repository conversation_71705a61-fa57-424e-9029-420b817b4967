<template>
  <div>
    <article class="content" v-show="!isShow">
      <div class="assets_rzbox" v-html="fillContent.content">
      </div>
      <div class="file_info_module">
        <h5 class="title">图片</h5>
        <ul class="file_upload_list">
          <li :key="index" v-for="(item, index) in imgArr">
            <div class="pic"><img :src="item.imgSrc"></div>
            <a class="delete" href="javascript:void(0)" @click.prevent="deleteImg(index)"></a>
          </li>
          <li @click="selImgClick('')" v-show="imgArr.length < pictrueAmount">
            <a class="add_btn" href="javascript:void(0)"></a>
          </li>
        </ul>
        <p class="tips">注：{{fillContent.name}}图片仅用于大同证券理财交易，最多可以上传 {{ pictrueAmount }} 张图片</p>
      </div>
      <div class="file_info_module">
        <h5 class="title">材料示例图</h5>
        <ul class="file_example_list">
          <li :key="index" v-for="(item, index) in exampleArr" >
            <div class="pic"><img :src="item.imgSrc" @click.prevent="showImg(item)"></div>
          </li>
        </ul>
      </div>
    </article>
    <getImgBoxBrowser
        ref="getImgBoxBrowser"
        @getImgCallBack="getImgCallBack"
    ></getImgBoxBrowser>
    <show-image :is-show="isShow" :image-src="activeImgSrc" :count="activeImgIdx" :length="exampleArr.length" @change="isShowImage"></show-image>
  </div>
</template>
<script>
import getImgBoxBrowser from '@/components/getImg_browser';
import { uploadImg } from '@/service/comServiceNew'
import showImage from '../common/showImage' // 展示图片
export default {
  components: {
    getImgBoxBrowser,
    showImage,
  },
  props: ['pageParam'],
  data () {
    return {
      imgArr: [], // 图片列表
      exampleArr: [
        {
          id: 1,
          imgSrc: (process.env.NODE_ENV == 'development' ? '' : ROUTER_BASE) +
              '/static/images' + '/cunkuan.jpg',
        },
      ], // 示例列表
      serivalId: this.$parent.publicParam.serivalId,
      flow_current_step_name: this.$parent.flow.currentStepInfo.flow_current_step_name,
      fillContent: {
        name: '资产证明',
        mediaCode: '232',
        content:
            `<h5 class="title">金融资产证明</h5>
            <p>请提供您个人在所有金融机构拥有的金融资产超过300万元的以下任意一种证明材料：</p>
            <dl class="info">
            <dd>1.证券资产：如证券公司营业部打印并盖章的资产证明。</dd>
            <dd>2.银行存款：银行流水或存款证明。</dd>
            <dd>3.债券：加盖银行或证券公司专用章的国债、企业 债、公司债、可转债等证明。</dd>
            <dd>4.基金：对账单、份额确认书、合同。</dd>
            <dd>5.资管计划：对账单、份额确认书、合同。</dd>
            <dd>6.银行理财：对账单、理财产品证明。</dd>
            <dd>7.信托计划：对账单、份额确认书、合同。</dd>
            <dd>8.保险产品：对账单、合同。</dd>
            <dd>9.期货权益：期货结算单。</dd>
            </dl>`,
      },
      isShow: false, // 是否展示图片详情
      activeImgIdx: -1, // 当前图片序号
      activeImgSrc: '', // 当前图片地址
      ygtUserInfo: $h.getSession('ygtUserInfo', { decrypt: false }),
      pictrueAmount: ($h.getSession('pictrueAmount') || 9)
    };
  },
  created () {
    window.imgCallBack = this.getImgCallBack;
  },
  activated () {

  },
  methods: {
    doCheck () {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        if (this.imgArr.length) {
            // 可以下一步
            resolve();
        } else {
          _hvueToast({ mes: '请上传证明' })
        }
      });
    },
    selImgClick (imgType) {
      this.imgType = imgType;
      this.$refs.getImgBoxBrowser.getImg();
    },
    getImgCallBack (imgInfo) {
      // h5本地上传base64到bus
      imgInfo.base64 = this.filterBase64Pre(
          imgInfo.base64
      )
      let param = {
        mediaCode: this.fillContent.mediaCode,
        isOcr: '0',
        file_data: encodeURIComponent(imgInfo.base64),
        serivalId: this.serivalId,
        businessCode: this.$route.query.type,
        flow_current_step: this.flow_current_step_name,
      }
      uploadImg(param)
          .then((data) => {
            if (data.error_no === '0') {
              let uploadInfo = data.uploadInfo[0];
              let obj = {
                imgSrc: 'data:image/jpeg;base64,' + imgInfo.base64,
                uploadInfo: uploadInfo,
              }
              this.imgArr.push(obj);
            } else {
              return Promise.reject(new Error(data.error_info))
            }
          })
          .catch((e) => {
            _hvueToast({ mes: e.message })
          })
    },
    // 删除图片
    deleteImg (i) {
      this.imgArr.splice(i, 1);
    },
    // 展示案例图
    showImg (item) {
      this.activeImgIdx = item.id;
      this.activeImgSrc = item.imgSrc;
      this.isShow = true;
    },
    isShowImage (isShow) {
      this.isShow = isShow;
    },
    filterBase64Pre (ndata) {
      let arr = ndata.split('base64,')
      return arr[arr.length - 1]
    },
    /**
     * 请求参数打包
     */
    reqParamPackage () {
      // 业务请求参数封装
      let params = {};
      return params;
    },
    putFormData () {
      let uploadInfo = this.imgArr.map(item => item.uploadInfo);
      let formData = {
        uploadFinancialAssets: uploadInfo,
      };
      return formData;
    },
  },
};
</script>
