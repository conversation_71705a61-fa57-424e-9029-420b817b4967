<!-- 业务入口页面，每个业务都跳转此页面，根据每一步返回的结果集名称加载对应的原子组件
    必须携带的参数：type:业务名称 例如：fxcp business.vue?type=fxcp  路由跳转使用query传入参数
    作用：
            1、查询表单状态
            2、初始化Flow对象。执行Flow对象的构造方法，初始化流程数据。
            初始化后，flow对象属性：userId flowId flowName数据已初始化完成。

            3、加载流程loadFlow
               加载完成后，当前业务流程步骤信息、业务流水编号serivalId数据已初始化
            4、执行步骤的拦截器事件
            5、加载当前步骤所需的原子组件
 -->
<template>
  <div class="page_main" v-cloak>
    <!--公用业务标题
        若不显示，调用 this.$store.commit("updateIsShowHead", false); 隐藏
    -->
    <headComponent :headerTitle="stepName" v-show="isShowHead"></headComponent>
    <article class="content">
      <!--根据当前init数据结果集名称加载对应的页面流子组件-->
      <keep-alive v-for="(atom, atomIndex) in componentList" :key="atomIndex">
        <component :is="atom.path" :ref="atom.name"  :pageParam="initData[atomIndex]"></component>
      </keep-alive>
      <!--
        下一步按钮：
             1、业务公用的下一步按钮
             2、若不需要，在子组件的created钩子函数中,执行：this.$store.commit("updateBusinessNextBtnStatus", false); // 隐藏下一步按钮
      -->
      <div class="ce_btn mt20" v-show="parentBtnShow">
        <!--点击此按钮 触发所有子组件的putFormData,doSubmit事件-->
        <a
          v-throttle
          class="ui button block rounded"
          :class="{ disabled: nextBtnDisabled, border: nextBtnCss }"
          @click.stop.prevent="emitNextEvent"
          >{{ nextBtnText }}</a
        >
      </div>
    </article>
  </div>
</template>
<script>
import Flow from '@/flow/flowNew'
import { queryUserFlowStatus, closeFlow, queryNewUserFlowStatus } from '@/service/comServiceNew'
import { closeYgt } from '@/common/sso'
import { queryBusiness } from '@/common/util'
import headComponent from '@/components/headComponent' // 头部
import Vue from 'vue'
import {clearFormData} from '../service/comServiceNew'
export default {
  inject: ['reload'],
  components: { headComponent },
  name: 'business',
  data() {
    return {
      currentPage: 'loading', // 默认加载loading,等待流程初始化完成
      flowName: this.$route.query.type, // 业务类型
      flow: null, // 流程对象
      isBreakPoint: '0', // 根据业务配置是否有断点来判断是否结束流程isBreakPoint是否支持断点   1:支持   0：不支持
      businessName: '', // 业务名称

      stepName: '', // 步骤名称 -- 头部标题 取的pageFlow每一步配置的描述description字段
      stepsInfo: [], // 业务所有流程的步骤信息
      initData: [], // 组件初始化数据
      formData: {}, // 需要提交的表单数据
      componentNames: '', // 当前组件名称数组
      componentList: [],
      returnIgnoreStep: ['popInformation', 'noticePopInformation', 'queryRiskLevel'], // 返回时要跳过的步骤
      returnIgnoreComponents: [ // 返回时要跳过的步骤包含的组件
        'preconditions', 'accessConditionsInspection', 'popInformationMessage', 'question'
      ],
      preSubmitResult: '', // 上一步的提交结果 用于下一步的页面需要展示上一步提交的结果时用

      nextBtnDisable: false, // 下一步按钮是否可点击  默认不可点击 初始化成功后再允许点击

      ygtUserInfo: $h.getSession('ygtUserInfo', { decrypt: false }) || {}, // 用户信息
      loginType: '',

      // !!!公共入参在此处定义，禁止子组件修改
      publicParam: {
        userId: $h.getSession('ygtUserInfo', { decrypt: false })
          ? $h.getSession('ygtUserInfo', { decrypt: false }).userId
          : '',
        clientId: $h.getSession('ygtUserInfo', { decrypt: false })
          ? $h.getSession('ygtUserInfo', { decrypt: false }).clientId
          : '',
        fundAccount: $h.getSession('ygtUserInfo', { decrypt: false })
          ? $h.getSession('ygtUserInfo', { decrypt: false }).fundAccount
          : '',
        name: $h.getSession('ygtUserInfo', { decrypt: false })
          ? $h.getSession('ygtUserInfo', { decrypt: false }).name
          : '',
        branchNo: $h.getSession('ygtUserInfo', { decrypt: false })
          ? $h.getSession('ygtUserInfo', { decrypt: false }).branchNo
          : '',
        businessCode: this.$route.query.type, // 业务类型
        serivalId: '', // 流程初始化后赋值
      },
      historyStepInfo: [], // 历史pageFlow步骤记录，用于返回到指定的步骤
      historyComponentInfo: [], // 保存组件历史数据
      keyWords: []
    }
  },
  computed: {
    // 父组件的下一步按钮是否显示，保存在vuex中
    parentBtnShow() {
      return this.$store.state.businessNextBtnShow
    },
    // 父组件的下一步按钮的文字描述值，保存在vuex中
    nextBtnText() {
      return this.$store.state.nextBtnText
    },
    nextBtnDisabled() {
      return this.$store.state.nextBtnDisabled
    },
    nextBtnCss() {
      return this.$store.state.nextBtnCss
    },
    isShowHead() {
      return this.$store.state.isShowHead
    },
  },
  watch: {
    // 检测路由变化，当前业务类型发生改变时，强制刷新当前页面
    $route: {
      handler(route, oldVal) {
        if (route.query.type !== this.flowName && route.name === 'business') {
          this.flowName = route.query.type
          this.$store.commit('updateCachePath', [])
          this.reload() // 强制刷新当前页面 provide 与inject配合实现
        }
      },
    },
  },
  created() {
    this.loginType =
      this.$route.query.loginType || $h.getSession('loginType') || '1'
    $h.setSession('loginType', this.loginType)
    if (this.loginType == '2') {
      this.publicParam.fundAccount = this.ygtUserInfo.fundAccountXy || ''
    }
    queryBusiness(this.$route.query.type, (data) => {
      this.isBreakPoint = data.isBreakPoint
      this.businessName = data.businessName
    })
    // 监控下一步的初始化事件 -- 原子组件调用完下一步后，必须提交此方法初始化下一步。
    this.$on('init', (params) => {
      this.$store.commit('updateIsShowHead', true) // 初始化展示头部 ，需要隐藏时在子组件处理
      let stepInfo = this.flow.currentStepInfo
      let currentView = stepInfo.flow_current_step_view // 当前需要展示的组件
      let currentStepName = stepInfo.flow_current_step_name // 当前步骤名称
      let isFinished = stepInfo.flow_finish // 流程是否已经结束
      let isShow = stepInfo.flow_current_step_show // 当前步骤是否显示
      let isAutoSubmit = stepInfo.flow_current_step_auto_submit // 是否自动提交
      // 如果需要展示，则需要初始化
      if (isShow === '1') {
        // 记录步骤信息到历史步骤中。
        for (const iterator of this.stepsInfo) {
          if (iterator.step_name === currentStepName) {
            this.stepName = iterator.description
            break
          }
        }
        this.historyStepInfo.push(currentStepName)
        this.historyStepInfo = Array.from(new Set(this.historyStepInfo)) // 去重
        this.init(params)
        if (isAutoSubmit === '1') {
          this.next()
        }
      } else {
        // 不需要展示，直接提交事件。
        let initParam = this.$route.params || {}
        this.next(initParam).then((res) => {
          let stepInfo = this.flow.currentStepInfo
          let isFinished = stepInfo.flow_finish // 流程是否已经结束
          let isShow = stepInfo.flow_current_step_show // 当前步骤是否显示
          // 流程已经结束，不需要展示结果页面，若不需要则直接弹框提示
          // 不显示，直接弹框提示
          if (isFinished === '1' && isShow === '0') {
            _hvueToast({
              mes: '提交成功',
              icon: 'success',
              timeout: 1500,
              callback: () => {
                this.$router.push({ name: 'index', params: {} })
              },
            })
            return
          }
          this.$emit('init', params)
        })
      }
    })
    // 监控表单数据更新
    this.$on('putFormData', (params) => {
      this.putFormData(params)
    })
  },
  mounted() {
    window.phoneBackBtnCallBack = this.back
    this.initFlow() // 初始化业务流程对象。
  },
  methods: {
    // 初始化流程对象
    async initFlow (rejectFlag) {
      let options = {
        userId: $h.getSession('ygtUserInfo', { decrypt: false })
          ? $h.getSession('ygtUserInfo', { decrypt: false }).userId
          : '',
        clientId: $h.getSession('ygtUserInfo', { decrypt: false })
          ? $h.getSession('ygtUserInfo', { decrypt: false }).clientId
          : '',
        fundAccount: $h.getSession('ygtUserInfo', { decrypt: false })
          ? $h.getSession('ygtUserInfo', { decrypt: false }).fundAccount
          : '',
        flowName: this.flowName, // 业务名称/流程名称
        _this: this,
        formStatus: '',
        flowId: '',
        serivalId: '', // 业务流水ID
      }
      if (this.loginType == '2') {
        options.fundAccount = this.ygtUserInfo.fundAccountXy || ''
      }
      /**
       * 重置密码业务不查询在途业务
       */
      if (this.flowName === 'czmm' || this.flowName === 'zhzh') {
        this.afterInitFlow(options)
        return
      }

      let formStatusResult;
      if ((this.flowName !== 'dzjy' && this.flowName !== 'gspt')) {
        // 1、查询表单状态
        formStatusResult = await queryUserFlowStatus({
          userId: options.userId,
          businessCode: options.flowName,
        })
      } else {
        // 1、查询表单状态
        formStatusResult = await queryNewUserFlowStatus({
          userId: options.userId,
          businessCode: options.flowName,
          resultFlag: rejectFlag,
          clientId: options.clientId
        })
      }

      if (formStatusResult.error_no === '0') {
        let formStatus = formStatusResult.results[0].formStatus
        // 表单状态 0:表单未提交完(暂存)  1:表单已提交完成  2:表单已处理  3:结束  4:驳回  8:审核  9:待跑批(审核通过)
        // if (formStatus === '9' ||  formStatus === '4' || formStatus === '8') {
        //   // 跳转结果页，查询业务结果
        //   this.dealBusinessResult();
        // }
        options.formStatus = formStatusResult.results[0].formStatus || ''
        options.flowId = formStatusResult.results[0].flowId || ''
        options.serivalId = this.publicParam.serivalId =
          formStatusResult.results[0].serivalId

        console.log(
          '111flowId' +
            options.flowId +
            'serivalId' +
            this.publicParam.serivalId
        )
        // 根据业务配置是否有断点来判断是否结束流程isBreakPoint是否支持断点   1:支持   0：不支持
        if (this.isBreakPoint === '1') {
          if (options.flowId && formStatus === '0') {
            _hvueConfirm({
              mes: '检测您有未完成操作，是否继续上次操作？',
              opts: [
                {
                  txt: '取消',
                  color: false,
                  callback: () => {
                    // 结束原流水
                    this.closeOldFlow(options)
                  },
                },
                {
                  txt: '确定',
                  color: true,
                  callback: () => {
                    this.afterInitFlow(options)
                  },
                },
              ],
            })
          } else {
            this.afterInitFlow(options)
          }
        } else {
          if (options.flowId && formStatus === '0') {
            // 结束原流水
            this.closeOldFlow(options)
          } else {
            // if (options.flowName === 'dzjy' || options.flowName === 'gspt') {
            //   options.flowId = '';
            // }
            this.afterInitFlow(options)
          }
        }
      } else {
        _hvueAlert({ mes: formStatusResult.error_info })
      }
    },
    async afterInitFlow(options) {
      // 2、创建流程实例对象
      this.flow = new Flow(options)
      // 3、加载流程
      await this.flow.loadFlow()
      if (this.flow.currentStepInfo.flow_finish === '1' && options.flowId) {
        // 已经执行完成 且初始化时已经有flowId,则将原flowId的流程驳回到success,再重新加载。
        await this.flow.rejectFlow('success')
      }
      // 4、执行拦截器事件
      await this.flow.interceptorEvent()
      // 5、查询流程全部步骤信息
      this.stepsInfo = await this.flow.queryFlowInfo()
      // 6、执行初始化事件
      this.$emit('init')
    },
    /**
     * 初始化当前步骤
     */
    init(params) {
      // 先判断是否需要显示
      let paramMap = params || {}
      this.formData = {} // 初始化时清空formData数据
      // 执行当前步骤初始化方法
      const currentStepName = this.flow.currentStepInfo.flow_current_step_name
      paramMap = Object.assign({}, this.publicParam, paramMap)
      if (currentStepName) {
        paramMap.flow_current_step_name = currentStepName
      }
      this.flow
        .init(paramMap)
        .then((res) => {
          this.initData = res.componentResults // 这里才是正式的代码
          // 返回步骤初始化的数据
          // let componentArray = res.componentName // 接口返回的组件
          // 将keyWords数据传输组件剔除，并将其数据储存起来供协议列表agreementList使用
          let componentArray = res.componentName.filter((item) => {
            return item !== 'keyWords';
          });

          this.componentList = componentArray.map(item => {
            return {
              path: () => import(`../views/busComp/${item}.vue`),
              name: item
            }
          })
          // for (let index = 0; index < componentArray.length; index++) {
          //   const element = componentArray[index]
          //   let component = () => import(`../views/busComp/${element}.vue`)
          //   componentNameArray.push(component)
          // }
          this.historyComponentInfo.push(this.componentList) // 保存当前组件到已经加载的步骤信息里面去。返回时直接显示即可，动态切换
          // this.componentNames = componentNameArray

          // 接口返回的组件
          let keyWordsIndex = res.componentName.indexOf('keyWords');
          if (keyWordsIndex >= 0) {
            this.keywords = res.componentResults.splice(keyWordsIndex, 1) || [];
          }

          // 更新下一步按钮文字
          this.$store.commit('updateNextBtnText', '下一步')
          // 显示下一步按钮 初始化成功后再显示下一步按钮
          this.$store.commit('updateBusinessNextBtnStatus', true)
          this.$store.commit('updateNextBtnCss', false)

          // 初始化成功后，再允许用户点击下一步按钮
          this.nextBtnDisable = true
        })
        .catch((errorInfo) => {
          // 初始化报错，隐藏下一步按钮 不能让用户点击 重新加载流程
          _hvueAlert({
            title: '错误提示',
            mes: errorInfo,
            callback: () => {
              // 初始化报错，流程无法继续往下走 初始化报错的场景不应该发生 一般都是查询数据，接口处理。此处直接返回首页
              // 直接后退
              this.$router.go(-1)
            },
          })
        })
    },
    // dealBusinessResult(){
    //   // 调用接口查询业务办理结果

    // },
    /**
     * 存放表单数据
     */
    putFormData(params) {
      this.formData = Object.assign(this.formData, params)
    },
    next(params, options) {
      let paramMap = params || {}
      // 表单数据处理
      paramMap.formData = JSON.stringify([this.formData])
      let reqParam = Object.assign({}, this.publicParam, paramMap)
      return this.flow.next(reqParam, options) // 执行下一步 此处需要return 当下一步需要上一步放回的结果作为执行初始化方法的入参时，如果不返回提交结果，则无法处理下一步。
    },
    async back(params, backStep) {
      // 如果是从交易跳转过来的，返回到交易
      let source = this.$route.query.source
      if (source && source === 'trade') {
        closeYgt(0, '1A', 0, 1, this.$router)
        return
      }
      // 当在第一步和驳回重走流程时返回首页
      let inputBackStep = backStep || ''
      if (inputBackStep) {
        let res = await this.flow.back(inputBackStep)
        if (res.error_no === '0') {
          // 需要重新加载流程
          await this.flow.loadFlow()
          // 重新初始化当前步骤
          this.$emit('init')
        } else {
          _hvueAlert({ mes: res.error_info })
        }
        return
      }
      console.log('this.flow.currentStepInfo:' + this.flow.currentStepInfo)
      if (
        this.historyStepInfo.length < 2 ||
        this.flow.currentStepInfo.flow_reject === '1' ||
        this.flow.currentStepInfo.flow_finish === '1'
      ) {
        // 直接后退
        this.$router.go(-1)
        return
      }
      let backStepName = this.historyStepInfo[this.historyStepInfo.length - 2]

      // 专业投资者认定上传返回需要清除表单数据
      if (
          this.flow.currentStepInfo.flow_name === 'zytzzrd' &&
          ['preUploadFamilyAssets', 'preUploadAssets', 'knowledge', 'satisfyKnowledgeQuesResult '].includes(backStepName)
      ) {
        const res = await clearFormData({
          stepName: backStepName,
          serivalId: this.publicParam.serivalId
        })
        if (res.error_no === '0') {
          this.rollback(backStepName)
        } else {
          _hvueAlert({ mes: res.error_info })
        }
        } else {
        this.rollback(backStepName)
      }
    },
    /**
     * 驳回流程
     */
    async rejectFlow(rejectName) {
      if (rejectName) {
        this.flow.rejectFlow(rejectName).then((res) => {
          if (res.error_no === '0') {
            this.initFlow('1')
          }
        })
      }
    },
    async rollback(backStepName) {
      let res = await this.flow.back(backStepName)
      if (res.error_no === '0') {
        // 回退成功
        this.historyStepInfo.pop() // 删掉最后一个步骤信息
        // 切换组件
        // this.componentNames = this.historyComponentInfo[
        //   this.historyComponentInfo.length - 2
        // ]
        this.historyComponentInfo.splice(-2, 2) // 删除组件
        // 需要重新加载流程
        await this.flow.loadFlow()
        // 重新初始化当前步骤
        this.$emit('init')
      } else {
        _hvueAlert({ mes: res.error_info })
      }
    },
    // 执行子组件的 putFormData,reqParamPackage事件 存放表单数据与请求参数
    async emitNextEvent() {
      if (!this.nextBtnDisable) {
        return
      }

      // 控制按钮此时无法点击
      /* TODO 怀疑导致表单丢失问题,暂时注释 let childrens = this.$children.filter(
        element => { return (element._inactive === false || element.isActived) }
      ) */
      const userRef = this.$refs
      let childrens = this.componentList.map(item => {
        return (userRef[item.name] && userRef[item.name][0]) || ''
      })

      // 控制按钮此时无法点击
      this.nextBtnDisable = true
      // var childrens = this.$children.filter(
      //   (element) => element._inactive === false
      // )

      var reqParams = {} // 请求参数
      for (let index = 0; index < childrens.length; index++) {
        if (childrens[index].doCheck) {
          await childrens[index].doCheck() // 执行子组件的校验事件
        }
        if (childrens[index].reqParamPackage) {
          let childReqParam = childrens[index].reqParamPackage()
          // 合并子组件的请求参数
          Object.assign(reqParams, childReqParam)
        }
        if (childrens[index].putFormData) {
          // 合并子组件的表单数据
          this.formData = Object.assign(
            this.formData,
            childrens[index].putFormData()
          )
        }
      }
      // 继续执行下一步
      const currentStepName = this.flow.currentStepInfo.flow_current_step_name
      if (currentStepName) {
        reqParams.flow_current_step_name = currentStepName
      }
      this.next(reqParams).then((res) => {
        // 判断是否返回了表单结果，是的话将接口返回的serivalId和flowId同步到前端参数中
        if (res.dsName.indexOf('formData') > -1) {
          let formData = res['formData'][0]
          this.publicParam.serivalId = formData.serivalId
          this.publicParam.flowId = formData.flowId
          console.log(
            '222--flowId' +
              this.publicParam.flowId +
              '-serivalId-' +
              this.publicParam.serivalId
          )
        }
        // 判断是否需要对提交的结果进行处理 如果需要 需要在子组件中触发init事件。
        for (let index = 0; index < childrens.length; index++) {
          if (childrens[index].handleResult) {
            // 需要对结果集进行处理
            childrens[index].handleResultFunc(res)
            // 隐藏加载框
            _hvueLoading.closeLoading()
            return
          }
        }
        this.preSubmitResult = res['DataSet'][0]
        this.$emit('init')
      })
    },
    // 关闭原流程
    async closeOldFlow(options) {
      let closeResults = await closeFlow({
        userId: options.userId,
        businessCode: options.flowName,
        serivalId: options.serivalId,
      })
      if (closeResults.error_no === '0') {
        // 重新加载流程
        this.initFlow()
      } else {
        _hvueAlert({
          title: '提示',
          mes: closeResults.error_info,
          callback: () => {
            // 直接后退
            this.$router.go(-1)
          },
        })
      }
    },
  },
  destroyed() {
    this.$store.commit('updateNextBtnText', '下一步')
    // 退出后隐藏下一步按钮
    this.$store.commit('updateBusinessNextBtnStatus', false)
  },
}
</script>
