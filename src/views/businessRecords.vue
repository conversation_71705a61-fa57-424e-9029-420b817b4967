<template>
  <div class="page_main" :class="className">
    <headComponent headerTitle="办理记录" isWhite="true"></headComponent>
    <div class="tab_nav">
      <ul>
        <li :class="{active : showIng==1}">
          <a @click.stop="queryBusinessRecords(1)">
            <span>全部</span>
          </a>
        </li>
        <li :class="{active : showIng==0}">
          <a @click.stop="queryBusinessRecords(0)">
            <span>进行中</span>
          </a>
        </li>
      </ul>
    </div>
    <article class="content">
      <div class="bus_record">
        <scroll
          :height="scrollheight"
          :pullupActive="!noMoreData"
          @pullingDown="pullDownFun"
          @pullingUp="pullupFun"
          ref="scrollBusniess"
          :tiptxt="tiptext"
        >
          <div class="nav_list">
            <ul>
              <li
                v-for="(item,index) in list"
                :key="index"
                v-show="(showIng==0&&item.formStatus==0)||showIng==1?true:false"
              >
                <i class="icon">
                  <img :src="item.mobileImgUrl" />
                </i>
                <h5>{{item.businessName}}</h5>
                <p>{{item.date}}</p>
                <span
                  :class="item.formStatus|formatFormStateStyle"
                  class="state"
                >{{item.formStatus|formatFormStateDesc(item.handleStatus)}}</span>
              </li>
            </ul>
          </div>
        </scroll>
      </div>
    </article>
  </div>
</template>

<script>
import headComponent from '@/components/headComponent'
import scroll from '@/components/scroll'

import { queryBusinessRecords } from '@/service/comServiceNew'

export default {
  components: { headComponent, scroll },
  name: 'onTheWay',
  data () {
    return {
      className: 'onTheWay',
      ygtUserInfo: $h.getSession('ygtUserInfo', {decrypt: false}),
      showIng: 1, // 是否展示进行中业务，查询标识   默认查询办理中流水  0:办理中   1：全部业务流水
      list: [], // 业务流水列表
      scrollheight: '100%',
      pulldown: true,
      pullup: true,
      curPage: 1,
      numPerPage: 6,
      totalPages: 0,
      noMoreData: false,
      tiptext: '没有更多内容啦'
    }
  },
  created () {
    this.scrollheight = window.innerHeight - 44
  },
  mounted () {
    window.phoneBackBtnCallBack = this.pageBack
    this.queryBusinessRecords()
  },
  methods: {
    pullDownFun () {
      console.log('down')
      this.curPage = 1
      this.noMoreData = true
      this.queryBusinessRecords()
    },
    pullupFun () {
      console.log('up')
      this.curPage++
      this.queryBusinessRecords()
    },
    // 查询业务流水
    queryBusinessRecords (queryFlag) {
      this.showIng = queryFlag == undefined ? this.showIng : queryFlag
      queryBusinessRecords({
        clientId: this.ygtUserInfo.clientId,
        queryFlag: this.showIng,
        currentPage: this.curPage
      }).then(
        res => {
          console.log(res)
          if (res.error_no === '0') {
            let _results = res.results
              ? res.results[0]
              : res.businessHandlingRecord[0]
            this.totalPages = _results.totalPages
            this.curPage = _results.currentPage
            _results.data = JSON.parse(_results.data)
            _results.data = _results.data.map(function (item) {
              item.date = item.createDate
              // item.date = item.updateDate ? item.updateDate : item.createDate
              item.mobileImgUrl =
                (process.env.NODE_ENV == 'development' ? '' : ROUTER_BASE) +
                item.mobileImgUrl
              return item
            })

            if (_results.currentPage == 1 && _results.data) {
              this.list = _results.data
            } else if (_results.currentPage > 1 && _results.data != '') {
              this.list = this.list.concat(_results.data)
            }
            if (_results.currentPage >= _results.totalPages) {
              this.noMoreData = true
            }
            this.$refs.scrollBusniess.refresh()
          } else {
            this.noMoreData = true
            _hvueToast({
              icon: 'error',
              mes: res.error_info
            })
          }
        },
        err => {
          this.noMoreData = true
          console.log(err)
        }
      )
    },
    pageBack () {
      this.$router.go(-1)
    }
  }
}
</script>
<style>
</style>
