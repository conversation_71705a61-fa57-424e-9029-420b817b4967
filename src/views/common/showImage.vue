<template>
  <section class="main fixed black_bg" data-page="home" v-show="isShow">
    <headComponent :headerTitle="'示例图（'+ count +'/'+ length +'）'"></headComponent>
    <article class="content">
      <div class="file_img_big">
        <img :src="imageSrc">
      </div>
    </article>
  </section>
</template>

<script>
import headComponent from '@/components/headComponent' // 头部
export default {
  components: {
    headComponent,
  },
  name: 'showImage',
  props: {
    isShow: {
      type: Boolean,
      default: false,
    },
    count: {
      type: Number,
      default: 0,
    },
    length: {
      type: Number,
      default: 0,
    },
    imageSrc: {
      type: String,
      default: '',
    },
  },
  data () {
    return {}
  },
  mounted () {
  },
  methods: {
    back () {
      this.$emit('change', false);
    },
  },
}
</script>

<style>
</style>
