<template>
  <div class="page_main">
    <!--头部 -->
    <headComponent :headerTitle="title"></headComponent>
    <article class="content">
      <div class="notice_box">
        <div class="pic">
          <img :src="imageSrc" />
        </div>
        <h5 v-html="message"></h5>
      </div>
      <div class="ce_btn mt20">
        <a class="ui button block rounded" @click.stop="pageBack">返回首页</a>
      </div>
    </article>
  </div>
</template>
<script>
import headComponent from '@/components/headComponent' // 头部
export default {
  components: { headComponent },
  data () {
    return {
      title: '提示',
      message: '',
      imageSrc: require('../../assets/images/not_ic06.png')
    }
  },
  mounted () {
    window.phoneBackBtnCallBack = this.pageBack
    this.title = this.$route.params.title
      ? this.$route.params.title
      : this.title
    this.message = this.$route.params.message
      ? this.$route.params.message
      : this.message
    this.imageSrc = this.$route.params.imageSrc
      ? this.$route.params.imageSrc
      : this.imageSrc
    console.log(this.$route.params)
  },
  methods: {
    // 传递过来的图片地址需要处理后才能正常使用
    pageBack () {
      this.$router.push({ name: 'index' })
    }
  }
}
</script>
