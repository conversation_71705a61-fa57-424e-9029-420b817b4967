<template>
  <div>
    <div v-if="xhList.clientFlag" class="xh_result_info">
      <p class="tit">客户号</p>
      <div class="cont">
        <ul>
          <li
            v-for="(item, index) in pageParam"
            v-show="item.isShow && item.type == '0'"
            :key="index"
          >
            <span>{{ item.views }}</span>
            <em :class="item.dealBusinessResultState | fillStateStyle">{{
              item.dealBusinessResultState | fillStateDesc
            }}</em>
            <div v-if="item.dealBusinessResultState == 2" class="fail_reason">
              <span>失败原因</span>
              <p @click.stop="showErrorInfo(item.dealBusinessDesc)">
                {{ item.dealBusinessDesc.substring(0, 8) }}...
              </p>
            </div>
          </li>
        </ul>
      </div>
    </div>
    <div v-if="xhList.assetFlag" class="xh_result_info">
      <p class="tit">资金账户</p>
      <div class="cont">
        <ul>
          <li
            v-for="(item, index) in pageParam"
            v-show="item.isShow && item.type == '1'"
            :key="index"
          >
            <span>{{ item.views }}</span>
            <em :class="item.dealBusinessResultState | fillStateStyle">{{
              item.dealBusinessResultState | fillStateDesc
            }}</em>
            <div v-if="item.dealBusinessResultState == 2" class="fail_reason">
              <span>失败原因</span>
              <p @click.stop="showErrorInfo(item.dealBusinessDesc)">
                {{ item.dealBusinessDesc.substring(0, 8) }}...
              </p>
            </div>
          </li>
        </ul>
      </div>
    </div>
    <div v-if="xhList.stockFlag" class="xh_result_info">
      <p class="tit">证券帐户</p>
      <div class="cont">
        <ul>
          <li
            v-for="(item, index) in pageParam"
            v-show="item.isShow && item.type == '2'"
            :key="index"
          >
            <span>{{ item.views }}</span>
            <em :class="item.dealBusinessResultState | fillStateStyle">{{
              item.dealBusinessResultState | fillStateDesc
            }}</em>
            <div v-if="item.dealBusinessResultState == 2" class="fail_reason">
              <span>失败原因</span>
              <p @click.stop="showErrorInfo(item.dealBusinessDesc)">
                {{ item.dealBusinessDesc.substring(0, 8) }}...
              </p>
            </div>
          </li>
        </ul>
      </div>
    </div>

    <div v-if="xhList.fundFlag" class="xh_result_info">
      <p class="tit">基金帐户</p>
      <div class="cont">
        <ul>
          <li
            v-for="(item, index) in pageParam"
            v-show="item.isShow && item.type == '3'"
            :key="index"
          >
            <span>{{ item.views }}</span>
            <em :class="item.dealBusinessResultState | fillStateStyle">{{
              item.dealBusinessResultState | fillStateDesc
            }}</em>
            <div v-if="item.dealBusinessResultState == 2" class="fail_reason">
              <span>失败原因</span>
              <p @click.stop="showErrorInfo(item.dealBusinessDesc)">
                {{ item.dealBusinessDesc.substring(0, 8) }}...
              </p>
            </div>
          </li>
        </ul>
      </div>
    </div>

    <div v-if="xhList.otcFlag" class="xh_result_info">
      <p class="tit">OTC帐户</p>
      <div class="cont">
        <ul>
          <li
            v-for="(item, index) in pageParam"
            v-show="item.isShow && item.type == '4'"
            :key="index"
          >
            <span>{{ item.views }}</span>
            <em :class="item.dealBusinessResultState | fillStateStyle">{{
              item.dealBusinessResultState | fillStateDesc
            }}</em>
            <div v-if="item.dealBusinessResultState == 2" class="fail_reason">
              <span>失败原因</span>
              <p @click.stop="showErrorInfo(item.dealBusinessDesc)">
                {{ item.dealBusinessDesc.substring(0, 8) }}...
              </p>
            </div>
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script>
// 销户账户字段说明：
// accountCancellationType  账户类别  "0";//客户号"1";//资金账户"2";//证券账户"3";//基金账户"4";//OTC账户
// account   账户号
// mainFlag  主辅标识
// market    市场
// holderStatus  股东户状态
// accountName  账户名称
// accountCode  账户编号（一般用户基金公司编号）
// zdCancellationFlag 中登销户标识(用户股东户账户属性中)
export default {
  name: 'closeAccountResultList',
  props: {
    pageParam: {
      type: Array
    }
  },
  data() {
    return {
      busiCode: this.$route.query.type,
      xhList: {
        clientFlag: false,
        assetFlag: false,
        stockFlag: false,
        fundFlag: false,
        otcFlag: false
      }
    }
  },
  created() {
    this.xhList.clientFlag = false
    this.xhList.assetFlag = false
    this.xhList.stockFlag = false
    this.xhList.fundFlag = false
    this.xhList.otcFlag = false
    if (this.pageParam.length > 0) {
      let a = this.pageParam
      for (let s = 0; s < a.length; s++) {
        let element = a[s]
        // 销户账户处理relFields，判断账户类型
        if(!element.relFields) return false
        let relFields = JSON.parse(element.relFields)
        element.type = relFields.accountCancellationType
        switch (element.type) {
          case '0':
            this.xhList.clientFlag = true
            break
          case '1':
            this.xhList.assetFlag = true
            break
          case '2':
            this.xhList.stockFlag = true
            break
          case '3':
            this.xhList.fundFlag = true
            break
          case '4':
            this.xhList.otcFlag = true
            break
          default:
            break
        }
        this.$set(this.pageParam, s, element)
      }
    } else {
      _hvueAlert({
        mes: '未查询到办理结果'
      })
    }
  },
  methods: {
    showErrorInfo(info) {
      _hvueAlert({
        title: '失败原因',
        mes: info
      })
    }
  }
}
</script>
