<template>
  <div class="page_main" v-cloak>
    <headComponent headerTitle="协议详情"></headComponent>
    <article class="content">
      <div class="xy_box">
        <div class="title">{{ agreementTitle }}</div>
        <div class="xy_cont" v-html="agreementContent"></div>
      </div>
    </article>
    <div class="bottom_check">
      <p class="tips" v-if="tips != ''">{{ tips }}</p>
      <div class="ce_btn">
        <a v-if="showReadSeconds" class="ui button block rounded disable"
          >请认真阅读以上内容({{ readSeconds }}s)</a
        >
        <a
          v-else
          class="ui button block rounded"
          @click.prevent="confirmSubmit"
          >{{ btnDecs }}</a
        >
      </div>
    </div>
  </div>
</template>
<script>
import headComponent from '@/components/headComponent'
import { getProtocolById } from '@/service/comServiceNew'
export default {
  components: {
    headComponent,
  },
  data() {
    return {
      agreementId: '',
      agreementTitle: '',
      agreementContent: '',
      tips: '',
      // 协议必读的业务和对应秒数配置
      readSecondsArr: {
        lryzx: 10,
        lryykh: 10,
        xsbkt: 20,
        cybkt: 30,
        kzzkt: 15,
        fxjskt: 15,
        tsgpkt: 15,
        jcjjqxkt: 30,
        zghgtzz: 10,
        smhgtzz: 10,
        gszqqxkt: 30,
      },
      readSeconds: 0,
      interval: null,
      btnDecs: '确认阅读并签署',
    }
  },
  created() {
    this.$store.commit('updateIsWhite', true)
    // 查詢协议详情
    this.agreementId = this.$route.query.agreementId
    this.agreementTitle = this.$route.query.agreementTitle
    this.tips = this.$route.query.tips
    let type = this.$route.query.type
    let readOnly = this.$route.query.readOnly
    if (type === 'dzht' || readOnly === '1') {
      // 电子合同业务、和只读不签署的
      this.btnDecs = '返回'
    }
    let hasRead = $h.getSession('agreementIds') || ''
    if (hasRead.indexOf(this.agreementId) === -1) {
      this.readSeconds = this.readSecondsArr[type] || 0
    } else {
      this.readSeconds = 0
    }
    if (this.readSeconds > 0) {
      let me = this
      me.interval = window.setInterval(() => {
        this.readSeconds--
        if (this.readSeconds === 0) {
          window.clearInterval(this.interval)
        }
      }, 1000)
    }
  },
  computed: {
    showReadSeconds() {
      if (this.readSeconds === 0) {
        return false
      } else {
        return true
      }
    },
  },
  mounted() {
    window.phoneBackBtnCallBack = this.pageBack
    getProtocolById({
      agreementId: this.agreementId,
      userId: $h.getSession('ygtUserInfo', { decrypt: false }).userId,
      keyWords: this.$route.query.keyWords,
    }).then((res) => {
      if (res.error_no === '0') {
        let results = res.results || res.DataSet
        this.agreementContent = results[0].agreeContent
      } else {
        _hvueAlert({ mes: res.error_info })
      }
    })
  },
  methods: {
    confirmSubmit() {
      // 标记已经阅读过的协议
      let readAgreements = $h.getSession('agreementIds') || ''
      if (readAgreements.indexOf(this.agreementId) < 0) {
        $h.setSession(
          'agreementIds',
          readAgreements
            ? readAgreements + ',' + this.agreementId
            : this.agreementId
        )
      }
      this.$bus.emit('isReadAgreement', this.$route.query.agreementIndex) // 通过vuebus调用
      this.$router.go(-1)
    },
    pageBack() {
      this.$router.go(-1)
    },
    clearCountDown() {
      if (this.interval) {
        console.log('clearInterval')
        clearInterval(this.interval) || (this.interval = null)
      }
    },
  },
  destroyed() {
    this.clearCountDown()
    this.$store.commit('updateIsWhite', false)
  },
}
</script>
<style scoped>
  .content {
    overflow-x: auto!important;
  }
</style>
