<template>
  <div class="page_main" v-cloak>
    <headComponent headerTitle="资质查询"></headComponent>
    <article class="content">
      <div class="notice_box spel">
        <div class="pic">
          <img src="@/assets/images/not_ic04.png" />
        </div>
        <h5 v-if="bType == 'lryykh'">您的两融权限已开通</h5>
        <p v-else-if="bType == 'jsjyqx'"></p>
        <h5 v-else>您的账户已全部开通</h5>
        <p v-if="bType == 'lryykh'">您已有信用账户，可直接进行两融交易！</p>
        <p v-else-if="bType == 'jsjyqx'">
          抱歉，您不符合此业务办理条件，如有疑问，请联系我司客服
        </p>
        <p v-else>您的账户已全部开通{{ flowNameCn }}</p>
      </div>
      <div class="result_sfinfo">
        <ul>
          <li v-for="(item, index) in accountList" :key="index">
            <span class="tit"
              >{{
                item.market == '10' && item.trdacctExcls == '4'
                  ? '沪基金'
                  : item.market == '00' && item.trdacctExcls == '4'
                  ? '深基金'
                  : item.marketName
              }}
              {{ item.stockAccount }}</span
            >
            <p>
              <span class="status">已开通</span>
            </p>
          </li>
        </ul>
      </div>
      <div class="ce_btn mt20">
        <a class="ui button block rounded" @click.stop="toIndex">返回主页</a>
      </div>
    </article>
  </div>
</template>
<script>
import headComponent from '@/components/headComponent' // 头部
export default {
  components: { headComponent },
  props: ['flowNameCn', 'initData'],
  data() {
    return {
      accountList: [],
      bType: this.$route.query.type,
    }
  },
  created() {
    if (typeof this.initData === 'string') {
      this.accountList = JSON.parse(this.initData)
    } else {
      this.accountList = this.initData.SecAccountList
    }
  },
  mounted() {
    window.phoneBackBtnCallBack = this.pageBack
  },
  methods: {
    toIndex() {
      this.$router.push({ name: 'index', params: {} })
    },
    // 返回
    pageBack() {
      this.$router.push({ name: 'index', params: {} })
      // 调用父类返回
      // this.$parent.pageBack();
    },
  },
}
</script>
