<template>
  <div class="page_main" :class="className">
    <headComponent v-show="!queryShow">
      <div class="header_inner">
        <a class="icon_back" @click.stop="pageBack"></a>
        <h1 class="title text-center">首页</h1>
        <a class="icon_record" @click.stop="pageBusinessRecords"></a>
      </div>
    </headComponent>
    <article class="content" v-show="!queryShow">
      <div class="banner_box">
        <a class="search_btn" @click.stop="queryShow = true">
          <span>搜索</span>
        </a>
        <div class="home_banner">
          <img src="@/assets/images/banner01.png" />
        </div>
      </div>
      <div class="in_transit_tips" v-if="onTheWayShow">
        <p>
          您有
          <span>{{ onTheWayNum }}</span> 项在途业务
        </p>
        <div class="list">
          <a @click.stop="pageBusinessRecords">
            <!-- <img src="@/assets/images/nav/nav_cybzq.png" /> -->
          </a>
          <span class="omit"></span>
        </div>
      </div>
      <div class="home_tabnav">
        <ul>
          <li :class="{ active: loginType == 1 }"><a @click.stop="loginType = 1">普通网厅</a></li>
          <li :class="{ active: loginType == 2 }"><a @click.stop="loginType = 2">信用网厅</a></li>
        </ul>
      </div>
      <div class="home_tabcont">
        <!-- <div v-if="loginType==1 && (ygtUserInfo.fundAccount == null || ygtUserInfo.fundAccount == '')" class="hm_loginbox">
          <h5>办理普通业务请先登录</h5>
          <a class="login_button" @click.stop="toLoginPage">登录</a>
        </div>
        <div v-else-if="loginType==2 && (ygtUserInfo.fundAccountXy == null || ygtUserInfo.fundAccountXy == '')" class="hm_loginbox">
          <h5>办理信用业务请先登录</h5>
          <a class="login_button" @click.stop="toLoginPage">登录</a>
        </div>
        <div v-if="loginType==1 && !!ygtUserInfo.fundAccount" class="login_info_wp">
          <div class="login_info">
            <p><i></i>普通账户：{{ygtUserInfo.fundAccount}}</p>
            <a class="exit_btn" @click.stop="logout(1)">退出登录</a>
          </div>
        </div>
        <div v-else-if="loginType==2 && !!ygtUserInfo.fundAccountXy" class="login_info_wp">
          <div class="login_info">
            <p><i></i>信用账户：{{ygtUserInfo.fundAccountXy}}</p>
            <a class="exit_btn" @click.stop="logout(2)">退出登录</a>
          </div>
        </div>-->
        <div v-show="hotLable.length > 0" class="home_nav">
          <ul>
            <li
              v-if="
                !fromTHSGB ||
                  (fromTHSGB && !ignoreThsgb.includes(item.businessCode)) ||
                  (loginType == 1 && item.businessCode === 'cybkt')
              "
              v-for="(item, index) in hotLable"
              :key="index"
              @click.stop="toBusiness(item)"
              v-show="(loginType == 1 && item.showZq) || (loginType == 2 && item.showXy)"
            >
              <i class="icon">
                <img :src="item.mobileImgUrl" />
              </i>
              <p>{{ item.businessName }}</p>
              <span>立即办理</span>
            </li>
          </ul>
        </div>
        <div class="nav_main">
          <div
            class="nav_list"
            v-for="(item, index) in allBusinessArr"
            :key="index"
            v-show="showList(item)"
          >
            <h5 class="title">{{ item.groupName }}</h5>
            <ul>
              <li
                v-if="
                  !fromTHSGB ||
                    (fromTHSGB && !ignoreThsgb.includes(menuItem.businessCode)) ||
                    (loginType == 1 && menuItem.businessCode === 'cybkt')
                "
                v-for="(menuItem, index) in item.businessMenu"
                :key="index"
                @click.stop="toBusiness(menuItem)"
                v-show="
                  ((loginType == 1 && menuItem.showZq) || (loginType == 2 && menuItem.showXy)) &&
                    !(
                      (menuItem.businessCode == 'czmm' || menuItem.businessCode == 'zhzh') &&
                      loginType == 1 &&
                      isLogin
                    ) &&
                    !(
                      (menuItem.businessCode == 'czmm' || menuItem.businessCode == 'zhzh') &&
                      loginType == 2 &&
                      isLoginXy
                    ) &&
                    menuItem.businessCode !== 'yyxh'
                "
              >
                <i class="icon">
                  <img :src="menuItem.mobileImgUrl" />
                </i>
                <h5 class="inline-block">{{ menuItem.businessName }}</h5>
                <span
                  class="icon_tip_dot"
                  v-if="menuItem.businessCode === 'dzhfwj' && isWjhf"
                ></span>
                <p>{{ menuItem.description }}</p>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </article>
    <queryBusiness v-if="queryShow" v-model="queryShow" @toBusiness="toBusiness"></queryBusiness>
  </div>
</template>

<script>
import { businessList, queryWjhfList } from '@/service/comServiceNew'
import { closeYgt } from '@/common/sso'
import queryBusiness from '@/views/queryBusiness'
import headComponent from '@/components/headComponent'
export default {
  components: { headComponent, queryBusiness },
  name: 'index',
  data() {
    return {
      className: 'index',
      loginType: $h.getSession('loginType') || '1', // 登录账号类型 1普通账号 2信用账号
      onTheWayShow: false, // 是否有在途业务
      onTheWayNum: 0, // 在途业务数量
      queryShow: false,
      allBusinessArr: $h.getSession('allBusinessArr') || [],
      hotLable: $h.getSession('hotLable') || [],
      ygtUserInfo: $h.getSession('ygtUserInfo', { decrypt: false }) || {},
      // 支持类型  0：普通账户+信用账户+机构账户  1：普通账户 +信用账户    2：信用账户 +机构账户  3：普通账户 +机构账户  4：普通账户 5：信用账户   6：机构账户
      zqSupportTypeArr: ['0', '1', '3', '4'],
      xySupportTypeArr: ['0', '1', '2', '5'],
      questionnaireList: [],
      fromTHSGB: $h.getSession('fromTHSGB'),
      // ignoreThsgb: ['czmm', 'sfzgx', 'sfzsw', 'kcbkt', 'sgtkt', 'hgtkt', 'bjstzz', 'zkgdh', 'jggdh', 'xsbkt', 'glgxqr', 'cybkt']//同花顺公版屏蔽的业务
      ignoreThsgb: ['yyxh'] // 同花顺公版屏蔽的业务
    }
  },
  created() {
    $h.setSession('loginType', this.loginType)
    $h.clearSession('result_serivalId')
  },
  mounted() {
    this.queryBusinessList()
    window.phoneBackBtnCallBack = this.pageBack
  },
  computed: {
    // 是否登录普通号
    isLogin() {
      let islogin =
        !!this.ygtUserInfo.fundAccount &&
        this.ygtUserInfo.fundAccount != '' &&
        $h.getSession('isLogin')
      return islogin
    },
    // 是否登录信用号
    isLoginXy() {
      return (
        !!this.ygtUserInfo.fundAccountXy &&
        this.ygtUserInfo.fundAccountXy != '' &&
        $h.getSession('isLoginXy')
      )
    },
    // 是否存在问卷回访的试题
    isWjhf() {
      return this.questionnaireList.length
    }
  },
  watch: {
    loginType(newType) {
      $h.setSession('loginType', newType)
    },
    isLogin: {
      handler(val) {
        if (val) {
          this.queryProduct()
        }
      },
      immediate: true
    }
  },
  methods: {
    showList(item) {
      let isShow = false
      let el = item['businessMenu']
      for (let j = 0; j < el.length; j++) {
        let el2 = el[j]
        if (this.loginType == 1 && el2.showZq) {
          isShow = true
          break
        }
        if (this.loginType == 2 && el2.showXy) {
          isShow = true
          break
        }
      }
      return isShow
    },
    // 查询业务列表
    queryBusinessList() {
      if (
        this.allBusinessArr.length > 0 &&
        JSON.stringify(this.allBusinessArr).indexOf('showZq') > -1
      ) {
        return
      }
      this.hotLable = []
      businessList({ source: 2 }, {}).then(
        res => {
          if (res.error_no === '0') {
            let allBusiness = []
            for (let i = 0; i < res.ismpCompQueryMenu.length; i++) {
              let el = res.ismpCompQueryMenu[i]
              let menus = (el.businessMenu = JSON.parse(el.businessMenu))
              for (let j = 0; j < menus.length; j++) {
                let menu = menus[j]
                menu.mobileImgUrl =
                  (process.env.NODE_ENV == 'development' ? '' : ROUTER_BASE) + menu.mobileImgUrl
                if (menu.isHot === '1') {
                  this.hotLable.push(menu)
                }
                menu.showZq = this.zqSupportTypeArr.indexOf(menu.supportType) > 0
                menu.showXy = this.xySupportTypeArr.indexOf(menu.supportType) > 0
                allBusiness.push(menu)
              }
              this.allBusinessArr.push(el)
            }
            if (this.fromTHSGB) {
              allBusiness = allBusiness.filter(item => {
                return this.ignoreThsgb.indexOf(item.businessCode) === -1
              })
            }
            $h.setSession('allBusinessArr', this.allBusinessArr)
            $h.setSession('allBusiness', allBusiness)
            $h.setSession('hotLable', this.hotLable)
          } else {
            _hvueToast({
              icon: 'error',
              mes: res.error_info
            })
          }
        },
        err => {
          console.log(err)
        }
      )
    },
    toBusiness(menuItem) {
      let url = menuItem.mobileBusinessUrl || ''
      let state = menuItem.mobileState || ''
      let businessCode = menuItem.businessCode || ''

      // 判断是否为在建业务
      if (state === '2') {
        _hvueToast({ mes: '业务建设中，敬请期待' })
        return
      }
      if (
        url !== 'businessRecords' &&
        businessCode !== 'dzht' &&
        businessCode !== 'wdzj' &&
        businessCode !== 'dzhfwj'
      ) {
        url = 'business'
      }
      // 两融合约展期，两融额度调整 跳转到交易
      // if (businessCode == 'lrhyzq' || businessCode == 'lredtz') {
      //   let msgNo = businessCode == 'lrhyzq' ? '136' : '135'
      //   let msg = {
      //     funcNo: '60099',
      //     actionType: '6', // 打开交易页面
      //     params: {
      //       pageName: msgNo
      //     }
      //   }
      //   $h.callMessageNative(msg)
      //   return
      // }

      if (businessCode === 'kcbczc') {
        this.$router.push({
          name: 'kcbczcIntro',
          query: {
            type: businessCode
          }
        });
        return;
      }

      // 一站式销户
      if (businessCode === 'yyxh') {
        this.$router.push({
          name: 'xhBusinessIntro',
          query: {
            type: businessCode
          }
        })
        return
      }

      if (businessCode === 'czmm' || businessCode === 'zhzh') {
        this.$router.push({
          name: url,
          query: {
            type: businessCode
          }
        })
        return
      }
      // 判断是否登录对应的账号
      if (this.loginType == 1 && this.ygtUserInfo.fundAccount && this.isLogin) {
        this.$router.push({
          name: url,
          query: {
            type: businessCode
          }
        })
      } else if (this.loginType == 2 && this.ygtUserInfo.fundAccountXy && this.isLoginXy) {
        this.$router.push({
          name: url,
          query: {
            type: businessCode
          }
        })
      } else {
        if ($hvue.platform === '0' && !this.fromTHSGB) {
          this.$router.push({ name: 'login', query: { loginType: this.loginType } })
        } else {
          this.$router.push({
            name: url,
            query: {
              type: businessCode
            }
          })
        }
      }
    },
    pageBusinessRecords() {
      this.toBusiness({ mobileBusinessUrl: 'businessRecords' })
      // this.$router.push({ name: "businessRecords", params: {} });
    },
    pageBack() {
      // h5访问时退出登录
      if ($hvue.platform === '0' && !this.fromTHSGB) {
        $h.clearSession('ygtUserInfo')
        this.ygtUserInfo = {}
      } else {
        closeYgt(0, '1A', 0, 1, this.$router)
      }
    },
    logout(accountType) {
      if (accountType == 1) {
        this.ygtUserInfo.fundAccount = ''
      } else {
        this.ygtUserInfo.fundAccountXy = ''
      }
      if (this.ygtUserInfo.fundAccount == '' && this.ygtUserInfo.fundAccountXy == '') {
        this.ygtUserInfo = {}
        $h.clearSession('ygtUserInfo')
      }
    },
    pageQuery() {
      this.$router.push({ name: 'query', params: {} })
    },
    // 查询电子合同
    queryProduct() {
      queryWjhfList({
        fundAccount: $h.getSession('ygtUserInfo', { decrypt: false }).fundAccount,
        clientId: $h.getSession('ygtUserInfo', { decrypt: false }).clientId,
        userId: $h.getSession('ygtUserInfo', { decrypt: false }).userId,
        name: $h.getSession('ygtUserInfo', { decrypt: false }).name,
        branchNo: $h.getSession('ygtUserInfo', { decrypt: false }).branchNo
      }).then(
        res => {
          if (res.error_no === '0') {
            let _results = res.questionnaireList,
              openFlag = res.dataRow.length ? res.dataRow[0].openFlag : ''
            this.questionnaireList = _results
            $h.setSession('wjhf_questionnaireList', JSON.stringify(_results))
            if (+openFlag) {
              _hvueConfirm({
                title: '客户回访问卷',
                mes:
                  '尊敬的投资者：您好，为了保护投资者的合法权益，诚邀您填写回访问卷，感谢您的支持与信任！',
                opts: [
                  {
                    txt: '关闭',
                    color: false
                  },
                  {
                    txt: '去完成',
                    color: true,
                    callback: () => {
                      // 确定之后的回调
                      this.$router.push({
                        name: 'dzhfwjList',
                        query: {
                          type: 'dzhfwj'
                        }
                      })
                    }
                  }
                ]
              })
            }
          } else {
            _hvueToast({
              icon: 'error',
              mes: res.error_info
            })
          }
        },
        err => {
          console.log(err)
        }
      )
    }
  }
}
</script>

<style>
.home_nav ul li p {
  font-size: 0.13rem;
  color: #333;
  line-height: 0.3rem;
  font-weight: 500;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.inline-block {
  display: inline-block;
}
.icon_tip_dot {
  display: inline-block;
  vertical-align: middle;
  margin-left: 0.08rem;
  width: 0.06rem;
  height: 0.06rem;
  border-radius: 50%;
  background: #d84749;
}
</style>
