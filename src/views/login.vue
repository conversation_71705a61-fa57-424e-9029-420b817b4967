<template>
  <div class="content">
    <div class="login_box">
      <a class="icon_close" @click.stop="pageBack"></a>
      <div class="login_logo">
        <img src="@/assets/images/logo.png" />
      </div>
      <div class="login_form">
        <div class="ui field">
          <div class="ui dropdown">
          <!-- <div class="ui dropdown" @click.stop="chooseTypeShow = !chooseTypeShow"> -->
            <strong>{{ accountType[loginType].text }}</strong>
          </div>
          <div class="login_select" v-show="chooseTypeShow">
            <ul>
              <li v-for="(item, index) in accountType" :key="item.value">
                <span @click.stop="chooseType(index)">{{ item.text }}</span>
              </li>
            </ul>
          </div>
        </div>
        <div class="ui field">
          <input
            v-model.trim="account"
            type="tel"
            class="ui input"
            :placeholder="placeholderTxt"
            maxlength="15"
            @blur="verifyYes(account, 'account')"
            @input="compeletenessCheck()"
          />
          <a class="txt_close" @click.stop="account=''" v-show="account!=''"></a>
        </div>
        <div class="ui field pword">
          <input
            v-model.trim="pwd"
            :type="pwdType"
            class="ui input"
            placeholder="请输入密码"
            maxlength="8"
            @blur="verifyYes(pwd, 'pwd')"
            @input="compeletenessCheck()"
          />
          <a class="txt_close" @click.stop="pwd=''" v-show="pwd!=''"></a>
          <a class="icon_eye" :class="{show: pwdShow}" @click.stop="pwdShow = !pwdShow"></a>
        </div>
      </div>
      <div class="ce_btn">
        <a class="ui button block rounded" :class="{disable:!verifyPass}" @click.stop="login">登录</a>
      </div>
      <p class="login_bottom">
<!--        <a class="fr" href="#">账户安全设置</a>-->
        <span>没有账号？</span><a @click.stop="findAccount">立即找回</a>
      </p>
<!--      <div class="no_acctbox">-->
<!--        <a class="link" href="#">没有账户？立即开户</a>-->
<!--      </div>-->
    </div>
  </div>
</template>

<script>
import { closeYgt, ssoTHSGBFund } from '@/common/sso'
import { userLogin } from '@/service/comServiceNew'
import { thscodeToType } from '@/common/util'
export default {
  /**
   * ********,********,********,********,********,********,********
   */
  data () {
    return {
      account: '',
      pwd: '',
      pwdShow: false,
      accountType: [{
      }, {
        value: 1,
        text: '资金账号'
      }, {
        value: 2,
        text: '信用账号'
      }],
      currentAccountType: 3,
      chooseTypeShow: false,
      verifyPass: false,
      errTxt: '',
      placeholderTxt: '',
      loginType: 1, // 登录账号类型 1普通账号 2信用账号
    }
  },
  created () {
    this.loginType = this.$route.query.loginType || '1'
    if (this.loginType) {
      $h.setSession('loginType', this.loginType)
    }
    console.log('loginType:' + this.loginType)
    this.placeholderTxt = this.loginType == 1 ? '请输入资金账号' : '请输入信用账号'

    //获取同花顺公版的资金账号
    if($h.getSession('fromTHSGB') && $h.getSession('thsgb_token')){
      ssoTHSGBFund($h.getSession('thsgb_token')).then(data => {
        this.account = data.encryData;
      }).catch(error => {})
    }
  },
  computed: {
    pwdType () {
      return this.pwdShow ? 'tel' : 'password'
    }
  },
  methods: {
    // 选择账户类型
    chooseType (index, event) {
      this.currentAccountType = index
      this.chooseTypeShow = false
    },
    verifyYes (str, type, noToast) {
      if (type === 'pwd') {
        if (str.trim() === '') {
          this.errTxt = '请输入6-8位数字密码'
        } else if (!/^\d{6,8}$/.test(str)) {
          this.errTxt = '请输入6-8位数字密码'
        } else {
          this.errTxt = 'pass'
        }
      }
      if (type === 'account') {
        if (str.trim() === '') {
          this.errTxt = '请输入资金账号'
        } else if (!/^\d{5,15}$/.test(str)) {
          this.errTxt = '请输入正确格式的资金账号'
        } else {
          this.errTxt = 'pass'
        }
      }

      if (this.errTxt !== 'pass') {
        if (!noToast) {
          _hvueToast({
            timeout: 1500,
            mes: this.errTxt
          })
        }
        return false
      }
      return true
    },
    compeletenessCheck () {
      this.verifyPass = (this.verifyYes(this.account, 'account', true) && this.verifyYes(this.pwd, 'pwd', true))
    },
    login () {
      if (
        !this.verifyYes(this.account, 'account') ||
        !this.verifyYes(this.pwd, 'pwd')
      ) { return }

      userLogin({ account: this.account, password: this.pwd, accountType: this.loginType }).then(
        res => {
          console.log(res)
          if (res.error_no === '0') {
            // 保存用户信息
            let userInfo = res.userInfo[0]
            userInfo.riskLevelDesc =
              res.custTypeCheck !== undefined
                ? res.custTypeCheck[0].riskLevelDesc
                : ''
            let ygtUserInfo = $h.getSession('ygtUserInfo', {decrypt: false}) || {}

            if (this.loginType == '1') {
              if (ygtUserInfo.userId && userInfo.userId === ygtUserInfo.userId) {
                userInfo.fundAccountXy = ygtUserInfo.fundAccountXy || ''
              } else {
                userInfo.fundAccountXy = ''
              }
              $h.setSession('isLogin', true)
              $h.setSession('ygtUserInfo', userInfo, {encrypt: false})
            } else {
              userInfo.fundAccountXy = userInfo.fundAccount
              if (ygtUserInfo.userId && userInfo.userId === ygtUserInfo.userId) {
                userInfo.fundAccount = ygtUserInfo.fundAccount || ''
              } else {
                userInfo.fundAccount = ''
              }
              $h.setSession('isLoginXy', true)
              $h.setSession('ygtUserInfo', userInfo, {encrypt: false})
            }
            // 对比用户是否一致
            if (ygtUserInfo.userId && userInfo.userId !== ygtUserInfo.userId) {
              if (this.loginType == '1') {
                _hvueToast({
                  timeout: 4000,
                  mes: '已登录的信用账户，与当前登录的账户不一致，将退出已登录的账户。',
                  callback: () => {
                    this.afterCallback()
                  }
                })
              }
              if (this.loginType == '2') {
                _hvueToast({
                  timeout: 4000,
                  mes: '已登录的普通账户，与当前登录的账户不一致，将退出已登录的账户。',
                  callback: () => {
                    this.afterCallback()
                  }
                })
              }
            } else {
              this.afterCallback()
            }
          } else {
            _hvueToast({
              icon: 'error',
              mes: res.error_info
            })
          }
        },
        err => {
          console.log(err)
        }
      )
    },
    afterCallback () {
      // 判断是否有参数直接跳转到业务
      let businessCode = this.$route.query.type
      if (businessCode) {
        this.$router.push({
          name: 'business',
          query: {
            type: businessCode
          }
        })
        return
      }
      if (this.$route.query.thscode) {
        businessCode = thscodeToType(this.$route.query.thscode)
        this.$router.replace({
          name: 'business',
          query: {
            type: businessCode
          }
        })
        return
      }
      this.$router.go(-1)
    },
    // 返回上一页
    pageBack () {
      // 如果是从交易跳转过来的，返回到交易
      let source = this.$route.query.source
      if (source && source === 'trade') {
        closeYgt(0, '1A', 0, 1, this.$router)
        return
      }
      this.$router.push({
        path: '/index'
      })
    },
    // 找回账户
    findAccount () {
      this.$router.push({
        name: 'business',
        query: {
          type: 'zhzh'
        }
      })
    }
  },
}
</script>
