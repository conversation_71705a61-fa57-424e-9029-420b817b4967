body {
	background: #f9f9f9;
	font-family: <PERSON>ragino Sans GB, Helvetica, STHeiti STXihei, Microsoft YaHei, Arial;
	color: #333;
}

button:visited {
	border: none;
	background: none;
}

button:active {
	border: none;
	background: none;
}

p {
	margin: 0;
}

section.main.fixed {
	background-color: #f9f9f9;
}

section.main.fixed>article.content {
	overflow-y: auto;
	position: relative;
}

.ui.field.border>.ui.input,
.ui.field.border>.ui.dropdown {
	border: 1px solid #EEEEEE;
}

button.ui.button:active {
	box-shadow: 0 0 0 0 rgba(0, 0, 0, 0) inset, 0 0 0 rgba(0, 0, 0, 0) inset;
}

section.main.fixed>article::-webkit-scrollbar {
	width: 0px;
	display: none;
}

.redcol {
	color: #e04c3a !important;
}

.greencol {
	color: #6aba3f !important;
}

.mt10 {
	margin-top: 0.1rem !important;
}

.mt15{
	margin-top: 0.15rem !important;
}

.mt20 {
	margin-top: 0.2rem !important;
	white-space: normal;
}

.ml10 {
	margin-left: 0.1rem;
}


/*-- 按钮 --*/

.ce_btn {
	padding: 0.15rem 0.1rem;
}

.ui.button {
	height: 0.44rem;
	line-height: 0.44rem;
	background: #BA0105;
	color: #fff;
	font-size: 0.16rem;
}

.ui.button.rounded {
	-moz-border-radius: 0.03rem;
	-webkit-border-radius: 0.03rem;
	border-radius: 0.03rem;
}

.ui.button.disable {
	background: #cccccc;
}

.ui.button.border {
	border: 1px solid #fff;
	line-height: 0.42rem;
	background: #fff;
	color: #B80205;
}

.code_img {
	display: block;
	width: 0.7rem;
	height: 0.32rem;
	position: absolute;
	top: 50%;
	margin-top: -0.16rem;
	right: 0.15rem;
}

.code_img img {
	display: block;
	width: 100%;
	height: 100%;
}


/*-- 输入 --*/

.ui.field > .ui.input {
	padding-left: 0;
	border-bottom: 1px solid #eee;
	font-size: 0.16rem;
	color: #000;
	height: 0.47rem;
	padding-top: 0.12rem;
	padding-bottom: 0.12rem;
	-moz-transition: none;
	-webkit-transition: none;
	transition: none;
}

.ui.field > .ui.input::-moz-placeholder {
	color: #ccc;
}

.ui.field > .ui.input::-webkit-input-placeholder {
	color: #ccc;
}

.ui.field > .ui.input:focus {
	border-color: #eee!important;
}

.ui.field > .ui.dropdown {
	border-bottom: 1px solid #eee;
	font-size: 0.16rem;
	color: #000;
	height: auto;
	line-height: 0.46rem;
	-moz-transition: none;
	-webkit-transition: none;
	transition: none;
}

.ui.field > .ui.dropdown>strong {
	padding-left: 0;
	font-size: 0.16rem;
	height: 0.46rem;
	line-height: 0.46rem;
	font-weight: normal;
	padding-right: 0.36rem;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}

.ui.field > .ui.dropdown > strong:after {
	display: block;
	content: "";
	width: 0.1rem;
	height: 0.1rem;
	position: absolute;
	top: 50%;
	margin-top: -0.05rem;
	right: 0.15rem;
	border: 0 none;
	background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAAEnQAABJ0Ad5mH3gAAADzSURBVDhP7ZNBDoIwEEUJtY2sqnsT9l7BO+iKC5h4AD2AXsNT6BbUhAVX8AQcwCUrEuxvJ4SiRoouecks+ufPNIUZb+A/xD5fp1JO6OhMJuUUPfQh8cVOHSoVWRqGYy06gBrUoofqtfXSIJglPs+NwM9VFDHyfgVe1FBtjl46cRNirsQHEjHjRy12AF5do2rRg2RDMhovVKIwBrEn+SPKc6BmBWpJtrkysVKGkowbkl9AjjzlhYklye9pmnEByTVdL7XAk6nAeo7rZ7Fof/C+P66mPRIIuuDkMloWzaGl6DX8FmatxB3xy3paYAPqLRiw8bwnz9h+l68SlD0AAAAASUVORK5CYII=") no-repeat center;
	background-size: 0.1rem;
}

.ui.field > .ui.dropdown > strong.default {
	color: #ccc;
}

.ui.field > .ui.dropdown.active {
	border-color: #eee;
}

.ui.field.text > .ui.label {
	position: absolute;
	line-height: 0.46rem;
	height: 0.46rem;
	width: auto;
	padding: 0;
	top: 0;
	left: 0;
	text-align: left;
	font-size: 0.16rem;
	color: #666;
	z-index: 50;
}

.ui.field.text > .ui.input,
.ui.field.text > .ui.dropdown {
	padding-left: 0.75rem;
}

.ui.field {
	-webkit-user-select: auto;
}

.ui.field > .teare01 {
	display: block;
	width: 100%;
	min-height: 0.47rem;
	padding: 0.11rem 0.15rem;
	line-height: 0.24rem;
	border: 0 none;
	border-bottom: 1px solid #eee;
	font-size: 0.16rem;
	color: #000;
	outline: none;
	-webkit-tap-highlight-color: rgba(0, 0, 0, 0);
	-webkit-user-select: auto;
	user-modify: read-write-plaintext-only;
}

.ui.field.text > .teare01 {
	padding-left: 0.75rem;
}

.ui.field.pword .txt_close {
	right: 0.42rem;
}

.ui.field.code .txt_close {
	right: 0.95rem;
}

.input_form {
	background: #fff;
	padding-left: 0.15rem;
}

.input_form .ui.field:last-child>.ui.input,
.input_form .ui.field:last-child>.ui.dropdown {
	border-bottom: 0 none;
	height: 0.46rem;
}

.date_input {
	height: 0.47rem;
	border-bottom: 1px solid #eee;
}

.input_form .ui.field:last-child>.date_input {
	border-bottom: 0 none;
	height: 0.46rem;
}

.ui.field.text .date_input {
	padding-left: 0.78rem;
}

.ui.field .date_input .dropdown {
	height: 0.46rem;
	padding: 0.11rem 0;
	line-height: 0.24rem;
	font-size: 0.16rem;
	float: left;
	width: auto !important;
	color: #000;
}

.ui.field .date_input .dropdown strong {
	font-weight: normal;
}

.ui.field .date_input .line {
	height: 0.01rem;
	width: 0.14rem;
	float: left;
	margin: 0.22rem 0.05rem 0;
	background: #000;
}


.ui.field > .ui.input[readonly=readonly]{
	background: #fff;
}



/*-- 选择 --*/
.icon_radio {
	min-height: 0.24rem;
	line-height: 0.24rem;
	display: inline-block;
	padding-left: 0.3rem;
	position: relative;
}

.icon_radio:before {
	display: block;
	content: "";
	width: 0.16rem;
	height: 0.16rem;
	position: absolute;
	top: 0.03rem;
	left: 0;
	border: 1px solid #ccc;
	-moz-border-radius: 100%;
	-webkit-border-radius: 100%;
	border-radius: 100%;
}

.icon_radio.checked:before {
	background: #f6f6f6 url(../images/check_ic02.png) no-repeat center;
	background-size: 0.18rem;
}

.icon_radio.disable:before {
	opacity: 0.5;
}

.ui.checkbox label {
	display: block;
	line-height: 0.24rem;
	font-size: 0.14rem;
}

.ui.radio>input[type=radio],
.ui.checkbox>input[type=checkbox] {
	width: 0.18rem;
	height: 0.18rem;
	top: 0.03rem;
	left: 0;
	-moz-transform: translateY(0);
	-webkit-transform: translateY(0);
	transform: translateY(0);
}

.ui.checkbox label:before {
	top: 0.03rem;
	margin-top: 0;
	border: 1px solid #ccc;
	-moz-border-radius: 0.02rem;
	-webkit-border-radius: 0.02rem;
	border-radius: 0.02rem;
}

.ui.checkbox input[type=checkbox]:checked+label:before {
	border-color: #d2b380;
    background: #d2b380 url(../images/check_ic01.png) no-repeat center;
    background-size: 0.18rem;
}

.ui.checkbox.rule_check {
	margin: 0.15rem 0.15rem 0;
	display: block;
	position: relative;
}

.ui.checkbox.rule_check label {
	color: #999;
}

.ui.checkbox.rule_check a {
	color: #285FC1;
}

.icon_check {
	min-height: 0.2rem;
	line-height: 0.2rem;
	display: inline-block;
	padding-left: 0.3rem;
	position: relative;
}

.icon_check:before {
	display: block;
	content: "";
	width: 0.16rem;
	height: 0.16rem;
	position: absolute;
	top: 0.03rem;
	left: 0;
	border: 1px solid #ccc;
	-moz-border-radius: 0.03rem;
	-webkit-border-radius: 0.03rem;
	border-radius: 0.03rem;
}

.icon_check.checked:before {
	border-color: #d2b380;
	background: #d2b380 url(../images/check_ic01.png) no-repeat center;
	background-size: 0.18rem;
}



/*-- 头部 --*/

.header_inner {
	height: 0.44rem;
	line-height: 0.44rem;
	background: #8C0002;
	background-image: -webkit-linear-gradient(90deg, #8C0002 0%, #BA0105 100%);
	background-image: -moz-linear-gradient(90deg, #8C0002 0%, #BA0105 100%);
	background-image: -o-linear-gradient(90deg, #8C0002 0%, #BA0105 100%);
	background-image: -webkit-gradient(linear, 0 100%, 0 0, from(#8C0002), to(#BA0105));
	background-image: linear-gradient(90deg, #8C0002 0%, #BA0105 100%);
	position: relative;
}

.header_inner>h1.title {
	font-size: 0.18rem;
	color: #fff;
	position: relative;
	z-index: 0;
}

.header_inner>h1.title.spel{
	line-height: 0.2rem;
	padding: 0.04rem 0;
}

.header_inner>h1.title.spel em{
	display: block;
	font-style: normal;
	font-size: 0.1rem;
	line-height: 0.14rem;
	margin-top: 0.02rem;
}

.header_inner>.icon_back {
	min-width: 0.5rem;
	height: 0.44rem;
	position: absolute;
	left: 0;
	top: 0;
	z-index: 50;
}

.header_inner>.icon_back:before {
	display: block;
	content: "";
	width: 0.5rem;
	height: 0.44rem;
	position: absolute;
	top: 0;
	left: 0;
	background: url(../images/icon_back.png) no-repeat center;
	background-size: 0.22rem 0.22rem;
}

.header_inner>.icon_back.text {
	padding-left: 0.3rem;
	font-size: 0.13rem;
	color: #666;
	text-decoration: none;
}

.header_inner>.icon_back.text:before {
	left: -0.04rem;
}

.icon_text {
	font-size: 0.14rem;
	color: #fff;
	padding: 0 0.13rem;
	position: absolute;
	top: 0;
	right: 0;
	z-index: 50;
}

.icon_text:active {
	color: #fff;
}


/*-- 其他公用 --*/
.icon_eye {
	display: block;
	width: 0.22rem;
	height: 0.22rem;
	position: absolute;
	top: 50%;
	margin-top: -0.11rem;
	right: 0.12rem;
	background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAkCAYAAADhAJiYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAAEnQAABJ0Ad5mH3gAAAIJSURBVFhH7ZQ7aBRRFIb/O+fecWOjjRACGhC0UZCACIJBAkZEA4KKlXYi2EoCYiEWYiFGSGeTMoJWIWZnZncVwXaDjSKCthY+Cl9E8BX/e/csriTZbDAPhPvBheE/7zNzB5FIJBKJ/O88Ajbnkp6YRqlXpTUng+srRAamgI1BuAdIbtzN3NjxKuzeIK4yXITlIo6z5lQlcedV/ps8sRfzxP3iqfutqbyi3AW6isReYa3XReI+VkQOq2lhKpKeYkNf6Tznuy9Q2qqmfyYXOcRmXoXcviG4PWpqTwZ7gE29bwS6L0xyia81VfOymQa6C2Pv+HyNY5/VUNqm5s7gBCN/EoSJXjLRcBVdPeqyJBW43YVxNzjUh9ZcBew+dekMf/MY+C4EG/eACb+1JPzJC/CQ+nXqZ3k7juSw/dzqwUzkKLVztN9i809aYvymP3Oop+HZuExLdQaLjTWS2Of+NlSR7qB2m9pss8AyzifGjvrNlpHubA7nm9dy7cnhdjHoe0gmMqhygGNt4XRXaX/TUnDB4z9anst+2xoe8L+Xho99MQM4lReHAfdDgLGTKs1jDjD+hvj/B/2v0f9taMLYiULSk+1+sjVgU9OfTV1QeXE4/Wn//mvYsF2lJWHMTCggMqRSW7LEnaH/Y/82VFpZ2FA9bEjkmErrCz/aMhv6wdu2X6VIJBKJRNYG4Dfa0uAsn5paHQAAAABJRU5ErkJggg==") no-repeat center;
	background-size: 0.18rem;
}

.icon_eye.show {
	background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAkCAYAAADhAJiYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAAEnQAABJ0Ad5mH3gAAALKSURBVFhH7VdBaxNBGF13O7vZUFoUBKOVXnqqIIoHPat3PdiDoCcFf0K9FQ+ePHjx6i+o0BSk7CYNRYXag/ZQTAUvIl4lPQQpCGH93uSl1jDZzKSL9pAHjyQz73vfZOeb2RlvjDH+JT5UKuVaEN1IA/Uk8dVa4ocf5fOb8Ccp33XbGjTQIobhxSDLshP1ILopSVaFv4SZIxFThQe8aDsa0iC8k/rqc5/5e/n3z5IgvF1X6nJSKp3fnJmJQXzXbdKnNaJljI6HFzxpb49aHJ+V4JWekfCrTMXjt5OTpymxBmIQ2/U4GNgKclCSj9pEfE2CWgzeS/3oUba05LN7ZMADXvCkdwu52G1GGkTXRdhmwOuNcvkMuwoDPOHNHG3kZNffWFfqogj2IZR5fpktLATsGghdL77aFP6AubAptfO8odQFSoyAN3Igl3AfudnVxZe5uUjmeQcCCNk8FOtxfI6m/ewkgXrxaX4+pNSIP4MKdzAGNnueBD+l0a7LvoHCZNwgNvIGhVyi2dVaGYNu3JidLUmDLuJ0Ir6qGy1R89VdbZZHeVKUG4Gc1LYwFpjeZ8MWNVZIpqZOScx3xuaxM6ymRLMFLcbiyZ7wCj/k8wH7jUABo2belMsVPhmbwWii0GljBHJrnYxFRhdu6x9KXWG/EaLBajImtGCTNkYgd1cXbiOR3qjeTU+fZL8RosHS7k9kyzZtjEBu6vZcBtTbMEehy4Csp6zJoFHoNGXL+DGsqFGYNHfmsKKWRfKQ2mXrZY+lK5oOtS60XvbyUO45bYzY5KBzouvGCNi+OvAaEE2DWhu6vzoAl5erHlT3SeVN39FeroDr8QN1wULH6sOWUNzxo4djdUDr4VgdYXvgOafKQBCH/MUjHPIXux4HflXrQ/5hyDwPvAbVg/CW7LCX+q9Buq3oa9BhFHRRXC3kotgP7Bv//So9xhi58LzfKwcIb4CJ/JoAAAAASUVORK5CYII=");
}

.txt_close {
	display: block;
	width: 0.22rem;
	height: 0.22rem;
	position: absolute;
	top: 50%;
	margin-top: -0.11rem;
	right: 0.1rem;
	background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAkCAYAAADhAJiYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAAEnQAABJ0Ad5mH3gAAAF6SURBVFhH7ZfBrYMwEERp5ZeQCtLEbyhdAOJCI7nSAvQQQEpu/5I/Y60li6zBBiJ84EkjRcnuzAYDhuzk5AiqqrqUZXnL8/xeFEUHvUQdv+NvrJHy74GgX4S20DtQLXukfT9g+gPzZhIWo4YeYrcNmF2hh2O+VvS4iu06aIBz4s8x3STxWjeULNMeR2aqx6rlQ+OWc2ZJjcSEwStDMTGq69pI+83VUh0zJG4ZNKiXNgP6vjeaCwusayVuHt7QlGYjG0R8YSE1VkE3TxzKm9ZsNRcYMwzFLIn1I9uBamClBccOQzFLYv2gkHuTauDKHWAYhvc4juZz6DCiTmL9oIibpNb8IQZzGAuHihiGekmsHxZNmrxiuD0yhMNFDvSUWD8oil4yDmWP1O5LltxJndxln9yNkaA4na2DcONTDIwYMDeM1VIdMyQuDDSl8/hB8A/SekAjaE7nEdZCAyiNh3yLLF8ar0EuvDJgfvyL4hTe0BB0/Kv0yckHWfYPUBw0HY/LPIMAAAAASUVORK5CYII=") no-repeat center;
	background-size: 0.18rem;
}

.white_bg {
	background: #fff !important;
}

.notice_box {
	padding: 0.2rem 0.15rem 0.25rem;
	background: #fff;
	min-height: 2.2rem;
	text-align: center;
	margin-bottom: 0.1rem;
}

.notice_box .pic {
	margin-bottom: 0.05rem;
}

.notice_box .pic img {
	display: block;
	margin: 0 auto;
	height: 1.68rem;
}

.notice_box h5 {
	font-weight: normal;
	font-size: 0.16rem;
	line-height: 0.24rem;
	color: #666;
}

.notice_box .imp{
	color: #BA0105;
}

.notice_box.spel{
	padding: 0 0.3rem 0.2rem;
}

.notice_box.spel h5{
    font-size: 0.2rem;
    line-height: 0.3rem;
    padding-bottom: 0.05rem;
    color: #000;
}

.notice_box.spel p{
	font-size: 0.14rem;
    color: #999;
    line-height: 0.2rem;
}

.code_btn {
	line-height: 0.32rem;
	padding: 0 0.15rem;
	font-size: 0.13rem;
	color: #B80205;
	position: absolute;
	top: 0.07rem;
	right: 0;
	z-index: 50;
	background: #fff;
}

.code_btn.time {
	color: #999;
}

.bot_tips{
	padding: 0.05rem 0.15rem;
	font-size: 0.13rem;
	color: #999;
	line-height: 0.18rem;
}

.bot_tips a{
	color: #285FC1;
}

.com_infobox{
	margin: 0.1rem 0;
}

.com_infobox ul{
	background: #fff;
	padding-left: 0.15rem;
}

.com_infobox ul li{
	border-bottom: 1px solid #eee;
	position: relative;
	padding: 0.11rem 0.15rem 0.11rem 0.9rem;
	line-height: 0.24rem;
	font-size: 0.16rem;
}

.com_infobox ul li:last-child{
	border-bottom: 0 none;
}

.com_infobox ul li .tit{
	position: absolute;
	top: 0.11rem;
	left: 0;
}

.com_infobox ul li p{
	text-align: right;
	min-height: 0.24rem;
	color: #999;
}




/*-- 弹层提示 --*/
.ui.dialog-overlay {
	background: rgba(0, 0, 0, 0.4);
}

.ui.dialog {
	width: 80%;
	left: 10%;
	margin-top: 0;
	-weebkit-transform: translateY(-50%);
	transform: translateY(-50%);
	-moz-border-radius: 0.04rem;
	-webkit-border-radius: 0.04rem;
	border-radius: 0.04rem;
	background: rgba(255,255,255,0.95);
}

.ui.dialog .ui.dialog-btn {
	border-top: 1px solid #eee;
}

.ui.dialog .ui.dialog-btn .ui.button {
	color: #285FC1;
	height: 0.5rem;
	line-height: 0.5rem;
	font-size: 0.18rem;
	padding: 0;
}

.ui.dialog .ui.dialog-btn .ui.button:not(:nth-child(1)) {
    border-left: 1px solid #eee;
}

.ui.dialog .ui.dialog-cnt{
	padding: 0.2rem;
}

.ui.dialog .ui.dialog-cnt h4 {
	border-bottom: 0 none;
	font-size: 0.18rem;
	height: auto;
	color: #000;
	line-height: 0.28rem;
	padding: 0;
	margin-bottom: 0.08rem;
}

.ui.dialog .ui.dialog-cnt > div {
	padding: 0;
}

.ui.dialog .ui.dialog-cnt > div > p {
	font-size: 0.14rem;
	color: #666;
	line-height: 0.22rem;
}

.ui.dialog .ui.dialog-cnt > div.spel > p{
	font-size: 0.16rem;
	color: #000;
	line-height: 0.28rem;
}

.ui.dialog .ui.dialog-btn .ui.button.cancel{
	color: #000;
}

.ui.dialog .ui.dialog-cnt > div > p.spel{
	font-size: 0.18rem;
	color: #000;
	line-height: 0.28rem;
}

.pop_tips {
	position: fixed;
	top: 50%;
	left: 50%;
	-moz-transform: translate(-50%, -50%);
	-webkit-transform: translate(-50%, -50%);
	transform: translate(-50%, -50%);
	background: rgba(0, 0, 0, 0.7);
	z-index: 2000;
	padding: 0.2rem;
	color: #fff;
	-moz-border-radius: 0.03rem;
	-webkit-border-radius: 0.03rem;
	border-radius: 0.03rem;
	font-size: 0.16rem;
	text-align: center;
	min-width: 1.2rem;
}

.pop_tips .ok {
	font-size: 0.14rem;
	display: block;
	padding-top: 0.5rem;
	position: relative;
}

.pop_tips .ok:before {
	display: block;
	content: "";
	width: 0.5rem;
	height: 0.4rem;
	position: absolute;
	top: 0;
	left: 50%;
	margin-left: -0.25rem;
	background: url(../images/tips_ok.png) no-repeat center;
	background-size: 100%;
}

.toast_tips{
	width: 100%;
	padding: 0 0.3rem;
	text-align: center;
	position: fixed;
	top: 50%;
	-webkit-transform: translateY(-50%);
	transform: translateY(-50%);
	left: 0;
	z-index: 2000;
}

.toast_tips span{
	display: inline-block;
	padding: 0.06rem 0.25rem;
	line-height: 0.24rem;
	font-size: 0.16rem;
	color: #fff;
	background: rgba(0,0,0,0.7);
	-moz-border-radius: 0.04rem;
	-webkit-border-radius: 0.04rem;
	border-radius: 0.04rem;
}


/*-- 选择列表 --*/
.select_list {
	background: #fff;
	padding-left: 0.15rem;
}

.select_list li {
	border-bottom: 1px solid #eee;
}

.select_list li:last-child {
	border-bottom: 0 none;
}

.select_list li span {
	display: block;
	padding: 0.11rem 0.32rem 0.11rem 0;
	line-height: 0.24rem;
	font-size: 0.16rem;
	color: #000;
	position: relative;
}

.select_list li.active span:after {
	display: block;
	content: "";
	width: 0.22rem;
	height: 0.22rem;
	position: absolute;
	top: 50%;
	margin-top: -0.11rem;
	right: 0.1rem;
	background: url(../images/icon_active.png) no-repeat center;
	background-size: 0.22rem;
}




/*-- 资质查询、账户详情 --*/
.cond_box .title {
	padding: 0.15rem 0.15rem 0.05rem;
	line-height: 0.24rem;
	font-weight: normal;
	font-size: 0.13rem;
	color: #999;
}

.cond_box ul {
	background: #fff;
	padding-left: 0.15rem;
}

.cond_box ul li {
	position: relative;
}

.cond_box ul li .tit {
	border-bottom: 1px solid #eee;
	padding: 0.11rem 0.15rem 0.11rem 0;
	line-height: 0.24rem;
	position: relative;
}

.cond_box ul li:last-child .tit {
	border-bottom: 0 none;
}

.cond_box ul li p {
	color: #606060;
	font-size: 0.16rem;
}

.cond_box ul li.ok .tit p,
.cond_box ul li.error .tit p,
.cond_box ul li.warn .tit p {
	padding-left: 0.3rem;
	position: relative;
}

.cond_box ul li.ok .tit p:before,
.cond_box ul li.error .tit p:before,
.cond_box ul li.warn .tit p:before {
	display: block;
	content: "";
	width: 0.22rem;
	height: 0.22rem;
	position: absolute;
	top: 50%;
	margin-top: -0.11rem;
	left: 0;
	background-repeat: no-repeat;
	background-position: center;
	background-size: 0.22rem;
}

.cond_box ul li.ok .tit p:before {
	background-image: url(../images/tag_ok.png);
}

.cond_box ul li.error .tit p:before {
	background-image: url(../images/tag_error.png);
}

.cond_box ul li.warn .tit p:before {
	background-image: url(../images/tag_warn.png);
}

.cond_box ul li .link {
	height: 0.24rem;
	line-height: 0.24rem;
	position: absolute;
	top: 0.11rem;
	right: 0.15rem;
	padding-right: 0.18rem;
	font-size: 0.13rem;
	color: #285FC1;
}

.cond_box ul li .link:after {
	display: block;
	content: "";
	width: 0.06rem;
	height: 0.12rem;
	position: absolute;
	top: 50%;
	margin-top: -0.06rem;
	right: 0;
	background: url(../images/arrow02.png) no-repeat center;
	background-size: 100%;
}

.cond_tips {
	padding: 0.1rem 0.15rem;
	text-align: center;
	font-size: 0.13rem;
	color: #999;
	margin-top: 0.1rem;
}

.cond_box ul li .cont {
	padding-left: 0.3rem;
	border-top: 1px solid #eee;
	margin-top: -1px;
}

.cond_box ul li .cont p {
	padding: 0.11rem 0.15rem 0.11rem 0;
	border-bottom: 1px solid #eee;
	line-height: 0.24rem;
	overflow: hidden;
	color: #999;
	position: relative;
}

.cond_box ul li .cont p:last-child{
	border-bottom: 0 none;
}

.cond_box ul li .tips {
	display: block;
	font-size: 0.13rem;
	line-height: 0.18rem;
	color: #999;
	margin-left: 0.3rem;
}

.cond_box ul li.warn .tips{
	color: #F3A91C;
}

.cond_box ul li .status {
	position: absolute;
	top: 0.11rem;
	right: 0.15rem;
	font-size: 0.16rem;
	color: #999;
}

.cond_box ul li .status.ok {
	color: #999;
}

.cond_box ul li .status.error {
	color: #f85e6b;
}

.normal_span,
.dormant_span,
.assign_span{
	display: inline-block;
	font-size: 0.1rem;
	width: 0.4rem;
	text-align: center;
	line-height: 0.16rem;
	-moz-border-radius: 0.03rem;
	-webkit-border-radius: 0.03rem;
	border-radius: 0.03rem;
	font-style: normal;
	position: relative;
	top: -1px;
	margin-left: 0.1rem;
}

.normal_span {
	border: 1px solid #285FC1;
	color: #285FC1;
}

.dormant_span {
	border: 1px solid #DDD;
	color: #DDD;
}

.assign_span {
	border: 1px solid #B80205;
	color: #B80205;
}


.cond_box ul.acct li .cont{
	border-top: 0 none;
	padding-left: 0;
}




/*-- 协议 --*/
.xy_box .title {
	padding: 0.15rem 0;
	margin: 0 0.15rem;
	text-align: center;
	line-height: 0.26rem;
	font-weight: normal;
	color: #000;
	font-size: 0.18rem;
	border-bottom: 1px solid #eee;
}

.xy_cont {
	padding: 0.15rem;
	color: #000;
	font-size: 0.16rem;
	line-height: 0.26rem;
}

.rule_check{
	margin: 0.15rem;
	padding-left: 0.3rem;
	position: relative;
	line-height: 0.2rem;
	font-size: 0.14rem;
	color: #999;
}

.rule_check a{
	color: #285FC1 ;
}

.rule_check .icon_check{
	padding-left: 0;
	width: 0.28rem;
	height: 0.28rem;
	position: absolute;
	top: -0.04rem;
	left: -0.04rem;
}

.rule_check .icon_check:before{
	top: 50%;
	left: 50%;
	margin: -0.09rem 0 0 -0.09rem;
}

.xy_cont p {
	padding: 0.03rem 0;
	text-indent: 0.3rem;
}

.bottom_check{
	padding-top: 0.15rem;
}

.bottom_check .rule_check{
	margin-top: 0;
}
.bottom_check .ce_btn{
	padding-top: 0;
}

.white_bg .bottom_check{
	border-top: 0.05rem solid #f9f9f9;
}

.rule_list{
	background: #fff;
	padding-left: 0.15rem;
	margin: 0.05rem 0;
}

.rule_list li{
	border-bottom: 1px solid #eee;
	position: relative;
}

.rule_list li a{
	display: block;
	padding: 0.11rem 0.45rem 0.11rem 0;
	line-height: 0.24rem;
	font-size: 0.16rem;
	color: #333;
	position: relative;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}

.rule_list li a:after{
	content: "";
	width: 0.06rem;
	height: 0.12rem;
	background: url(../images/arrow01.png) no-repeat center;
	background-size: 100%;
	position: absolute;
	top: 50%;
	margin-top: -0.06rem;
	right: 0.15rem;
}

.rule_list li.unread a:after{
	background-image: url(../images/arrow01.png);
}

.rule_list li.read a:after{
	background-image: url(../images/arrow04.png);
}

.rule_list li.unread a:before{
	content: "";
	width: 0.08rem;
	height: 0.08rem;
	background: #EA3437;
	-moz-border-radius: 100%;
	-webkit-border-radius: 100%;
	border-radius: 100%;
	position: absolute;
	top: 50%;
	margin-top: -0.04rem;
	right: 0.32rem;
}

.rule_list li:last-child{
	border-bottom: 0 none;
}

.bottom_check .tips{
	padding: 0 0.15rem;
	font-size: 0.13rem;
	line-height: 0.21rem;
	color: #999;
	margin-bottom: 0.1rem;
}

.rule_lylist{
	text-align: left;
	padding: 0.05rem 0;
}
.rule_lylist li a{
	display: block;
	padding: 0.05rem 0;
	line-height: 0.24rem;
	font-size: 0.14rem;
	color: #285FC1;
}


/*-- 上传身份证 --*/
.info_ctpage{
	min-height: 100%;
	padding-bottom: 0.38rem;
	position: relative;
}

.info_ctpage .title{
	background: #fff;
	border-bottom: 1px solid #eee;
	padding: 0.12rem 0.15rem;
	line-height: 0.22rem;
	font-size: 0.13rem;
	color: #666;
	font-weight: normal;
}

.upload_cont{
	background: #fff;
	margin-bottom: 0.05rem;
	padding-top: 0.23rem;
	padding-bottom: 0.01rem;
}

.upload_pic{
	margin: 0 12% 0.23rem;
	position: relative;
}

.upload_pic .pic{
	min-height: 1rem;
	-moz-border-radius: 0.04rem;
	-webkit-border-radius: 0.04rem;
	border-radius: 0.04rem;
	overflow: hidden;
}

.upload_pic .pic img{
	display: block;
	width: 100%;
}

.upload_pic .info{
	width: 100%;
	height: 100%;
	-moz-border-radius: 0.04rem;
	-webkit-border-radius: 0.04rem;
	border-radius: 0.04rem;
	overflow: hidden;
	position: absolute;
	top: 0;
	left: 0;
	z-index: 50;
}

.upload_pic .info .btn{
	display: block;
	width: 100%;
	padding-top: 0.84rem;
	padding-bottom: 0.2rem;
	line-height: 0.18rem;
	text-align: center;
	font-size: 0.12rem;
	color: #B80205;
	position: absolute;
	top: 50%;
	left: 0;
	-webkit-transform: translateY(-50%);
	transform: translateY(-50%);
}

.upload_pic .info .btn:before{
	content: "";
	width: 0.54rem;
	height: 0.54rem;
	background: rgba(184,3,5,0.95) url(../images/icon_photo.png) no-repeat center;
	background-size: 0.26rem;
	box-shadow: 0 0 0.08rem rgba(184,3,5, 0.3);
	-moz-border-radius: 100%;
	-webkit-border-radius: 100%;
	border-radius: 100%;
	position: absolute;
	top: 0.2rem;
	left: 50%;
	margin-left: -0.27rem;
}

.upload_pic .info .ret_btn{
	width: 0.48rem;
	height: 0.48rem;
	background: url(../images/ret_btn.png) no-repeat center;
	background-size: 100%;
	position: absolute;
	top: 0;
	right: 0;
}

.photo_tips{
	width: 100%;
	text-align: center;
	line-height: 0.18rem;
	padding: 0.1rem;
	font-size: 0.12rem;
	color: #D2D2D2;
	position: absolute;
	bottom: 0;
	left: 0;
}

.photo_tips span{
	padding-left: 0.16rem;
	position: relative;
}

.photo_tips span:before{
	content: "";
	width: 0.11rem;
	height: 0.14rem;
	background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABYAAAAcCAYAAABlL09dAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAAEnQAABJ0Ad5mH3gAAAEUSURBVEhL7ZU9DoJAEIW5iTW1NbW9tSeht7a29hLW1lb8hv8DeAh9A7s4LMMmqDGa8JKXuMzMt7OzAZ3/U9M09yAIPmbwLi1YCr7jLMvuvwHGEc8ociVXVXXUeVjPA6P40BYIQszXeQt4AXdawL1EcJIkI4hkG5hed5bXgeu6HkEk28DUpc6jTdqH/Bg2T4HNeqw9FRruOGUJXJblOoqiPgfd3lSoE4p2HCLZBBdFsYnjuI/TBlivVPgpzPrEQaY5GLn7MAz7GP2m5lR4LPq/4jBuAmNk9FGXYr5CTIsAvBttzLPtjD+jUVg7NQXINk3TAcR0nuf0MriqZJ4wy6sJpEui+1AprwtH9TB7DUzEm/+eHOcBJu4BfZQEbKsAAAAASUVORK5CYII=") no-repeat center;
	background-size: 100%;
	position: absolute;
	top: 50%;
	margin-top: -0.07rem;
	left: 0;
}

.photo_tipbox{
	padding-top: 0.1rem;
}

.photo_tipbox img{
	display: block;
	width: 100%;
}

.upload_cont .ui.field:last-child .teare01{
	border-bottom: 0 none;
}

.upload_btn {
	background: #F9F9F9;
	width: 100%;
	position: fixed;
	bottom: 0;
	left: 0;
	z-index: 6000;
}

.upload_btn ul li {
	border-bottom: 1px solid #eee;
}

.upload_btn ul li:last-child {
	border-bottom: 0 none;
}

.upload_btn ul li a {
	display: block;
	height: 0.46rem;
	line-height: 0.46rem;
	text-align: center;
	background: #fff;
	font-size: 0.16rem;
	color: #285FC1;
}

.cancel_btn {
	padding-top: 0.05rem;
}

.cancel_btn a {
	display: block;
	height: 0.46rem;
	line-height: 0.46rem;
	text-align: center;
	background: #fff;
	font-size: 0.16rem;
	color: #666;
}




/*-- 视频认证 --*/
.video_zbbox{
	background: #fff;
	padding: 0.23rem 0.2rem;
}

.video_zbbox h5{
	text-align: center;
	font-weight: normal;
	font-size: 0.13rem;
	color: #999;
	line-height: 0.2rem;
	margin-bottom: 0.25rem;
}

.video_zbbox ul{
	padding: 0 0.15rem;
	overflow: hidden;
}

.video_zbbox ul li{
	width: 50%;
	float: left;
	text-align: center;
	padding: 0.1rem 0;
	font-size: 0.13rem;
	line-height: 0.2rem;
	color: #222;
}

.video_zbbox ul li .icon{
	display: block;
	width: 0.76rem;
	height: 0.76rem;
	margin: 0 auto 0.12rem;
}

.video_zbbox ul li .icon img{
	display: block;
	width: 100%;
	height: 100%;
}

.video_zbbox ul li em{
	display: block;
	color: #285FC1;
}


/*-- 驳回 --*/
.reject_info{
	background: #fff;
	margin-bottom: 0.1rem;
	padding: 0.1rem 0.2rem;
}

.reject_info .item{
	padding: 0.1rem 0 0.1rem 0.3rem;
	position: relative;
}

.reject_info .item:before{
	content: "";
	width: 0.17rem;
	height: 0.17rem;
	background: url(../images/warn_ic03.png) no-repeat center;
	background-size: 100%;
	position: absolute;
	top: 0.12rem;
	left: 0;
}

.reject_info .item h5{
	font-size: 0.16rem;
	line-height: 0.22rem;
	color: #B80205;
	font-weight: normal;
	margin-bottom: 0.03rem;
}

.reject_info .item p{
	font-size: 0.13rem;
	line-height: 0.2rem;
	color: #999;
}


/*-- 适当性 --*/

.approp_layer{
	width: 92%;
	background: #fff;
	height: 92%;
	position: absolute;
	top: 4%;
	left: 4%;
	z-index: 9999;
	overflow: auto;
	-moz-border-radius: 0.03rem;
    -webkit-border-radius: 0.03rem;
    border-radius: 0.03rem;
}

.approp_result{
	padding: 0.15rem;
	overflow: hidden;
	text-align: center;
}

.approp_result h5{
	display: inline-block;
	padding-left: 0.6rem;
	position: relative;
	line-height: 0.55rem;
	font-size: 0.2rem;
	font-weight: normal;
	color: #000;
}

.approp_result .icon{
	display: block;
	width: 0.55rem;
	height: 0.55rem;
	position: absolute;
	top: 0;
	left: -0.03rem;
}

.approp_result .icon img{
	display: block;
	width: 100%;
	height: 100%;
}

.approp_info ul{
	border-top: 1px solid #eee;
	padding-left: 0.15rem;
	background: #fff;
}

.approp_info ul li{
	border-bottom: 1px solid #eee;
	position: relative;
	font-size: 0.16rem;
	line-height: 0.24rem;
	padding: 0.1rem 0.15rem 0.1rem 1.2rem;
}

.approp_info ul li .tit{
	position: absolute;
	top: 50%;
	margin-top: -0.12rem;
	left: 0;
	color: #999;
}

.approp_info ul li p{
	text-align: right;
	min-height: 0.24rem;
}

.approp_info.spel ul{
	border-top: 0 none;
}

.approp_info.spel ul li:last-child{
	border-bottom: 0 none;
}

.approp_tips{
	padding: 0.1rem 0.15rem;
	border-bottom: 1px solid #eee;
	font-size: 0.14rem;
	color: #999;
	line-height: 0.2rem;
}

.approp_tips p{
	padding: 0.04rem 0;
}

.ablue{
	color: #285FC1 !important;
}

.ared{
	color: #BA0105 !important;
}

.approp_layer .rule_check{
	margin-bottom: 0;
}


/*-- 风险测评、知识测评 --*/
.test_result{
	background: #fff;
	margin-bottom: 0.1rem;
}

.test_level{
	padding: 0.44rem 0.15rem 0.2rem;
	text-align: center;
}

.test_canvas{
	width: 2.02rem;
	height: 1.7rem;
	padding-top: 0.2rem;
	margin: 0 auto;
	position: relative;
	background-repeat: no-repeat;
	background-position: center top;
	background-size: 2.02rem 1.52rem;
	overflow: hidden;
}

.test_level.risk .test_canvas{
	background-image: url(../images/test_bg01.png);
}

.test_level.knowledge .test_canvas{
	background-image: url(../images/test_bg02.png);
}

.test_canvas canvas {
	width: 1.4rem;
	height: 1.4rem;
	margin: 0 auto;
	display: block;
}

.test_canvas .info{
	width: 100%;
	padding-top: 0.75rem;
	text-align: center;
	position: absolute;
	top: 0;
	left: 0;
	z-index: 50;
}

.test_canvas .info strong{
	display: block;
	font-weight: 500;
	height: 0.34rem;
	line-height: 0.34rem;
	font-size: 0.24rem;
	color: #B80205;
	margin-bottom: 0.03rem;
}

.test_canvas .info strong.error{
	color: #F4E1C1;
}

.test_canvas .info span{
	display: block;
	height: 0.18rem;
	line-height: 0.18rem;
	font-size: 0.12rem;
	color: #999;
}

.test_level.knowledge .test_canvas .info strong{
	font-size: 0.34rem;
}

.test_level p{
	font-size: 0.14rem;
	color: #999;
	line-height: 0.2rem;
}

.test_level .state{
	color: #999;
}

.test_level .state.warn,
.test_level .state.error{
	padding-left: 0.26rem;
	position: relative;
}

.test_level .state.warn{
	color: #D2B380;
}

.test_level .state.error{
	color: #B80205;
}

.test_level .state.warn:before,
.test_level .state.error:before{
	content: "";
	width: 0.17rem;
	height: 0.17rem;
	background-repeat: no-repeat;
	background-position: center;
	background-size: 100%;
	position: absolute;
	top: 50%;
	margin-top: -0.09rem;
	left: 0;
}

.test_level .state.warn:before{
	background-image: url(../images/warn_ic01.png);
}

.test_level .state.error:before{
	background-image: url(../images/warn_ic02.png);
}

.test_level h5{
	font-size: 0.16rem;
	line-height: 0.24rem;
	padding-bottom: 0.05rem;
	color: #999;
	font-weight: normal;
}

.test_level h5.error{
	color: #B80205;
}


.test_result .approp_tips{
	border-bottom: 0 none;
	border-top: 1px solid #eee;
	padding-left: 0;
	margin-left: 0.15rem;
}

.re_testbtn{
	display: inline-block;
	line-height: 0.28rem;
	padding: 0 0.16rem;
	font-size: 0.15rem;
	color: #B80205;
	border: 1px solid #E87173;
	background: #FFF5F5;
	-moz-border-radius: 0.3rem;
	-webkit-border-radius: 0.3rem;
	border-radius: 0.3rem;
}

.test_infobox{
	background: #fff;
	padding: 0.12rem 0.15rem 0.18rem;
}

.test_infobox .item{
	height: 0.04rem;
	font-size: 0;
	line-height: 0;
	background: #F9F9F9;
	-moz-border-radius: 0.04rem;
	-webkit-border-radius: 0.04rem;
	border-radius: 0.04rem;
	overflow: hidden;
}

.test_infobox .item b{
	display: block;
	height: 0.04rem;
	background: #D2B380;
	-moz-border-radius: 0.04rem;
	-webkit-border-radius: 0.04rem;
	border-radius: 0.04rem;
}

.test_infobox p{
	height: 0.2rem;
	line-height: 0.2rem;
	margin-top: 0.15rem;
	text-align: center;
	font-size: 0.14rem;
	color: #999;
}

.test_infobox a{
	font-size: 0.16rem;
	color: #B80205;
	padding: 0 0.15rem;
	line-height: 0.36rem;
	position: absolute;
	bottom: 0.1rem;
	z-index: 50;
}

.test_infobox a.prev{
	left: 0;
}

.test_infobox a.next{
	right: 0;
}

.test_box {
	background: #fff;
}

.test_box h5 {
	padding: 0.15rem;
	margin-bottom: 0.05rem;
	line-height: 0.28rem;
	font-size: 0.18rem;
	color: #333;
	font-weight: 500;
}

.input_list {
	padding-left: 0.15rem;
	overflow: hidden;
}

.input_list li {
	border-top: 1px solid #eee;
}

.input_list li:first-child{
	border-top: 0 none;
}

.input_list li .icon_radio {
	color: #666;
	display: block;
	padding: 0.11rem 0.15rem 0.11rem 0.32rem;
	font-size: 0.16rem;
}

.input_list li .icon_radio:before {
	top: 0.14rem;
}

.input_list li .icon_radio.checked{
	color: #333;
}

.input_list.multiple li .icon_radio.checked{
	background: #fff;
}

.input_list.multiple li .icon_radio.checked:before{
	border-color: #d2b380;
    background: #d2b380 url(../images/check_ic01.png) no-repeat center;
    background-size: 0.18rem;
}





/*-- 结果页 --*/

.result_main {
	background: #fff;
	padding: 0.3rem;
	margin-bottom: 0.05rem;
}

.result_main .icon_ok,
.result_main .icon_error,
.result_main .icon_ing {
	display: block;
	width: 0.8rem;
	height: 0.8rem;
	margin: 0 auto 0.2rem;
}

.result_main .icon_ok {
	background: url(../images/icon_ok.png) no-repeat center;
	background-size: 100%;
}

.result_main .icon_error {
	background: url(../images/icon_error.png) no-repeat center;
	background-size: 100%;
}

.result_main .icon_ing {
	background: url(../images/icon_ing.png) no-repeat center;
	background-size: 100%;
}

.result_main h5 {
	text-align: center;
	font-weight: normal;
	font-size: 0.2rem;
	line-height: 0.3rem;
	padding-bottom: 0.05rem;
	color: #000;
}

.result_main p {
	font-size: 0.14rem;
	color: #999;
	line-height: 0.2rem;
}

.result_main p.center {
	text-align: center;
}

.result_sfinfo ul {
	background: #fff;
	padding-left: 0.15rem;
}

.result_sfinfo ul li {
	border-bottom: 1px solid #eee;
	position: relative;
	min-height: 0.47rem;
	padding: 0.11rem 0.15rem 0.11rem 0;
	line-height: 0.24rem;
	font-size: 0.16rem;
}

.result_sfinfo ul li:last-child {
	border-bottom: 0 none;
}

.result_sfinfo ul li .tit {
	position: absolute;
	width: 2.2rem;
	top: 0.11rem;
	left: 0;
	color: #000;
}

.result_sfinfo ul li p {
	text-align: right;
	color: #999;
	margin-left: 0.9rem;
}

.fail_reason{
	background: #fff;
	padding: 0.1rem 0.15rem 0.1rem 1rem;
	line-height: 0.24rem;
	position: relative;
	font-size: 0.16rem;
	margin-bottom: 0.05rem;
}

.fail_reason span{
	color: #999;
	position: absolute;
	top: 0.1rem;
	left: 0.15rem;
}

.fail_reason p{
	height: 0.24rem;
	padding-right: 0.18rem;
	position: relative;
	color: #333;
	text-align: right;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}

.fail_reason p:after{
	content: "";
	width: 0.06rem;
	height: 0.12rem;
	background: url(../images/arrow04.png) no-repeat center;
	background-size: 100%;
	position: absolute;
	top: 50%;
	margin-top: -0.06rem;
	right: 0;
}

.result_sfinfo ul li .status {
	color: #999;
}

.result_sfinfo ul li .status.ok{
	color: #D2B380;
}

.result_sfinfo ul li .status.ing {
	color: #999;
}

.result_sfinfo ul li .status.error {
	color: #B80205;
}

.result_sfinfo ul li .fail_reason{
	border-top: 1px dashed #eee;
	padding-right: 0;
	top: 0.1rem;
	margin-bottom: 0;
}

.result_sfinfo ul li .fail_reason span{
	left: 0;
}

.result_sfinfo ul li .fail_reason p{
	color: #333;
}

.result_tips{
	padding: 0 0.15rem;
	margin: 0.1rem 0;
}

.result_tips dt{
	padding: 0.05rem 0;
	line-height: 0.24rem;
	font-size: 0.14rem;
	color: #000;
}

.result_tips dt span{
	display: inline-block;
	position: relative;
}

.result_tips dt span:after{
	display: block;
	content: "";
	width: 0.26rem;
	height: 0.04rem;
	background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADIAAAAICAYAAAC73qx6AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAAEnQAABJ0Ad5mH3gAAAFxSURBVDhPzZO/S8NAFMdPxFHwD3BTERzdBQcR/ANcBRc3F3fhLvFnF+eOuul6SSsugjpa0LvGFBehiDgU2lUpPd/38qaQgoVA++BL2ty77/dzyYtIYrVlIvlptPwyWl2ZSG2nT+ezYkILbGD0rGAmdpxB0J82yeX0Y7W8o6b9RAdL7DG2AgNYPBOx5VihtuC3kV/Iq2MjGdP10NbCzY/7iznOKL3gjQyflWV2mGG46Ax+tOik34UNwzXgB/BI10urlbQ62LE6XH+rH62m9WD5vXY2Dyj3XJ2B8Bv3sIYe9GZ7lIQHe8FzwBn/Etj9aKGcu5lOtFqjuavQQlq0YZKUMaoKmMHuD1FUzduTRROrA57JXt5oDOp5FmICG2OOVs65qWbteMXEcvdVyyq9/hcy7ueCylQfGT6LMpENBsYptzDz9EEumCjcMFGwR0/rlHRNAA8E0iC1eN67pF9Wl++1SA30Yg/2wgNe8IQ3x4xQQvwBawzOq/9QuKUAAAAASUVORK5CYII=") no-repeat center;
	background-size: 100% 100%;
	position: absolute;
	bottom: -0.03rem;
	left: 50%;
	margin-left: -0.13rem;
}

.result_tips dd{
	font-size: 0.13rem;
	line-height: 0.21rem;
	color: #999;
	padding: 0.05rem 0;
}




/*-- 选择账户 --*/
.agree_main .title {
	padding: 0.15rem 0.15rem 0.05rem;
	line-height: 0.24rem;
	font-weight: normal;
	font-size: 0.13rem;
	color: #999;
}

.account_list {
	background: #fff;
	padding-left: 0.15rem;
}

.account_list li {
	border-bottom: 1px solid #eee;
	position: relative;
}

.account_list li:last-child {
	border-bottom: 0 none;
}

.account_list li p {
	min-height: 0.46rem;
	line-height: 0.46rem;
	font-size: 0.16rem;
	color: #000;
}

.account_list li .status {
	position: absolute;
	top: 0.11rem;
	right: 0.15rem;
	font-size: 0.13rem;
	color: #999;
	z-index: 50;
}

.account_list li .status.ok {
	color: #999;
}

.account_list li .icon_radio {
	display: block;
	padding: 0.11rem 0.4rem 0.11rem 0;
}

.account_list li .icon_radio:before {
	top: 50%;
	margin-top: -0.09rem;
	left: auto;
	right: 0.15rem;
}

.account_list li .icon_radio.checked:before{
	border-color: #d2b380;
    background: #d2b380 url(../images/check_ic01.png) no-repeat center;
    background-size: 0.18rem;
}

.account_list li.disable .icon_radio:before{
	display: none;
}




/*-- switch选择 --*/
.ui.switch {
	width: 0.52rem;
	height: 0.32rem;
	overflow: visible;
}

.ui.switch>input[type=checkbox] {
	cursor: default;
	width: 0.52rem;
	height: 0.32rem;
}

.ui.switch>.ui.switch-inner {
	height: 0.32rem;
	background: #eeeeee;
	transition: all 0.1s ease-in 0s;
	-moz-transition: all 0.1s ease-in 0s;
	-webkit-transition: all 0.1s ease-in 0s;
}

.ui.switch>.ui.switch-inner:before{
	content: "";
	width: 0.02rem;
	height: 0.12rem;
	background: #fff;
	position: absolute;
	top: 50%;
	margin-top: -0.06rem;
	left: 0.12rem;
}

.ui.switch>.ui.switch-inner:after{
	content: "";
	width: 0.08rem;
	height: 0.08rem;
	border: 1px solid #a3a3a3;
	-moz-border-radius: 100%;
	-webkit-border-radius: 100%;
	border-radius: 100%;
	position: absolute;
	top: 50%;
	margin-top: -0.05rem;
	right: 0.07rem;
}

.ui.switch>.ui.switch-inner>.ui.switch-arrow {
	width: 0.28rem;
	height: 0.28rem;
	top: 0.02rem;
	right: 0.22rem;
	box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
	z-index: 50;
}

.ui.switch>input[type=checkbox]:checked+.ui.switch-inner,
.ui.switch.text>input[type=checkbox]:checked+.ui.switch-inner {
	background: #d2b380;
}

.ui.switch>input[type=checkbox]:checked+.ui.switch-inner .ui.switch-arrow,
.ui.switch.text>input[type=checkbox]:checked+.ui.switch-inner .ui.switch-arrow {
	right: 0.02rem;
}

.ui.switch.text.disable>input[type=checkbox]:checked+.ui.switch-inner {
	opacity: 0.5;
	-moz-opacity: 0.5;
}






/*-- 登录 --*/

.icon_close{
	width: 0.52rem;
	height: 0.52rem;
	background: url(../images/icon_close.png) no-repeat center;
	background-size: 0.2rem;
	position: absolute;
	top: 0;
	left: 0;
}

.login_box {
	padding: 20% 0 1.15rem;
	min-height: 100%;
	position: relative;
}

.login_logo {
	height: 0.76rem;
	margin-bottom: 15%;
}

.login_logo img {
	display: block;
	height: 100%;
	margin: 0 auto;
}

.login_form {
	background: #fff;
	padding: 0 0.3rem;
}


.login_form .icon_eye {
	right: 0;
}

.login_form .ui.field > .ui.dropdown > strong:after{
	right: 0.02rem;
}

.login_box .ce_btn {
	margin-top: 8%;
	padding: 0.15rem 0.3rem;
}

.login_bottom {
	padding: 0.1rem 0.3rem;
	line-height: 0.24rem;
}

.login_bottom a {
	font-size: 0.14rem;
	color: #B80205;
}

.login_bottom>a.fr {
	float: right;
}

.no_acctbox {
	background: #F1F1F1;
	width: 100%;
	position: absolute;
	bottom: 0;
	left: 0;
	padding: 0.2rem 0;
	text-align: center;
}

.no_acctbox .link{
	display: inline-block;
	padding-top: 0.48rem;
	line-height: 0.2rem;
	color: #D2B380;
	position: relative;
}

.no_acctbox .link:before{
	content: "";
	width: 0.42rem;
	height: 0.42rem;
	background: url(../images/noacct_link.png) no-repeat center;
	background-size: 100%;
	position: absolute;
	top: 0;
	left: 50%;
	margin-left: -0.21rem;
}

.code_layer{
	width: 2.8rem;
    background: rgba(255,255,255,0.95);
    -moz-border-radius: 0.04rem;
    -webkit-border-radius: 0.04rem;
    border-radius: 0.04rem;
    position: fixed;
    top: 50%;
    left: 50%;
    margin-top: -1.39rem;
    margin-left: -1.4rem;
    z-index: 9999;
}

.code_lytit{
	padding: 0.15rem 0 0;
	line-height: 0.38rem;
	text-align: center;
	position: relative;
}

.code_lytit h3{
	font-size: 0.16rem;
	font-weight: normal;
	color: #000;
}

.code_lytit h3.error{
	color: #BA0105;
}

.code_lytit .close{
	display: block;
    width: 0.3rem;
    height: 0.3rem;
    background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACoAAAAqCAYAAADFw8lbAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAALwSURBVFhH5ZlPattAGMVT6KbQLtsLGAq9Qlftoj1A98kVCr6Aob2JSzZe5AreZOe/YIPI1qusUoi3oer7TZ+oSBRJI8uNRB8MUd58fx6fpJlP45P/ArPZbLBYLIb6ez6fz6caVxp7D66nzNlmYLd/g/V6/UZJv0nERiONHBt8ieFw7WO73b5UopEG1coS32j8WC6XZ6vV6oMEvMUuSZJXXMMxh41tMz9ijLB1+HagW/dFga+zRPr/QpX5mKbpc5tUAlt88M3iEJPYNmkOBX+mYFTxlwNfqkLvPd0YxCCWYxJ7RC5Px2G3271QgImD3Wl89VRrIKZjk2NCTk/VgyuZifypCnzyVOsgNjmcaxJVWTlwu4NIPUPvTB8N5CCXc45Ml4OHW8Y8N3fHrOR96EX7TE5yV75gXoKyt7v1Z7IK5HTu69KlSwbZLb809QCq8muG/40Gi32ZP7mtofgRIIAmw2KuQIVLEAk0v2U0EVvHn9xo0NgX7mCa+I6Bno8LUw+QS0SgKLExvmjAju3W1F9oIuzd7B6mCtFEbKwPGmy7MfUHmhh44oatzvSjiEkcKxKgQbahN0Cb6VDqoQONTVWijoAmIjPInkaGR3FoKlSUfhLy1FQtlAk5RCSQPV0XFT03FdTT9Ka0ZaZqo0jQoSIBWuw/NRWE0o2n9JCmopAXpruSqAoJ13BNRAK0OMaVqSA0rJ+HNLIIQqSDc8uSpiKBd0li7U0FobeQdOamooGoXCVDZQ8RihbHujXV7q1HbK6yrd/6frxMLAGQCnhmqhbKBB0qFi34os1Uvxb8fmyhQGT3mxIgsh9tHk2qJo/aODtHqT+50aBR3DgDTVZ+iuDcRGSGKn9yW8PjX6O9+bgDej6e5HOZXOQkNxpMl0PG3T+AAByryKH7RzqgF4dkGVzZbh875uEXrLsHuXl46aK694/Gx0p2SlumNTIcjTO4hlP16ILGts38jnM0ngcLtpKw3Xbzx4YiKGl3f755Gpyc/AaBzG77YxehkQAAAABJRU5ErkJggg==") no-repeat center;
    background-size: 0.2rem 0.2rem;
    position: absolute;
    top: 0;
    right: 0;
}

.code_lycont{
	padding: 0.1rem 0.1rem 0.2rem;
}

.code_pic{
	width: 1.9rem;
	height: 0.66rem;
	margin: 0 auto 0.2rem;
	background: #FFFFFF;
	border-radius: 0.06rem;
	overflow: hidden;
	position: relative;
}

.code_pic img{
	display: block;
	width: 100%;
	height: 100%;
	border-radius: 0.06rem;
}

.code_pic:before{
	display: block;
	content: "";
	width: 100%;
	height: 100%;
	box-shadow: inset 0 0.01rem 0.06rem 0 rgba(0,0,0,0.1);
	border-radius: 0.06rem;
	position: absolute;
	top: 0;
	left: 0;
	z-index: 5;
}

.code_pic:after{
	display: block;
	content: "";
	width: 0.1rem;
	height: 0.1rem;
	background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAAEnQAABJ0Ad5mH3gAAAEuSURBVDhPvZQ/TsMwFMbDDmdgQtSRkJiRytKD2MqCxCF6BXYu0IkN1KkStlL1Ah1ygM4d0gOE99kfhELipInUn/Qk63t//JznODkbSruZMvZ1om0hdqAV0OBjWDfKbG6Utp+SXMUMMYhlWjMqs48SvGdSOdFunur8Pn3eXnqTNbTg8zF7ZfIp04/BbggIgW6lsvU1Xf+ADzF10YZO62O6VVVVF5RbQcx3UeRSDuAjc7cy1tlfQqfh+EeD4jTR3ZxSb5DjC0oNShD91RDRvYhjIeudrN/pjhIG5U9XUPIFcccg1mbsku4oD1l+xZwDJT+QNxGkK7tIjX26y/Jbujr5VbCkNI7GI4+hcShDab02Q4he7FPp9et1cfLjEEMSP1jgx3o9X22w4LAHdhxJ8gVbzgM9ZsrEJAAAAABJRU5ErkJggg==") no-repeat center;
	background-size: 100% 100%;
	position: absolute;
	bottom: 0.06rem;
	right: 0.06rem;
}

.code_input{
	width: 1.52rem;
	height: 0.44rem;
	line-height: 0.44rem;
	position: relative;
	overflow: hidden;
	margin: 0 auto;
}

.code_input .t1{
	display: block;
	width: 200%;
	height: 0.44rem;
	line-height: 0.44rem;
	border: 0 none;
	opacity: 0;
	position: absolute;
	top: 0;
	left: -100%;
	z-index: 5;
}

.code_input ul{
	height: 0.44rem;
}

.code_input ul li{
	width: 25%;
	float: left;
	height: 0.44rem;
	position: relative;
}

.code_input ul li i{
	display: block;
	width: 0.16rem;
	height: 0.04rem;
	font-size: 0;
	line-height: 0;
	background: #285FC1;
	opacity: 0.3;
	position: absolute;
	top: 50%;
	left: 50%;
	margin-left: -0.08rem;
}

.code_input ul li.now i{
	opacity: 1;
}

.code_input ul li span{
	display: block;
	width: 100%;
	text-align: center;
	height: 0.44rem;
	line-height: 0.44rem;
	font-size: 0.3rem;
	color: #285FC1;
	font-weight: bold;
	position: absolute;
	top: 0;
	left: 0;
	display: none;
}

.code_input ul li.yet i{
	display: none;
}

.code_input ul li.yet span{
	display: block;
}

.code_lybtn{
	display: table;
	width: 100%;
	table-layout: fixed;
	border-top: 1px solid #eee;
}

.code_lybtn a{
	display: table-cell;
	height: 0.5rem;
	line-height: 0.5rem;
	text-align: center;
	font-size: 0.18rem;
	color: #285FC1;
}

.code_lybtn a.disable{
	color: #ccc;
}

.login_btn {
	position: relative;
}

.login_btn:before {
	display: inline-block;
	content: "";
	width: 0.16rem;
	height: 0.16rem;
	background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAPFSURBVFhHzZdZbA1RGMertpKWUqqhpQiJorZoBKFJLZUglqTpA4LaaaK2psT6QBAvkkYtSW2JFvEmIdFowgNKn7xQPEksTwQPElq///Q7173X3Lp3Eo1/8suZ+X/nO+ebc2fOzE0KqtbW1gxYYfQ3u/PEpDNgK2yB6WZHqK2trYcdBhMDpDN4GWyCIWZ7IlaI5wqYZbYnYt3xGuA7VJmduEieCrtgJ6xh4K4W6rAA6/8TfsBbsxMXkwywwVTEDphqIVfANogoAD8LPuG5Ak5bKJgYoAh2gwopZ/BU+bEK4LgW3OTvIcNCwcQAPUFL7VZhgXwKmMmxK8C7CWkzQZO7AjbKj0t0zgPdbIsYvK/ZnvAmgFuFHcR70+oxXAV6DL2rxL/IsfSD46cQfs90wS+B+1DNeeQTglkOB+EAVNGh0HWy5FLQKpRxnuwlRYmYLkBX/w2mmS2/gJxG87/CF1ho4XZhFNPpEK0rYj9shwmKE+vG8TDo5SXEEPFx9M2142y4BJo4fPK39BnqJThh6CrHg5Y4upBJ1i1ukaN75w2EJmZcPR1a/izr9qcIahOZDfvCCvHd6ToSudrEPoO76tuQb+G/i85pMA+KVJTZCYm8JeTrsYz8vf8rUeUUKtTjVRnGHvOKrFtgMYYe38fwTDCfHs+nHD+AUnU4Cid9OGH0sbESFrmD4GMUH8JoUae1cCoGlVQb2kwSFbm6oXWl72JQq07JHAyGnCiyNYCNFViMkcJYerzzo8gj1s26/RvZ5BHfEXFJlZGot6C23mKzExJ5feAeaDO6BmMt1LGYfCKdj0ANnIVzeBEvp3hEnpb5JbyC19Yeh0zrEikC2rcrIDQxXICjQX4rcnRvacIX0AKukOew0rq1C0OPSzW4ic/DGVjGQCnWZyDkeAkxRN9+9DkMxyBNHm0B3AKtRnghv8fiZBS4ic8z0Aba0JeMxfVWq4f5Zv8hYuuhCZrhmNluNZbDQ1AhzXjpFm4Xpl7Hq2lHmuXJknVFdXADKuTT6tWsff4KDDdvLjwhRzudipgs34nzXsT0Ked/H/iJhDkk1NFep1UBo+XT6nP9DuhO32x99UrXfaNV0NarPN+Pl7hEciqD6GfR0mvycgupAH0n3gUVsN1s+WPgEbluFUoslLgYRD9JPa2u/ipt6Hfj3LcAifO94FahgbxgOx7JNTb5TVhstifO9UXsW4AKxdM/oyaOGyHYls4AS0G/4yGIeCnhxyxAwhsN+kjNMyuYmNh3+Rh4HbgCNpndeaKwXCa+DLo3RpidoJKSfgHlHKOK70luUQAAAABJRU5ErkJggg==") no-repeat center;
	background-size: 0.16rem;
	position: relative;
	top: 0.02rem;
	margin-right: 0.1rem;
	-webkit-animation: allrotate 2s infinite linear;
	animation: allrotate 2s infinite linear;
}

.login_success:before{
	display: inline-block;
	content: "";
	width: 0.18rem;
	height: 0.18rem;
	background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAkCAYAAADhAJiYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAAEnQAABJ0Ad5mH3gAAAFSSURBVFhH7ZY9bsJAEEbdcIFUNOnoXKTKXdJyg5whLS0NBRVVrkDLASIuQJOCkpLOzhv2yyqOsWXIaixF+6TRsvOz84GG1RaZTOY/UlXVDHuXzeQeh7qup4j4ZL3A5w+F/KH/BAG7ICXA/qSwPzRfSkcE35vCvtB4Lg0RfFuWiVL8oPEzdg4yAuwP2INS/KB3Y4gNE4c9KcUPereG2MD3opThUDc10/YuaHxtiBcKD4eiV9XbAXf9C6hLN8QUnsIRAfY3fSvy0w4xhXudE8G3VLgXUtMPsRVjjV/JwNcripR0Q/wbiTrqzAi+lVJaEEszxF1wWNkhaq2UCD6fm7hH1EYpluN7E3OwvV8ag2rg27CMcxPToEvUteH/+xAPoUvUT4inG+Ih0PARO6h/A/yjPSdaomyP+T8nvpGoy43OesRKhcYDLXYzl7bKlclkMrdRFF8liglDCa7wFQAAAABJRU5ErkJggg==") no-repeat center;
	background-size: 100%;
	vertical-align: middle;
	margin-right: 0.1rem;
}

.login_select{
	background:#fff;
	width:100%;
	padding-left:0.15rem;
	position:absolute;
	top:0.44rem;
	left:0;
	z-index:2000;
	overflow:hidden;
	box-shadow: 0 0.04rem 0.05rem rgba(0,0,0,0.1);
}

.login_select ul li{
	border-bottom:1px solid #eee;
}

.login_select ul li:last-child{
	border-bottom: 0 none;
}

.login_select ul li span{
	display:block;
	line-height:0.24rem;
	padding: 0.11rem 0.15rem 0.11rem 0;
	font-size:0.16rem;
	color:#000;
	position:relative;
}

.login_select ul li.active span{
	color:#B80205;
}

.login_select ul li.active span:after{
	display:block;
	content:"";
	width:0.18rem;
	height:0.18rem;
	position:absolute;
	top:50%;
	margin-top:-0.09rem;
	right:0.1rem;
	background:url(../images/icon_active.png) no-repeat center;
	background-size:0.18rem;
}




/*-- 首页 --*/
.icon_record{
	width: 0.5rem;
	height: 0.44rem;
	background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAAEnQAABJ0Ad5mH3gAAAILSURBVFhH7Zi7SgNRFEUTfKEogp2FCIIgCIJgYeOH+AH5Bgu/yMLCoIWFhWCRfxAEEURERExlYeLa41aEeThz7yQ+yILDnXPPuXsfxskI0/iT9Pv9Zq/XaxEdokseDTrPxLYtwkFkkTizbt08ob1lq+ogoDuXDMd6Q+wSiy4Hg9wYOgfWfSQ2XaoGB1tfhlvwdjDSEr4eR/NQOesDsZE0VYFDHQvseisKaQmnyifQPtIe6z2x7lI5OJD8IFij/6xCWsJpAukk+ifaZ71jWXPpe3RIOI3Gcik9BpsiTlVjvSVWXSomUQOn0VguU4+hponPHyTLikv5qFk4jcZyuXqUZhjuXD2s18SyS9moUTiNBsNb6bHmPtOUZ6lfuO+KWHIpjZqE02gwa0uPdc9bmVCfIz7eIJfeTqMG4TQazHaIV+KF2COK7uT8u3uBv+u1DSgYal9DWjoX9fpyuAMKBtSdbBPJM5mF+nw5/AHLYvvRgMHY/h8PyEOe/JMvg3p9rDQ+OrQBj32sND4aPuCgsf1owGBsPxowGNv/4wGrvGbyKHo/uuXHB8x9P7olfMBBY/vRgMHY/g8PyANc66ePKsjT3l1vpaFY68ejKsjT3h1vpaFY6+e3sshLnvZueTsN9YF8wMxD2vb4GE7eTZez8aFBfQLORZ7y9hjF0F/7R/QspG0PPVrFd+530mi8AUq5+04bCZ0XAAAAAElFTkSuQmCC") no-repeat center;
	background-size: 0.2rem;
	position: absolute;
	top: 0;
	right: 0;
	z-index: 50;
}

.banner_box{
	padding: 0 0.1rem;
	background: url(../images/home_bg.png) no-repeat center bottom;
	background-size: 100% auto;
	margin-bottom: 0.1rem;
}

.home_banner {
	overflow: hidden;
	margin-top: 0.12rem;
	-moz-border-radius: 0.04rem;
	-webkit-border-radius: 0.04rem;
	border-radius: 0.04rem;
	position: relative;
	width: 100%;
}
.home_banner img{
	display: block;
	width: 100%;
	min-height: 0.5rem;
}

.home_nav{
	padding: 0.1rem;
}

.home_nav ul {
	background: #fff;
	padding: 0.03rem 0 0.05rem;
	-moz-border-radius: 0.04rem;
	-webkit-border-radius: 0.04rem;
	border-radius: 0.04rem;
	box-shadow: 0 0 0.07rem 0 rgba(0,0,0,0.05);
	overflow: hidden;
}

.home_nav ul li {
	width: 25%;
	float: left;
	padding: 0.1rem 0;
	text-align: center;
	position: relative;
}

.home_nav ul li .icon{
	display: block;
	width: 0.5rem;
	height: 0.5rem;
	padding: 0.05rem;
	background: #fff;
	-moz-border-radius: 0.08rem;
	-webkit-border-radius: 0.08rem;
	border-radius: 0.08rem;
	box-shadow: 0 0 0.07rem 0 rgba(0,0,0,0.05);
	overflow: hidden;
	margin: 0 auto;
}

.home_nav ul li .icon img{
	display: block;
	width: 100%;
	height: 100%;
}

.home_nav ul li p{
	font-size: 0.13rem;
	color: #333;
	line-height: 0.3rem;
	font-weight: 500;
}

.home_nav ul li span{
	display: inline-block;
	margin-top: 0.05rem;
	height: 0.2rem;
	line-height: 0.18rem;
	padding: 0 0.08rem;
	font-size: 0.1rem;
	color: #D84749;
	border: 1px solid #E87173;
	background: #FFF5F5;
	-moz-border-radius: 0.5rem;
	-webkit-border-radius: 0.5rem;
	border-radius: 0.5rem;
}

.home_nav ul li span.disable{
	line-height: 0.2rem;
	padding: 0;
	border: 0 none;
	position: relative;
	background: none;
	color: #999;
}

.home_nav ul li span.disable:before,
.home_nav ul li span.disable:after{
	content: "";
	width: 0.06rem;
	height: 1px;
	background: #999;
	position: absolute;
	top: 50%;
}

.home_nav ul li span.disable:before{
	left: -0.08rem;
}

.home_nav ul li span.disable:after{
	right: -0.08rem;
}

.home_nav ul li .new{
	width: 0.3rem;
	height: 0.15rem;
	background: url(../images/icon_new.png) no-repeat center;
	background-size: 100%;
	position: absolute;
	top: 0;
	left: 50%;
	margin-left: 0.12rem;
	z-index: 50;
}

.search_btn {
	display: block;
	width: 100%;
	height: 0.3rem;
	line-height: 0.3rem;
	border: 0 none;
	background: #fff;
	font-size: 0.14rem;
	color: #B80205;
	text-align: center;
	-moz-border-radius: 0.04rem;
	-webkit-border-radius: 0.04rem;
	border-radius: 0.04rem;
}

.search_btn span {
	padding-left: 0.24rem;
	position: relative;
}

.search_btn span:before {
	display: block;
	content: "";
	width: 0.16rem;
	height: 0.16rem;
	position: absolute;
	top: 50%;
	margin-top: -0.08rem;
	left: 0;
	background: url(../images/icon_search2.png) no-repeat center;
	background-size: 100%;
}

.nav_main{
	padding-bottom: 0.2rem;
}

.nav_list .title {
	padding: 0.2rem 0.1rem 0.08rem;
	line-height: 0.24rem;
	font-weight: 500;
	font-size: 0.17rem;
	color: #333;
}

.nav_list ul {
	background: #fff;
	padding: 0 0.1rem;
}

.nav_list ul li {
	border-bottom: 1px solid #eee;
	position: relative;
	padding: 0.12rem 0;
	margin-left: 0.67rem;
	line-height: 0.2rem;
	font-size: 0.13rem;
	color: #999;
}

.nav_list ul li .icon{
	display: block;
	width: 0.5rem;
	height: 0.5rem;
	padding: 0.05rem;
	background: #fff;
	-moz-border-radius: 0.08rem;
	-webkit-border-radius: 0.08rem;
	border-radius: 0.08rem;
	box-shadow: 0 0 0.07rem 0 rgba(0,0,0,0.05);
	overflow: hidden;
	position: absolute;
	top: 50%;
	margin-top: -0.25rem;
	left: -0.65rem;
}

.nav_list ul li .icon img {
	display: block;
	width: 100%;
	height: 100%;
}

.nav_list ul li h5 {
	font-weight: normal;
	color: #333;
	font-size: 0.16rem;
	line-height: 0.26rem;
}

.nav_list ul li .not_time{
	line-height: 0.2rem;
	font-size: 0.12rem;
	color: #999;
	padding: 0 0.08rem;
	position: absolute;
	top: 0.15rem;
	right: 0;
}

.nav_list ul li .not_time:before,
.nav_list ul li .not_time:after{
	content: "";
	width: 0.06rem;
	height: 1px;
	background: #999;
	position: absolute;
	top: 50%;
}

.nav_list ul li .not_time:before{
	left: 0;
}

.nav_list ul li .not_time:after{
	right: 0;
}

.nav_list ul li .state{
	font-size: 0.13rem;
	color: #999;
	line-height: 0.2rem;
	position: absolute;
	bottom: 0.12rem;
	right: 0.15rem;
}

.nav_list ul li .state.ok,
.nav_list ul li .state.ing{
	color: #999;
}

.nav_list ul li .state.error{
	color: #F3A91C;
}

.in_transit_tips{
	background: #fff;
	margin: 0 0.1rem;
	padding: 0.09rem 0.32rem 0.09rem 0.15rem;
	line-height: 0.3rem;
    -moz-border-radius: 0.04rem;
    -webkit-border-radius: 0.04rem;
    border-radius: 0.04rem;
    box-shadow: 0 0 0.07rem 0 rgba(0,0,0,0.05);
    overflow: hidden;
    position: relative;
}

.in_transit_tips:after{
	content: "";
	width: 0.08rem;
	height: 0.16rem;
	background: url(../images/arrow03.png) no-repeat center;
	background-size: 100%;
	position: absolute;
	top: 50%;
	margin-top: -0.08rem;
	right: 0.15rem;
}

.in_transit_tips p{
	font-size: 0.16rem;
	font-weight: 500;
	height: 0.3rem;
}

.in_transit_tips .list{
	height: 0.3rem;
	position: absolute;
	top: 0.09rem;
	right: 0.32rem;
}

.in_transit_tips .list a{
	float: left;
	width: 0.3rem;
	height: 0.3rem;
	margin-right: 0.03rem;
}

.in_transit_tips .list a img{
	display: block;
	width: 100%;
	height: 100%;
}

.in_transit_tips .list .omit{
	width: 0.15rem;
	height: 0.3rem;
	float: left;
	background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAICAYAAADuv08kAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAAEnQAABJ0Ad5mH3gAAABOSURBVDhPYxhw8PTpGa7n1/f8v767F4xBbJAYVBoOqK2OASRxaXMDCgaJQaXhgNrqGECuQlcIEoNKwwG11Q2cxdQOQmLVDVzioi9gYAAAZ/hOeNVvP4IAAAAASUVORK5CYII=") no-repeat center;
	background-size: 0.15rem 0.04rem;
}

.search_content{
	border-top: 0.05rem solid #f9f9f9;
}

.search_box {
	padding: 0.07rem 0.56rem 0.07rem 0.45rem;
	position: relative;
	height: 0.44rem;
}

.search_input {
	position: relative;
}

.search_input input {
	display: block;
	width: 100%;
	height: 0.3rem;
	padding: 0.05rem 0.1rem 0.05rem 0.32rem;
	border: 0 none;
	background: #F1F1F1;
	line-height: 0.2rem;
	font-size: 0.14rem;
	color: #000;
	outline: none;
	-moz-border-radius: 0.04rem;
	-webkit-border-radius: 0.04rem;
	border-radius: 0.04rem;
}

.search_input .icon {
	display: block;
	width: 0.18rem;
	height: 0.18rem;
	position: absolute;
	top: 50%;
	margin-top: -0.09rem;
	left: 0.07rem;
	background: url(../images/icon_search.png) no-repeat center;
	background-size: 0.18rem;
}

.header_inner.spel{
	background: #fff;
}

.header_inner.spel > .icon_back:before{
	background-image: url(../images/icon_back2.png);
}

.header_inner.spel > h1.title{
	color: #333;
}

.header_inner.spel .icon_text{
	color: #B80205;
}

.search_empty {
	padding: 1.15rem 0.15rem 0.2rem;
	font-size: 0.14rem;
	color: #999;
	text-align: center;
	position: relative;
}

.search_empty:before{
	content: "";
	width: 0.52rem;
	height: 0.52rem;
	background: url(../images/search_empty.png) no-repeat center;
	background-size: 100%;
	position: absolute;
	top: 0.35rem;
	left: 50%;
	margin-left: -0.26rem;
}

.search_empty strong{
	font-weight: 500;
	color: #333;
}

.tab_nav{
	background: #fff;
	border-bottom: 0.05rem solid #F9F9F9;
}

.tab_nav ul{
	width: 100%;
	display: table;
	table-layout: fixed;
}

.tab_nav ul li{
	display: table-cell;
}

.tab_nav ul li a{
	display: block;
	height: 0.44rem;
	line-height: 0.44rem;
	text-align: center;
	font-size: 0.16rem;
	color: #333;
}

.tab_nav ul li a span{
	display: inline-block;
	position: relative;
	padding: 0 0.02rem;
}

.tab_nav ul li.active a{
	color: #B80205;
}

.tab_nav ul li.active a span:after{
	content: "";
	width: 100%;
	height: 2px;
	background: #B80205;
	position: absolute;
	bottom: 0;
	left: 0;
}

.bus_record .nav_list ul{
	padding-right: 0;
	padding-left: 0.12rem;
}

.bus_record .nav_list ul li{
	margin-left: 0;
	padding-left: 0.65rem;
}

.bus_record .nav_list ul li .icon{
	left: 0;
}

.no_moretips{
	text-align: center;
	padding: 0.1rem 0;
	font-size: 0.12rem;
	color: #ccc;
	line-height: 0.2rem;
}

.no_moretips span{
	position: relative;
}

.no_moretips span:before,
.no_moretips span:after{
	content: "";
	width: 0.06rem;
	height: 1px;
	background: #ccc;
	position: absolute;
	top: 50%;
}

.no_moretips span:before{
	left: -0.08rem;
}

.no_moretips span:after{
	right: -0.08rem;
}



/*-- 个人信息 --*/

.ste_cont {
	margin-bottom: 0.05rem;
}

.ste_cont ul {
	background: #fff;
	padding-left: 0.15rem;
}

.ste_cont ul li {
	border-bottom: 1px solid #eee;
	padding-right: 0.32rem;
	position: relative;
}

.ste_cont ul li:last-child {
	border-bottom: 0 none;
}

.ste_cont ul li.spel {
	padding-right: 0.15rem;
}

.ste_cont ul li .tit {
	font-size: 0.16rem;
	color: #000;
	line-height: 0.24rem;
	position: absolute;
	top: 0.11rem;
	left: 0;
}

.ste_cont ul li .cont {
	margin-left: 1rem;
	text-align: right;
	line-height: 0.24rem;
	padding: 0.11rem 0;
	font-size: 0.16rem;
	color: #333;
	min-height: 0.46rem;
}

.ste_cont ul li.spel .cont{
	color: #999;
}

.ste_cont ul li .cont span.default{
	color: #999;
}

.ste_cont ul li .lk {
	display: block;
	width: 0.06rem;
	height: 0.12rem;
	position: absolute;
	top: 50%;
	margin-top: -0.06rem;
	right: 0.15rem;
	background: url(../images/arrow01.png) no-repeat center;
	background-size: 100%;
}

.imperfect{
	color: #285FC1;
}

.contact_list {
	background: #fff;
	padding-left: 0.15rem;
}

.contact_list li {
	padding: 0.11rem 0.4rem 0.11rem 0;
	line-height: 0.24rem;
	font-size: 0.14rem;
	border-bottom: 1px solid #eee;
	position: relative;
}

.contact_list li:last-child {
	border-bottom: 0 none;
}

.contact_list li:after {
	display: block;
	content: "";
	width: 0.16rem;
	height: 0.16rem;
	position: absolute;
	top: 50%;
	margin-top: -0.08rem;
	right: 0.1rem;
	background: url(../images/arrow01.png) no-repeat center;
	background-size: 0.16rem;
}

.contact_list li h5 {
	font-weight: normal;
	font-size: 0.16rem;
	color: #000;
}

.contact_list li p {
	font-size: 0.13rem;
	color: #999;
}

.contact_list li p span {
	margin-right: 0.1rem;
}

.add_btn a {
	display: block;
	background: #fff;
	text-align: center;
	line-height: 0.46rem;
	height: 0.46rem;
	font-size: 0.16rem;
	color: #2277cc;
}

.add_btn a span {
	padding-left: 0.28rem;
	position: relative;
}

.add_btn a span:before {
	display: block;
	content: "";
	width: 0.18rem;
	height: 0.18rem;
	position: absolute;
	top: 50%;
	margin-top: -0.09rem;
	left: 0;
	background: url(../images/icon_add.png) no-repeat center;
	background-size: 0.18rem;
}

.record_box{
	margin-bottom: 0.1rem;
}

.record_box h5{
	line-height:0.24rem;
	color:#999;
	font-size:0.13rem;
	padding: 0.05rem 0.15rem;
	font-weight:normal;
}

.record_box ul{
	background:#fff;
	padding-left:0.15rem;
}

.record_box ul li{
	border-bottom:1px solid #eee;
}

.record_box ul li:last-child{
	border-bottom: 0 none;
}

.record_box ul li .icon_radio{
	display:block;
	padding: 0.12rem 0.48rem 0.12rem 0;
	line-height:0.22rem;
	font-size:0.16rem;
}

.record_box ul li .icon_radio:before{
	left:auto;
	right:0.15rem;
	top:0.14rem;
}

.record_box ul li .icon_radio.checked:before{
	border-color: #d2b380;
	background: #d2b380 url(../images/check_ic01.png) no-repeat center;
	background-size: 0.18rem;
}

.tax_checklist ul{
	background: #ffffff;
	padding-left: 0.15rem;
}

.tax_checklist ul li{
	border-bottom: 1px solid #eee;
	position: relative;
	line-height: 0.2rem;
	font-size: 0.13rem;
	color: #999;
	padding: 0.13rem 0.5rem 0.13rem 0;
}

.tax_checklist ul li:before{
	display: block;
    content: "";
    width: 0.16rem;
    height: 0.16rem;
    position: absolute;
    top: 50%;
    margin-top: -0.09rem;
    right: 0.15rem;
    border: 1px solid #ccc;
    -moz-border-radius: 100%;
    -webkit-border-radius: 100%;
    border-radius: 100%;
}

.tax_checklist ul li.checked:before{
	background: #f6f6f6 url(../images/check_ic02.png) no-repeat center;
    background-size: 0.18rem;
}

.tax_checklist ul li h5{
	font-size: 0.16rem;
	font-weight: normal;
	color: #333;
	margin-bottom: 0.04rem;
}

.tax_checklist ul li:last-child{
	border-bottom: 0 none;
}

.tax_infotips{
	padding: 0.1rem 0.15rem;
	font-size: 0.12rem;
	color: #999;
	line-height: 0.18rem;
	word-break:break-all;
}

.tax_infotips a{
	color: #285FC1;
}

.wtclass_main{
	padding: 0.1rem 0 0;
}

.wtclass_list li {
	position: relative;
	background: #fff;
	padding: 0.11rem 0.15rem;
	line-height: 0.24rem;
	margin-bottom: 0.05rem;
}

.wtclass_list li span {
	display: block;
	font-size: 0.16rem;
	color: #000;
}

.wtclass_list li .ui.switch {
	position: absolute;
	right: 0.15rem;
	top: 50%;
	margin-top: -0.16rem;
}




/*-- 手机号码修改 --*/

.phone_info {
	background: #fff;
	padding: 0.15rem 0.15rem 0.1rem;
	line-height: 0.2rem;
	font-size: 0.13rem;
	color: #999;
	margin-bottom: 0.05rem;
}

.phone_info .tel {
	line-height: 0.34rem;
	margin-top: 0.04rem;
	font-size: 0.24rem;
	color: #000;
}




/*-- 科创板 退市股票整理, 基础权限--*/
.kcb_page{
	background: #fdfdfd url(../images/kcb_bg.png) no-repeat center top;
	background-size: 100% auto;
	padding: 37% 0 0.23rem;
	min-height: 100%;
}
.tsgpky_page{
	background: #fdfdfd url(../images/ts_banner.png) no-repeat center top;
	background-size: 100% auto;
	padding: 37% 0 0.23rem;
	min-height: 100%;
}
.jcjjqxkt_page{
	background: #fdfdfd url(../images/ts_banner.png) no-repeat center top;
	background-size: 100% auto;
	padding: 37% 0 0.23rem;
	min-height: 100%;
}
.kcb_msutbox, .tsgpky_msutbox{
	margin: 0 0.15rem 0.1rem;
	background: #fff;
	padding: 0.2rem 0.18rem;
	box-shadow: 0 0 0.15rem rgba(0,0,0,0.05);
	-moz-border-radius: 0.1rem;
	-webkit-border-radius: 0.1rem;
	border-radius: 0.1rem;
}
.kcb_msutbox .title, .tsgpky_msutbox .title{
	text-align: center;
	margin-bottom: 0.1rem;
}
.kcb_msutbox .title h5, .tsgpky_msutbox .title h5{
	font-size: 0.2rem;
	font-weight: normal;
	color: #D2B380;
}
.kcb_msutbox .list li, .tsgpky_msutbox .list li{
	font-size: 0.14rem;
	color: #666;
	line-height: 0.2rem;
	padding: 0.05rem;
}
.kcb_msutbox .list li p, .tsgpky_msutbox .list li p{
	padding-left: 0.24rem;
	position: relative;
}
.kcb_msutbox .list li p:before, .tsgpky_msutbox .list li p:before{
	content: "";
	width: 0.06rem;
	height: 0.06rem;
	background: #D2B380;
	position: absolute;
	top: 0.07rem;
	left: 0;
	-webkit-transform: rotate(-45deg);
	transform: rotate(-45deg);
}
.kcb_msutbox .list li:nth-child(2n-1) p:before, .tsgpky_msutbox .list li:nth-child(2n-1) p:before{
	opacity: 1;
}
.kcb_msutbox .list li:nth-child(2n) p:before, .tsgpky_msutbox .list li:nth-child(2n) p:before{
	opacity: 0.3;
}
.kcb_risktips, .tsgpky_risktips{
	padding: 0.1rem 0.25rem;
	font-size: 0.13rem;
	line-height: 0.22rem;
	color: #999;
}
.kcb_cebtn .ui.button{
	height: 0.54rem;
	line-height: 0.54rem;
	background: #D2B380;
}

.shczd_page{
    background: #fdfdfd url(../images/czd.png) no-repeat center top;
    background-size: 100% auto;
    padding: 37% 0.1rem 0.23rem;
    min-height: 100%;
}
.szztg_page{
    /*background: #fdfdfd url(../images/ts_banner.png) no-repeat center top;*/
    /*background-size: 100% auto;*/
    padding: 0.1rem 0.1rem 0.23rem;
    min-height: 100%;
}




/*-- 增开基金户 --*/
.fund_acctbox ul{
	background: #fff;
	padding-left: 0.15rem;
	margin-bottom: 0.05rem;
}

.fund_acctbox ul li{
	border-bottom: 1px solid #eee;
}

.fund_acctbox ul li:last-child{
	border-bottom: 0 none;
}

.fund_acctbox ul li .icon_radio{
	display: block;
	padding: 0.11rem 0.4rem 0.11rem 0;
	line-height: 0.24rem;
	font-variant-east-asian: 0.16rem;
}
.fund_acctbox ul li .icon_radio:before{
	top: 50%;
	margin-top: -0.09rem;
	left: auto;
	right: 0.15rem;
}

.fund_acctbox ul li .icon_radio.checked:before{
	border-color: #d2b380;
    background: #d2b380 url(../images/check_ic01.png) no-repeat center;
    background-size: 0.18rem;
}

.fund_botbtn{
	background: #fff;
	border-top: 1px solid #eee;
	overflow: hidden;
}

.fund_botbtn .left{
	width: 50%;
	float: left;
	padding: 0.1rem 0.15rem;
}

.fund_botbtn .left .icon_radio{
	display: block;
}

.fund_botbtn .left .icon_radio.checked:before{
	border-color: #d2b380;
    background: #d2b380 url(../images/check_ic01.png) no-repeat center;
    background-size: 0.18rem;
}

.fund_botbtn .right{
	width: 40%;
	float: right;
}

.fund_openedlist ul{
	background: #fff;
	overflow: hidden;
	position: relative;
	margin-bottom: 0.05rem;
}

.fund_openedlist ul:after{
	content: "";
	width: 100%;
	height: 2px;
	background: #fff;
	position: absolute;
	bottom: 0;
	left: 0;
	z-index: 50;
}

.fund_openedlist ul li{
	width: 50%;
	float: left;
	border-bottom: 1px solid #eee;
	padding: 0.12rem 0.15rem;
	position: relative;
	font-size: 0.16rem;
	line-height: 0.22rem;
}

.fund_openedlist ul li p{
	height: 0.22rem;
}

.fund_openedlist ul li em{
	display: block;
	height: 0.18rem;
	font-size: 0.13rem;
	line-height: 0.18rem;
	margin-top: 0.03rem;
	color: #999;
}

.fund_openedlist ul li:nth-child(2n):before{
	content: "";
	width: 1px;
	height: 0.4rem;
	background: #eee;
	position: absolute;
	top: 50%;
	margin-top: -0.2rem;
	left: 0;
}



/*-- 两融预约 --*/
.investor_teach .title{
	padding: 0.15rem;
    margin-bottom: 0.05rem;
    line-height: 0.28rem;
    font-size: 0.18rem;
    color: #333;
    font-weight: 500;
    background: #fff;
}

.investor_teach .title em{
	display: block;
	font-size: 0.14rem;
	color: #666;
	line-height: 0.24rem;
	margin-top: 0.05rem;
	font-weight: normal;
}

.investor_teach .list{
	background: #fff;
	padding-left: 0.15rem;
	overflow: hidden;
	margin-top: 0.1rem;
}

.investor_teach .list li{
	width: 50%;
	float: left;
}

.investor_teach .list li .icon_radio{
	display: block;
	padding-top: 0.11rem;
	padding-bottom: 0.11rem;
	font-size: 0.16rem;
	color: #666;
}

.investor_teach .list li .icon_radio:before{
	top: 50%;
	margin-top: -0.09rem;
}

.investor_teach .list li .icon_radio.checked{
	color: #333;
}

.test_main .record_box ul li .icon_radio{
	    padding: 0.12rem 0.15rem 0.12rem 0.3rem;
}

.test_main .record_box ul li .icon_radio:before{
	right: auto;
	left: 0;
}

.special_sb_base{
	margin: 0.1rem 0;
	background: #fff;
	padding-left: 0.15rem;
}

.special_sb_base .title{
	padding: 0.1rem 0.15rem 0.1rem 0;
	line-height: 0.24rem;
	font-size: 0.16rem;
	font-weight: normal;
	color: #000;
	border-bottom: 1px solid #EEEEEE;
}

.special_sb_base .list li{
	border-bottom: 1px solid #EEEEEE;
}

.special_sb_base .list li:last-child{
	border-bottom: 0 none;
}

.special_sb_base .list li .icon_radio{
	display: block;
	padding: 0.11rem 0.15rem 0.11rem 0.3rem;
	line-height: 0.24rem;
	font-size: 0.16rem;
	color: #666;
}

.special_sb_base .list li .icon_radio.checked{
	color: #333;
}

.special_sb_base .list li .icon_radio:before{
	top: 50%;
	margin-top: -0.09rem;
}

.special_sb_info{
	margin: 0.1rem 0;
}

.special_sb_info .title{
	font-size: 0.13rem;
	font-weight: normal;
	line-height: 0.24rem;
	color: #999;
	padding: 0.05rem 0.15rem;
}

.special_sb_info .input_form{
	margin-bottom: 0.1rem;
}

.tax_delebtn{
	padding-right: 0.15rem;
}

.tax_delebtn a{
	display: block;
	height: 0.44rem;
	line-height: 0.44rem;
	text-align: center;
	font-size: 0.16rem;
	color: #B80205;
}

.add_com_btn{
	margin: 0.1rem 0;
}

.add_com_btn .ui.button{
	background: #fff;
	color: #B80205;
	font-size: 0.16rem;
}

.add_com_btn .ui.button.disable{
	background: #fff;
	color: #ccc;
}


.special_sb_info .ui.field > .ui.dropdown > strong:after{
	width: 0.06rem;
	height: 0.12rem;
	margin-top: -0.06rem;
	background-image: url(../images/arrow04.png);
	background-size: 100%;
}

.unit_span{
	display:block;
	line-height:0.24rem;
	font-size:0.14rem;
	color:#999;
	position:absolute;
	top:50%;
	margin-top:-0.12rem;
	right:0.15rem;
}

.radio_wrap{
	width: 100%;
	text-align: right;
	padding-left: 0.75rem;
	overflow: hidden;
	min-height: 0.46rem;
	border-bottom: 1px solid #eee;
}

.radio_wrap .icon_radio{
	display: inline-block;
	padding: 0.11rem 0.15rem 0.11rem 0.3rem;
	margin-left: 0.1rem;
}

.radio_wrap .icon_radio:before{
	top: 50%;
	margin-top: -0.09rem;
}

.ui.field:last-child .radio_wrap{
	border-bottom: 0 none;
}

.max_amount{
	background: #fff;
	margin-bottom: 0.1rem;
}

.max_amount ul{
	padding-left: 0.15rem;
}

.max_amount ul li{
	border-bottom: 1px solid #eee;
	padding: 0.11rem 0.15rem 0.11rem 0;
	line-height: 0.24rem;
	font-size: 0.16rem;
	position: relative;
}

.max_amount ul li .tit{
	color: #000;
	position: absolute;
	top: 0.11rem;
	left: 0;
}

.max_amount ul li p{
	text-align: right;
	min-height: 0.24rem;
	color: #999;
}

.max_amount ul li p.spel{
	color: #F3A91C !important;
}

.max_amount ul li p.spel{
	color: #F3A91C;
}

.max_amount.result ul li .tit{
	color: #999;
}

.max_amount.result ul li p{
	color: #333;
}

.amount_tips{
	padding: 0 0.15rem;
	font-size: 0.12rem;
	color: #999;
	line-height: 0.2rem;
	overflow: hidden;
}

.amount_tips .fl{
	float: left;
}

.amount_tips .fr{
	float: right;
}

.amount_info{
	background: #fff;
	margin-top: 0.1rem;
}

.amount_info .tit{
	padding: 0.1rem 0.15rem 0;
	line-height: 0.22rem;
	font-weight: normal;
	font-size: 0.16rem;
	color: #000;
}

.amount_input {
	position: relative;
}

.amount_input .icon{
	line-height: 0.36rem;
	font-style: normal;
	font-size: 0.3rem;
	font-weight: bold;
	color: #000;
	position: absolute;
	top: 0.1rem;
	left: 0.12rem;
}

.amount_input .t1{
	display: block;
	width: 100%;
	padding: 0.1rem 0.4rem 0.1rem 0.42rem;
	height: 0.56rem;
	line-height: 0.36rem;
	outline: none;
	border: 0 none;
	font-size: 0.35rem;
	color: #000;
}

.amount_input .t1::-moz-placeholder{
	font-size: 0.24rem;
	color: #ccc;
	position: relative;
	top: -0.04rem;
}

.amount_input .t1::-webkit-input-placeholder {
	font-size: 0.24rem;
    color: #ccc;
    position: relative;
    top: -0.04rem;
}

.amount_input .unit{
	font-size: 0.14rem;
	line-height: 0.36rem;
	color: #B80205;
	position: absolute;
	top: 0.1rem;
	right: 0.15rem;
}

.amount_info .tips{
	border-top: 1px solid #eee;
	margin-left: 0.15rem;
	padding: 0.07rem 0.15rem 0.07rem 0;
	line-height: 0.2rem;
	font-size: 0.13rem;
	color: #999;
}

.amount_info .tips span{
	color: #F3A91C;
}



/*-- 两融合约展期 --*/

.Extension_title{
	background: #F9F9F9;
}

.Extension_title ul{
	padding-left: 0.35rem;
	overflow: hidden;
	height: 0.3rem;
	line-height: 0.3rem;
}

.Extension_title ul li{
	float: left;
	font-size: 0.13rem;
	color: #999;
	padding: 0 0 0.05rem 0;
}

.Extension_title ul li.name{
	width: 40%;
}

.Extension_title ul li.date{
	width: 37%;
}

.Extension_title ul li.num{
	width: 38%;
}

.extension_list ul{
	background: #fff;
	padding-left: 0.05rem;
}

.extension_list ul li{
	border-bottom: 1px solid #eee;
	position: relative;
	width: 100%;
	/*display: table;*/
	display: flex;
	padding-left: 0.3rem;
}

.extension_list ul li:last-child{
	border-bottom: 0 none;
}

.extension_list ul li:before{
    content: "";
    width: 0.16rem;
    height: 0.16rem;
    position: absolute;
    top: 50%;
    margin-top: -0.09rem;
    left: 0;
    border: 1px solid #ccc;
    -moz-border-radius: 100%;
    -webkit-border-radius: 100%;
    border-radius: 100%;
}

.extension_list ul li.checked:before{
	border-color: #d2b380;
    background: #d2b380 url(../images/check_ic01.png) no-repeat center;
    background-size: 0.18rem;
}

.extension_list ul li .item_td{
	/*display: table-cell;*/
	/*vertical-align: middle;*/
	font-size: 0.14rem;
	color: #333;
	line-height: 0.2rem;
	padding: 0.1rem 0.05rem 0.1rem 0;
	word-break:break-all;
}

.extension_list ul li .item_td.name{
	width: 20%;
}

.extension_list ul li .item_td.date{
	width: 40%;
}

.extension_list ul li .item_td.num{
	width: 40%;
}

.extension_list ul li .item_td strong{
	display: block;
	font-size: 0.16rem;
	font-weight: normal;
}

.extension_list ul li .item_td em{
	display: block;
	font-size: 0.13rem;
	color: #999;
}

.com_extension{
	text-align: left;
	line-height: 0.2rem;
}

.com_extension .title{
	font-size: 0.14rem;
	font-weight: normal;
	margin-bottom: 0.05rem;
}

.com_extension .title strong{
	font-weight: normal;
	color: #BA0105;
}

.com_extension ul li{
	padding: 0.05rem 0;
	position: relative;
}

.com_extension ul li p{
	color: #999;
}

.com_extension ul li p span{
	color: #333;
	margin-left: 0.05rem;
}

.com_extension ul li .name{
	position: absolute;
	top: 0.05rem;
	right: 0;
}

.extension_result ul{
	background: #fff;
	padding-left: 0.15rem;
}

.extension_result ul li{
	border-bottom: 1px solid #eee;
	position: relative;
	padding: 0.12rem 0.15rem 0.12rem 0;
	overflow: hidden;
}

.extension_result ul li h5{
	font-size: 0.16rem;
	font-weight: normal;
	line-height: 0.22rem;
}

.extension_result ul li p{
	font-size: 0.13rem;
	line-height: 0.18rem;
	color: #999;
	margin-top: 0.02rem;
}

.extension_result ul li .left{
	width: 68%;
	float: left;
}

.extension_result ul li .right{
	width: 32%;
	float: right;
	text-align: right;
}

.extension_result ul li .right h5{
	color: #F3A91C;
}



/*-- 修改密码、重置密码 --*/
.pword_box .title{
	padding: 0.15rem 0.15rem 0.05rem;
    line-height: 0.24rem;
    font-weight: normal;
    font-size: 0.13rem;
    color: #999;
}

.pword_setcheck{
	background: #fff;
	position: relative;
	padding: 0.11rem 0.15rem;
	line-height: 0.24rem;
	font-size: 0.16rem;
}

.pword_setcheck .ui.switch{
	position: absolute;
	top: 50%;
	margin-top: -0.16rem;
	right: 0.15rem;
}

.com_title{
	padding: 0.15rem 0.15rem 0.05rem;
    line-height: 0.24rem;
    font-weight: normal;
    font-size: 0.13rem;
    color: #999;
}


/*-- 三方存管 --*/

.sele_card{
	padding: 0.15rem 0.15rem 0.1rem;
}

.sele_card li{
	margin-top: 0.1rem;
	background: #fff;
	position: relative;
	-moz-border-radius: 0.06rem;
	-webkit-border-radius: 0.06rem;
	border-radius: 0.06rem;
	overflow: hidden;
}

.sele_card li:first-child{
	margin-top: 0;
}

.card_cont{
	height: 1.04rem;
}

.card_spel{
	display:block;
	width:0.4rem;
	height:0.18rem;
	position:absolute;
	top:0.11rem;
	right:0.11rem;
	line-height:0;
	font-size:0;
	background:url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFAAAAAkCAYAAAAeor16AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAAEnQAABJ0Ad5mH3gAAAO4SURBVGhD7ZpvT9NQFMaPsHaDQTRBRYwYjRANMRr1jf8I4itFiYkIMUQDiCExCMb5Afhofg9fq8AGm6KvmeesZ2G3ezr6506M65P8Iub2nLZPe8+5bUdh9J36Tu2SO1UhZ2mXMp86G2elRO50hY6fUHtaq0i5CQ4sNCfqbMqUWd2jvpNqE1aZcuMoOKWOO6VWNWuXcsM4KKWBd2pXs8rkPgMBKQbOitpl6guNZHmDtO4dijOvlpnapOxFHJDSCPeICbXMFNe/eyggxaRI7qhaZopvzTkUkGKySYN5texAVdro2qHMOgpIyg45y7wItdacpNEx02is3ci5qGWmtig/iAIsUCjRQL9coBK3fzAemX1udlWa7UZj7cd9rJaZKpJzEwdY4a7so0LZSTAWGc/A2oxZC+AjirNBmZxrNcP84inxFAWEhR9xPsgzM8Z9IfvYpt4zeNxDcqDcfsTA2kEHSJ5ZUZwNAh/jZHGIAsITsDaKIM7xEuc28e7A6rEd6jmHaFcz5Av8Xvarh3sgqVEoIApcXN9sU/ZSEDLlBDRWh3OEeutzVDVwh9wZtcwU3/JXUIBNqnTLEdBYVI7OwMxttcwUF/eHKMAmUQ3cI3dMLixCzJOphMYa4al8FeWOCz+BnFfLTHFneY0CbBLVQNlWDy+25E5FuWNSgMekJ2XlBYLXRZ23iCpNZAQ0Jvg7MNfDRWYBUaUx11vG4PEGFhtzJoFvsldqmSm5LVFAPJw5TRtZvNSZwTmb8WrgRhcaaxclLnN6qKZ48I5/47jwFV/iTjWGqHdhNCbwFV5AORFioBy7rCs59jnaxjY/yb1cM8yvKFc+CVFrYCvqBn7mkiD//qbeoXafhyz1ZF+GvMVoZg0F2Ma2gd4yxlnZ4kcrubPlfErUc5afVWdRTBJKQW+gvc+WOMg27THQ+z+XjuUfvBDX0yL+e6Rx++S4TzS1Ka4713GAfdppYB2Zxl+pf0BO2D+WhCI5N9QyU0Vyp1BAHLhLTXJHvx+Et/id7UZjdTjHA5TbT5CBivVvOr8of1otM+WtwXBQVL7RUK+mjS0xBuX2c4iBVuEesV6vsYb2aTCPAuIj0yb7KBnhZsTfNFCaklpmitc1ozjg30eeWsK+O0xOrvYyuEmVkPWm02GfLqhlprj+zaOAFIOC/NhALTMli1AQkNKAfP1Tu5rFG1j5QvZ/kxtWu5oVtuN1KrwuHVersOTrEneyVRTc4RTkx6VqU2vJz1VLtU+AaT2Uz6oyK+X9gNrTQkR/AHWZ7c9oJMBfAAAAAElFTkSuQmCC") no-repeat center;
	background-size:100%;
}

.card_info{
	padding:0.12rem 0 0 0.12rem;
	height:0.58rem;
}

.card_info dt{
	background:rgba(255,255,255,0.8);
	width:0.46rem;
	height:0.46rem;
	padding: 0.06rem;
	-moz-border-radius:100%; -webkit-border-radius:100%; border-radius:100%;
	float:left;
}

.card_info dt img{
	display:block;
	width:0.34rem;
	height:0.34rem;
}

.card_info dd{
	margin-left:0.58rem;
	font-size:0.12rem;
	line-height:0.18rem;
}

.card_info dd h5{
	font-weight:normal;
	font-size:0.16rem;
	line-height:0.24rem;
}

.card_info dd p{
	color: #999;
}

.card_number{
	line-height:0.32rem;
	margin-top:0.03rem;
	padding-left:0.7rem;
	font-size:0.2rem;
	font-weight: 500;
}

.card_cont .tips{
	font-size: 0.14rem;
	line-height: 0.18rem;
	padding: 0.04rem 0.1rem 0 0.7rem;
}

.add_cardbtn{
	padding: 0 0.15rem 0.15rem;
}

.add_cardbtn a{
	display: block;
	padding: 0.11rem 0.4rem 0.11rem 0.45rem;
	line-height: 0.24rem;
	font-size: 0.16rem;
	font-weight: 500;
	color: #DB1E21;
	background: #fff;
	-moz-border-radius: 0.06rem;
	-webkit-border-radius: 0.06rem;
	border-radius: 0.06rem;
	position: relative;
}

.add_cardbtn a:before{
	content: "";
	width: 0.2rem;
	height: 0.16rem;
	background: url(../images/bk_addicon.png) no-repeat center;
	background-size: 100%;
	position: absolute;
	top: 50%;
	margin-top: -0.08rem;
	left: 0.15rem;
}

.add_cardbtn a:after{
	content: "";
	width: 0.06rem;
	height: 0.12rem;
	background: url(../images/arrow01.png) no-repeat center;
	background-size: 100%;
	position: absolute;
	top: 50%;
	margin-top: -0.06rem;
	right: 0.15rem;
}

.card_shadow{
	width:100%;
	height:100%;
	background:rgba(0,0,0,0.6);
	position:absolute;
	top:0;
	left:0;
	z-index:100;
	-moz-border-radius: 0.06rem;-webkit-border-radius: 0.06rem;border-radius: 0.06rem;
	text-align:center;
	font-size: 0.14rem;
	line-height: 0.32rem;
	padding: 0 0.15rem;
	color:#fff;
}

.card_shadow span{
	width: 100%;
	text-align: center;
	position: absolute;
	top: 50%;
	left: 0;
	-webkit-transform: translateY(-50%);
	transform: translateY(-50%);
}

.card_shadow span strong{
	display: block;
	font-size: 0.18rem;
	font-weight: normal;
}

.bank_infobox{
	padding:0.1rem 0;
}

.bank_infobox ul{
	background:#fff;
	padding-left:0.15rem;
}

.bank_infobox ul li{
	border-bottom:1px solid #eee;
	position:relative;
	padding:0.11rem 0.15rem 0.11rem 0;
	line-height:0.26rem;
	min-height:0.46rem;
	font-size:0.16rem;
}

.bank_infobox ul li:last-child{
	border-bottom: 0 none;
}

.bank_infobox ul li .tit{
	position:absolute;
	top:0.11rem;
	left:0;
	color:#999;
}

.bank_infobox ul li p{
	text-align:right;
	min-height: 0.24rem;
}

.ui.field > .ui.dropdown.bank_select strong{
	padding-top: 0.11rem;
	padding-bottom: 0.11rem;
	line-height: 0.24rem;
	overflow: hidden;
}

.ui.field > .ui.dropdown.bank_select strong:after{
	width: 0.06rem;
	height: 0.12rem;
	background: url(../images/arrow01.png) no-repeat center;
	background-size: 100%;
	top: 50%;
	margin-top: -0.06rem;
	right: 0.15rem;
}

.ui.field > .ui.dropdown.bank_select strong img{
	width: 0.24rem;
	height: 0.24rem;
	float: left;
	margin-right: 0.05rem;
}

.bank_list {
	background:#fff;
	padding-left:0.15rem;
}

.bank_list li{
	border-bottom:1px solid #eee;
	height:0.47rem;
	line-height:0.46rem;
	padding:0.09rem 0.15rem 0.09rem 0;
	line-height:0.28rem;
	font-size:0.16rem;
	color:#000;
	position: relative;
}

.bank_list li:last-child{
	border-bottom: 0 none;
}

.bank_list li img{
	display:block;
	width:0.28rem;
	height:0.28rem;
	float:left;
	margin-right:0.15rem;
}

.bank_list li.active:after{
	content: "";
	width: 0.18rem;
	height: 0.18rem;
	background: url(../images/icon_active.png) no-repeat center;
	background-size: 100%;
	position: absolute;
	top: 50%;
	margin-top: -0.09rem;
	right: 0.15rem;
}

.card_wbox{
	background: #fff;
}

.card_wbox .sele_card{
	padding-top: 0.1rem;
}



/*-- 我的资金 --*/
.fund_topbox{
	background: #fff;
}

.my_fundbox{
	padding: 0.25rem 0.15rem 0.15rem;
}

.my_fundbox .pic{
	height: 1.8rem;
	margin-bottom: 0.2rem;
}

.my_fundbox .pic img{
	display: block;
	height: 100%;
	margin: 0 auto;
}

.my_fundbox .list{
	overflow: hidden;
}

.my_fundbox .list li{
	width: 50%;
	float: left;
	padding: 0.05rem 0 0.05rem 0.16rem;
	font-size: 0.14rem;
	line-height: 0.2rem;
	color: #999;
	position: relative;
}

.my_fundbox .list li span{
	color: #333;
	margin-left: 0.05rem;
}

.my_fundbox .list li i{
	width: 0.08rem;
	height: 0.08rem;
	-moz-border-radius: 100%;
	-webkit-border-radius: 100%;
	border-radius: 100%;
	position: absolute;
	top: 0.11rem;
	left: 0;
}

.bk_transferlink{
	width: 100%;
	display: table;
	table-layout: fixed;
	border-top: 1px solid #F7F7F7;
	background: #fff;
	overflow: hidden;
	position: relative;
}

.bk_transferlink a{
	display: table-cell;
	padding: 0.12rem 0 0.12rem 0.4rem;
	position: relative;
	height: 0.66rem;
	color: #333;
	line-height: 0.22rem;
}

.bk_transferlink a h5{
	font-weight: 500;
	font-size: 0.16rem;
}

.bk_transferlink a p{
	font-size: 0.13rem;
	color: #999;
	margin-top: 0.02rem;
}

.bk_transferlink a:nth-child(2):after{
	content: "";
	width: 1px;
	height: 0.4rem;
	background: #F7F7F7;
	position: absolute;
	top: 50%;
	margin-top: -0.2rem;
	left: 0;
}

.bk_transferlink a:before{
	content: "";
	width: 0.2rem;
	height: 0.2rem;
	position: absolute;
	top: 0.13rem;
	left: 0.13rem;
	background-repeat: no-repeat;
	background-position: center;
	background-size: 100%;
}

.bk_transferlink a.out:before{
	background-image: url(../images/bk_ic_out.png);
}

.bk_transferlink a.in:before{
	background-image: url(../images/bk_ic_in.png);
}

.stock_infobox{
	background: #fff;
}

.stock_infobox ul{
	padding-left: 0.15rem;
}

.stock_infobox ul li{
	border-bottom: 1px solid #eee;
	position: relative;
	padding: 0.11rem 0.15rem 0.11rem 0;
	line-height: 0.24rem;
	font-size: 0.16rem;
}

.stock_infobox ul li:last-child{
	border-bottom: 0 none;
}

.stock_infobox ul li em{
	font-style: normal;
	font-size: 0.13rem;
	color: #999;
	margin-left: 0.05rem;
}

.stock_infobox ul li .info{
	position: absolute;
	top: 0.11rem;
	right: 0.15rem;
}

.stock_infobtn{
	background: #fff;
	border-top: 1px solid #eee;
	position: relative;
	height: 0.47rem;
}

.stock_infobtn a{
	padding: 0 0.15rem;
	line-height: 0.46rem;
	font-size: 0.14rem;
	color: #B80205;
	position: absolute;
	top: 0;
}

.stock_infobtn a.prev{
	padding-left: 0.26rem;
	left: 0;
}

.stock_infobtn a.next{
	padding-right: 0.26rem;
	right: 0;
}

.stock_infobtn a:before{
	content: "";
	width: 0.06rem;
	height: 0.12rem;
	background: url(../images/arrow01.png) no-repeat center;
	background-size: 100%;
	position: absolute;
	top: 50%;
	margin-top: -0.06rem;
}

.stock_infobtn a.prev:before{
	-webkit-transform: rotateY(180deg);
	transform: rotateY(180deg);
	left: 0.15rem;
}

.stock_infobtn a.next:before{
	right: 0.15rem;
}

.bk_record .title{
	height: 0.36rem;
	line-height: 0.36rem;
	padding: 0 0.15rem;
	font-size: 0.16rem;
	font-weight: normal;
}

.bk_record ul{
	background: #fff;
	padding-left: 0.15rem;
}

.bk_record ul li{
	border-bottom: 1px solid #eee;
	position: relative;
	padding: 0.15rem 0.15rem 0.15rem 0.48rem;
	line-height: 0.2rem;
}

.bk_record ul li:last-child{
	border-bottom: 0 none;
}

.bk_record ul li h5{
	font-size: 0.18rem;
	font-weight: 500;
	margin-bottom: 0.02rem;
}

.bk_record ul li p{
	font-size: 0.13rem;
	color: #999;
}

.bk_record ul li .time{
	font-size: 0.13rem;
	color: #999;
	position: absolute;
	bottom: 0.15rem;
	right: 0.15rem;
}

.bk_record ul li .type{
	width: 0.34rem;
	height: 0.34rem;
	text-align: center;
	line-height: 0.32rem;
	font-size: 0.1rem;
	border-style: solid;
	border-width: 1px;
	-moz-border-radius: 100%;
	-webkit-border-radius: 100%;
	border-radius: 100%;
	position: absolute;
	top: 50%;
	margin-top: -0.17rem;
	left: 0;
}

.bk_record ul li .type.in{
	border-color: #F3A91C;
	color: #F3A91C;
}

.bk_record ul li .type.out{
	border-color: #285FC1;
	color: #285FC1;
}



/*-- add 20200224 start --*/
.xsb_infopage .banner img{
	display: block;
	width: 100%;
}
.xsb_infopage .base{
	background: #fff;
	margin-bottom: 0.1rem;
	padding: 0.15rem;
	font-size: 0.14rem;
	line-height: 0.2rem;
	color: #333;
}
.xsb_detail{
	background: #fff;
	margin-bottom: 0.1rem;
	padding: 0.1rem 0.15rem;
}
.xsb_detail .title{
	font-size: 0.16rem;
	line-height: 0.22rem;
	color: #151515;
	font-weight: normal;
	padding: 0.1rem 0;
}
.xsb_detail .title em{
	color: #FA3B4B;
	margin-right: 0.05rem;
}
.xsb_detail dl{
	padding: 0.07rem 0;
	line-height: 0.2rem;
}
.xsb_detail dl dt{
	font-size: 0.16rem;
	color: #333;
	margin-bottom: 0.08rem;
}
.xsb_detail dl dd{
	padding: 0.02rem 0;
	line-height: 0.2rem;
	font-size: 0.14rem;
	color: #666;
}
.xsb_tips{
	padding: 0 0.15rem;
	font-size: 0.13rem;
	color: #999999;
	line-height: 0.2rem;
}
.acct_spbox{
	background: #fff;
	margin: 0 0 0.1rem;
}
.acct_spbox ul{
	padding-left: 0.15rem;
	background: #fff;
}
.acct_spbox ul li{
	border-bottom: 1px solid #eee;
	position: relative;
	padding: 0.1rem 0.4rem 0.1rem 0;
	line-height: 0.2rem;
	font-size: 0.16rem;
	color: #333;
}
.acct_spbox ul li:last-child{
	border-bottom: 0 none;
}
.acct_spbox ul li em{
	display: block;
	font-size: 0.13rem;
	color: #999999;
}
.acct_spbox ul li:after{
	display: block;
    content: "";
    width: 0.16rem;
    height: 0.16rem;
    position: absolute;
    top: 50%;
    margin-top: -0.09rem;
    right: 0.15rem;
    border: 1px solid #ccc;
    border-radius: 0.03rem;
}
.acct_spbox ul li.checked:after{
	border-color: #d2b380;
    background: #d2b380 url(../images/check_ic01.png) no-repeat center;
    background-size: 0.18rem;
}
.acct_spbox ul li.disable:after{
	display: none;
}
.acct_spbox ul li .state{
	font-size: 0.14rem;
	color: #999999;
	line-height: 0.2rem;
	position: absolute;
	top: 50%;
	margin-top: -0.1rem;
	right: 0.15rem;
	z-index: 50;
}
.acct_spbox .tips{
	background: #fff;
	margin-left: 0.15rem;
	padding: 0.15rem 0.15rem 0.15rem 0.24rem;
	font-size: 0.13rem;
	color: #B80205 ;
	line-height: 0.2rem;
	position: relative;
}
.acct_spbox .tips:before{
	content: "";
	width: 0.16rem;
	height: 0.16rem;
	background: url(../images/error_tipimg.png) no-repeat center;
	background-size: 100%;
	position: absolute;
	top: 0.17rem;
	left: 0;
}
.cond_box ul li.error .tit.spel p{
	padding-right: 0.8rem;
}
.cond_box ul li.error .tit.spel .link{
	top: 50%;
	margin-top: -0.12rem;
}
.cond_box ul li .link{
	padding-right: 0.12rem;
}
.acct_table{
	background: #fff;
	margin: 0 0 0.1rem;
	padding-left: 0.15rem;
}
.acct_table .title{
	overflow: hidden;
	display: -webkit-box;
    display: -webkit-flex;
    display: flex;
}
.acct_table .title li{
	height: 0.4rem;
	line-height: 0.4rem;
	font-size: 0.14rem;
	color: #A5A1B1;
	text-align: center;
}
.acct_table .title li:nth-child(1){
	text-align: left;
}
.acct_table .data li{
	border-top: 1px solid #eee;
	overflow: hidden;
	display: -webkit-box;
    display: -webkit-flex;
    display: flex;
}
.acct_table .data li .item{
	padding: 0.1rem 0;
	height: 0.5rem;
	line-height: 0.3rem;
	font-size: 0.16rem;
	text-align: center;
	color: #333;
}
.acct_table .data li .item:nth-child(1){
	text-align: left;
}
.m_soc_1{
	-moz-box-flex: 1;
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    flex: 1;
}
.m_soc_2{
	-moz-box-flex: 2;
    -webkit-box-flex: 2;
    -webkit-flex: 2;
    flex: 2;
}
.m_soc_3{
	-moz-box-flex: 3;
    -webkit-box-flex: 3;
    -webkit-flex: 3;
    flex: 3;
}
.acct_table .data li .icon_check{
	display: inline-block;
	padding-left: 0;
	width: 0.3rem;
	height: 0.3rem;
}
.acct_table .data li .icon_check:before{
	top: 50%;
	margin-top: -0.09rem;
	left: 50%;
	margin-left: -0.09rem;
}
.note_tips{
	margin: 0.1rem 0.15rem;
	padding: 0.05rem 0;
	font-size: 0.14rem;
	color: #999999;
	line-height: 0.2rem;
}
.note_tips dd{
	position: relative;
	padding: 0.03rem 0 0.03rem 0.13rem;
}
.note_tips dd:before{
	content: "";
	width: 0.05rem;
	height: 0.05rem;
	background: #999999;
	border-radius: 100%;
	position: absolute;
	top: 0.1rem;
	left: 0;
}
.com_infobox ul{
	background: #fff;
	padding-left: 0.15rem;
}
.com_infobox ul li{
	border-bottom: 1px solid #eee;
	position: relative;
	padding: 0.11rem 0.15rem 0.11rem 0;
	line-height: 0.22rem;
	font-size: 0.16rem;
	color: #333;
}
.com_infobox ul li:last-child{
	border-bottom: 0 none;
}
.com_infobox ul li p{
	min-height: 0.22rem;
}
.select_levellist li{
	background: #fff;
	margin-bottom: 0.1rem;
}
.select_levellist li:last-child{
	margin-bottom: 0;
}
.select_levellist li .tit{
	position: relative;
	margin-left: 0.15rem;
	position: relative;
	padding: 0.1rem 0.15rem 0.1rem 0;
	line-height: 0.24rem;
	color: #333;
}
.select_levellist li .tit .icon_check{
	display: inline-block;
	padding-left: 0;
	width: 0.3rem;
	height: 0.3rem;
	position: absolute;
	top: 50%;
	margin-top: -0.15rem;
	right: 0.1rem;
	z-index: 50;
}
.select_levellist li .tit .icon_check:before{
	top: 50%;
	left: 50%;
	margin: -0.09rem 0 0 -0.09rem;
}
.select_levellist li .tit h5{
	font-size: 0.16rem;
	font-weight: 500;
}
.select_levellist li .state{
	font-size: 0.14rem;
	color: #999999;
	line-height: 0.24rem;
	position: absolute;
	top: 50%;
	margin-top: -0.12rem;
	right: 0.15rem;
}
.select_levellist li .state.error{
	color: #B80205;
}
.select_levellist li .state.ok{
	color: #D2B380;
}
.select_levellist li .cont{
	margin-left: 0.15rem;
	border-top: 1px solid #eee;
	padding: 0.1rem 0.15rem 0.1rem 0;
	font-size: 0.14rem;
	color: #666666;
	line-height: 0.2rem;
}
.select_levellist li .cont p{
	padding: 0.03rem 0 0.03rem 0.13rem;
	position: relative;
}
.select_levellist li .cont p:before{
	content: "";
	width: 0.05rem;
	height: 0.05rem;
	background: #666666;
	border-radius: 100%;
	position: absolute;
	top: 0.1rem;
	left: 0;
}
.level_title{
	padding: 0.1rem 0.15rem 0.05rem;
	line-height: 0.23rem;
	color: #999999;
}
.level_title h5{
	font-size: 0.15rem;
	font-weight: 500;
}
.level_title p{
	font-size: 0.12rem;
}
.level_title .imp{
	color: #B80205;
}
.stock_table{
	background: #fff;
	padding-left: 0.15rem;
}
.stock_table table{
	table-layout: fixed;
}
.stock_table th{
	height: 0.4rem;
	text-align: center;
	font-size: 0.14rem;
	line-height: 0.2rem;
	font-weight: normal;
	color: #999999;
	vertical-align: middle;
}
.stock_table td{
	border-top: 1px solid #eee;
	height: 0.5rem;
	vertical-align: middle;
	text-align: center;
	font-size: 0.16rem;
	color: #333;
	line-height: 0.22rem;
}
.stock_table th:first-child,
.stock_table td:first-child{
	text-align: left;
}
.stock_table th:last-child,
.stock_table td:last-child{
	padding-right: 0.15rem;
	text-align: right;
}
.mt10{
	margin-top: 0.1rem !important;
}
.protocol_lycont{
	text-align: left !important;
	max-height: 3.6rem;
	overflow-x: hidden;
	overflow-y: auto;
	-webkit-overflow-scrolling: touch;
	color: #666;
	font-size: 0.14rem;
	line-height: 0.2rem;
}
.video_face{
    padding: 0.2rem 0.15rem 0.3rem;
    background: #ffffff;
}
.video_face h5{
    font-size: 0.16rem;
    font-weight: normal;
    color: #333333;
    line-height: 0.26rem;
    text-align: center;
}
.video_face .pic{
    width: 2.12rem;
    margin: 0.2rem auto 0.3rem;
}
.video_face img{
    display: block;
    width: 100%;
}
.video_face .face_step{
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    height: inherit!important;
}
.video_face .face_step li{
    -moz-box-flex: 1;
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    flex: 1;
    width: 0.02rem;
}
.video_face .img_box{
    width: 0.74rem;
    margin: 0 auto 0.13rem;
}
.video_face p{
    font-size: 0.14rem;
    color: #999999 ;
    line-height: 0.2rem;
    text-align: center;
}
.bottom_tips{
	padding: 0.1rem 0.15rem;
	font-size: 0.13rem;
	line-height: 0.18rem;
	color: #999999;
}
.result_info2 ul{
	background: #fff;
}
.result_info2 ul li{
	border-bottom: 1px solid #efefef;
	padding-left: 0.15rem;
}
.result_info2 ul li:last-child{
	border-bottom: 0 none;
}
.result_info2 ul li .base{
    padding: 0.1rem 0.15rem 0.1rem 0;
    line-height: 0.24rem;
    font-size: 0.16rem;
    color: #333;
    position: relative;
    overflow: hidden;
}
.result_info2 ul li .base p{
	width: 49%;
	float: left;
}
.result_info2 ul li .base .state{
	color: #999999;
	position: absolute;
	top: 0.1rem;
	right: 0.15rem;
}
.result_info2 ul li .base .state.ing{
	color: #999999;
}
.result_info2 ul li .base .state.ok{
	color: #D2B380;
}
.result_info2 ul li .base .state.error{
	color: #B80205;
}
.result_info2 ul li .fail_reason{
	border-top: 1px solid #efefef;
	padding: 0.11rem 0.15rem 0.11rem 1.2rem;
	position: relative;
}
.result_info2 ul li .fail_reason span{
	left: 0;
}
.com_span{
	color: #285fc1;
}

/*-- add 20200224 end --*/


/*-- add 20200304 start --*/
.pro_topbox{
	background: #fff;
	margin-bottom: 0.1rem;
	position: relative;
}
.pro_select{
	height: 0.46rem;
	padding: 0.12rem 0.4rem 0.12rem 0.15rem;
	line-height: 0.22rem;
	font-size: 0.16rem;
	color: #666666;
	position: relative;
}
.pro_select strong{
	font-weight: normal;
	display: block;
}
.pro_select:after{
	content: "";
	width: 0.05rem;
	height: 0.05rem;
	border-right: 1px solid #999999;
	border-bottom: 1px solid #999999;
	position: absolute;
	top: 50%;
	margin-top: -0.03rem;
	right: 0.16rem;
	-webkit-transform: rotate(45deg);
	transform: rotate(45deg);
}
.pro_select.on:after{
	border-color: #B80205;
	-webkit-transform: rotate(-135deg);
	transform: rotate(-135deg);
}
.pro_ctbox ul{
	background: #fff;
	padding-left: 0.15rem;
}
.pro_ctbox ul li{
	border-bottom: 1px solid #EFEFEF;
	position: relative;
	padding: 0.12rem 0.4rem 0.12rem 0;
}
.pro_ctbox ul li:last-child{
	border-bottom: 0 none;
}
.pro_ctbox ul li:after{
	content: "";
	width: 0.09rem;
	height: 0.09rem;
	border-right: 1px solid #999999;
	border-bottom: 1px solid #999999;
	position: absolute;
	top: 50%;
	margin-top: -0.05rem;
	right: 0.18rem;
	-webkit-transform: rotate(-45deg);
	transform: rotate(-45deg);
}
.pro_ctbox ul li h5{
	font-size: 0.16rem;
	font-weight: normal;
	color: #333;
	line-height: 0.22rem;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}
.pro_ctbox ul li p{
	font-size: 0.13rem;
	line-height: 0.18rem;
	color: #999999;
	margin-top: 0.03rem;
}
.pro_ctbox ul li p span + span {
	margin-left: .1rem;
}
.pro_selelayer{
	width: 100%;
	position: fixed;
	top: 0.88rem;
	bottom: 0;
	left: 0;
	z-index: 2000;
}
.pro_selelayer .shade{
	width: 100%;
	height: 100%;
	background: rgba(0,0,0,0.3);
	position: absolute;
	top: 0;
	left: 0;
}
.pro_selelayer ul{
	background: #fff;
	padding-left: 0.15rem;
	position: relative;
	z-index: 500;
}
.pro_selelayer ul li{
	border-bottom: 1px solid #EFEFEF;
	position: relative;
}
.pro_selelayer ul li:last-child{
	border-bottom: 0 none;
}
.pro_selelayer ul li span{
	display: block;
	padding: 0.12rem 0.4rem 0.12rem 0;
	line-height: 0.22rem;
	font-size: 0.16rem;
	color: #666;
}
.pro_selelayer ul li.active span{
	color: #B80205;
}
.pro_selelayer ul li.active:after{
	content: "";
	width: 0.18rem;
	height: 0.18rem;
	background: url(../images/icon_active.png) no-repeat center;
	background-size: 100%;
	position: absolute;
	top: 50%;
	margin-top: -0.09rem;
	right: 0.15rem;
}
.pro_infotitle{
	background: #fff;
	padding: 0.12rem 0.15rem;
	line-height: 0.22rem;
}
.pro_infotitle h5{
	font-size: 0.16rem;
	font-weight: 500;
}
.rule_ctbox ul{
	background: #fff;
	padding-left: 0.15rem;
}
.rule_ctbox ul li{
	border-bottom: 1px solid #EFEFEF;
}
.rule_ctbox ul li:last-child{
	border-bottom: 0 none;
}
.rule_ctbox ul li a{
	display: block;
	padding: 0.13rem 0.4rem 0.13rem 0;
	line-height: 0.2rem;
	font-size: 0.14rem;
	color: #333;
	position: relative;
}
.rule_ctbox ul li a:after{
	content: "";
	width: 0.09rem;
	height: 0.09rem;
	border-right: 1px solid #999999;
	border-bottom: 1px solid #999999;
	position: absolute;
	top: 50%;
	margin-top: -0.05rem;
	right: 0.18rem;
	-webkit-transform: rotate(-45deg);
	transform: rotate(-45deg);
}
.rule_time{
	margin: 0.2rem 0;
	padding: 0.05rem 0.15rem;
	font-size: 0.14rem;
	text-align: center;
	line-height: 0.2rem;
	color: #999999;
}

/*-- add 20200304 end --*/


/*-- add 20200311 start --*/
.ui.field .date_input{
	width: 100%;
	position: relative;
	padding-right: 0.5rem;
	height: auto !important;
	overflow: hidden;
	min-height: 0.46rem;
}
.ui.field .date_input .line{
	margin-bottom: 0.23rem;
}
.long_span{
	position: absolute;
	bottom: 0.11rem;
	right: 0.1rem;
}
.long_span .icon_check{
	line-height: 0.24rem;
	padding-left: 0.22rem;
}

/*-- add 20200311 end --*/


/*-- add ******** start --*/
.date_ctbox{
	background: #fff;
	overflow: hidden;
}
.date_ctbox .item{
	height: 0.44rem;
	width: 45%;
	line-height: 0.44rem;
	float: left;
	text-align: center;
}
.date_ctbox .item strong{
	height: 0.44rem;
	display: block;
	font-weight: normal;
	font-size: 0.16rem;
	color: #333;
}
.date_ctbox .line{
	width: 10%;
	display: block;
	height: 0.44rem;
	float: left;
	position: relative;
}
.date_ctbox .line:before{
	content: "";
	width: 0.12rem;
	height: 1px;
	background: #999;
	position: absolute;
	top: 50%;
	left: 50%;
	margin-left: -0.06rem;
}

/*-- add ******** end --*/


/*-- add ******** start --*/
.sub_title{
	background: #fff;
	padding: 0.14rem 0.15rem;
	border-bottom: 1px solid #eee;
}
.sub_title h5{
	font-size: 0.16rem;
	line-height: 0.22rem;
	font-weight: 500;
}
.account_list li.sub{
	padding-left: 0.2rem;
	border-top: 1px solid #fff;
	margin-top: -1px;
}
.account_list li.sub:before{
	content: "";
	width: 0.1rem;
	height: 0.3rem;
	border-left: 1px solid #eee;
	border-bottom: 1px solid #eee;
	position: absolute;
	left: 0.05rem;
	top: -0.07rem;
}
.account_list li .status.now{
	color: #285FC1;
}
/*-- add ******** end --*/


/*-- add ******** start --*/
.banner_box{
	margin-bottom: 0;
}
.home_nav{
	padding-top: 0;
}
.home_tabnav ul{
	display: -webkit-box;
    display: -webkit-flex;
    display: flex;
}
.home_tabnav ul li{
	-moz-box-flex: 1;
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    flex: 1;
}
.home_tabnav ul li a{
	display: block;
	height: 0.44rem;
	line-height: 0.44rem;
	text-align: center;
	font-size: 0.16rem;
	color: #333;
	position: relative;
}
.home_tabnav ul li.active a{
	color: #B80205;
}
.home_tabnav ul li.active a:after{
	content: "";
	width: 0.5rem;
	height: 2px;
	background: #B80205;
	position: absolute;
	bottom: -1px;
	left: 50%;
	margin-left: -0.25rem;
}
.hm_loginbox{
	border-top: 1px solid #EFEFEF;
	padding: 0.25rem 0.2rem;
	background: #fff url(../images/hm_login_bg.png) no-repeat center bottom;
	background-size: 100% auto;
	text-align: center;
}
.hm_loginbox h5{
	font-size: 0.18rem;
	line-height: 0.25rem;
	font-weight: normal;
	color: #666;
}
.login_button{
	display: block;
	margin: 0.2rem auto 0;
	width: 1.2rem;
	height: 0.36rem;
	line-height: 0.36rem;
	text-align: center;
	font-size: 0.16rem;
	color: #fff;
	background: #BA0105;
	border-radius: 0.03rem;
}
.login_info_wp{
	padding: 0.1rem;
}
.login_info{
	background: #fff;
	border-radius: 0.04rem;
	box-shadow: 0 0 7px 0 rgba(0,0,0,0.05);
	padding: 0.14rem 0.14rem 0.14rem 0.07rem;
	position: relative;
	font-size: 0.16rem;
	line-height: 0.22rem;
}
.login_info p{
	padding-left: 0.25rem;
	background: url(../images/lg_doticon.png) no-repeat 0 0.01rem;
	background-size: 0.19rem;
	min-height: 0.22rem;
}
.login_info .exit_btn{
	width: 0.64rem;
	height: 0.24rem;
	border: 1px solid #BA0105;
	text-align: center;
	line-height: 0.22rem;
	font-size: 0.12rem;
	color: #BA0105;
	border-radius: 0.03rem;
	position: absolute;
	top: 50%;
	margin-top: -0.12rem;
	right: 0.14rem;
	z-index: 50;
}




/*-- add ******** end --*/





/*-- add 20201020 start --*/
.Extension_title ul{
	min-width: 100%;
    /*display: table;*/
	display: flex;
}
.Extension_title ul li{
	float: none;
	/*display: table-cell;*/
    /*vertical-align: middle;*/
}
.Extension_title ul li{
	padding: 0 0.05rem 0 0;
}
.Extension_title ul li.name,
.Extension_title ul li.date,
.Extension_title ul li.num{
	/*width: 1rem !important;*/
}
.extension_list ul li{
	width: auto;
	min-width: 100%;
}
.extension_list ul li .item_td.name,
.extension_list ul li .item_td.date,
.extension_list ul li .item_td.num{
	/*width: 1rem !important;*/
}
.extension_scroll{
	width: 100%;
	overflow-y: hidden;
	overflow-x: auto;
	-webkit-overflow-scrolling: auto;
}
.ext_cont_scroll{
	background: #fff;
	overflow: auto;
	position: relative;
	z-index: 1;
	-webkit-overflow-scrolling: auto;
}
.extension_list ul li .item_td strong{
	width: 0.95rem;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}

/*-- add 20220118 start --*/
.zd_noticebox{
    background: #ffffff;
    padding: 0.4rem 0.25rem;
    text-align: center;
}
.zd_noticebox .pic{
    margin-bottom: 0.2rem;
}
.zd_noticebox .pic img{
    display: block;
    margin: 0 auto;
    height: 2.2rem;
}
.zd_noticebox p{
    color: #999999;
    font-size: 0.14rem;
    line-height: 0.2rem;
}
.asset_info{
    display: inline-block;
}
.icon_help{
    display:block;
    width: 0.24rem;
    height: 0.24rem;
    background:url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADwAAAA8CAYAAAA6/NlyAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAReSURBVGhD7VrZahRREI0x7gtCRMQlSow+qAgaF3CNScQsxqhxogjZwJnfmIfMkvUhj/ML85PjOU3V0AyTvkt3Oi3cQDPDdFXdOqfvra0zMBD+AgOBgcBAYCAwEBgIDAQGAgOBgUIysL29falerz/E9bHZbC41Go0VfFZw/cW1it9/4HOSMpQtJAiTU51O58TOzs49gPsq4AiwsrW19Ru/fcH3aV78zt8AtqxyojNKG6Z1CnEfT+kGnC8RAJzfAJgP+H63Wq2ePsxB3tvc3LwDuQnqCPhSrVa7WQhQ/Zxot9snAe6dAsXneBLIw4AcHBycAdBnChw74C1tFwo4gJ0F2G+ybWd3d3cvpHWQNgB6jjZpe39//1xam5noEyyewrJsw/FMjMaM8GnrFudaWdt3sgcHhvTJMso6KTsIg9BH+qSPdXvzfNERPgUH/71E9UkzTngZSKvECCoBasbGFs7jMAh6zVyMa1226So+FwDiiU2Ag/6sEJxv9GaO5LllJDUFKAAZBKA38XyM73/w208WHrH8uwKbt5LI41pCVinXPM2iwjZISYUVVVYg6AUIOB8Htbe3d1WKEZUZTgKtWxs6YzY7KxMZBio+XdM2RDFxO/ZkRxMKj0GtyvCUPyc5yTW5NuUzAWMyggUvC4gJkyycmpFzPmeS1V1DMCZZqd4q8OWiSTb1faYfKTBGTMYYjOgcy0aTbCwIrplksQtGJE0dWSrs+oCF2NmUTdvZ5HTvfTzZV7JzFk26rVbrFH1gfDDJpr7PtMIOJ7WhmAFpHNguVtB83LexDYJ+0Rcb2VQyWGiNNW4qI6LM1AKAz7U95Pa3tQuw8+ytbeW95ZhecE17GxBFaQ7YG0fpCGAfu9iE/BSJctHxkpVgMeWlLEo4/1d08sHjAfDXXO2RdBLlqucsL9XRvLNiTEHGOlHb5xv8eKx4vNL4YaXLQMGAYSXcRwj59rpuY1NZmrQGd0YuQYupgGeHqcEHNM+qAF7w0acO15ZYMulrw1rPpfDoZzTW0HsHPi1Zj7IH7/rOck4Cl3UKiQNnA8Gzhy35yZrlHkGsPyE5O5+xLgt3m+bBF1CSnjYPAGysyDJbn60ZGc5j0tHrtB4JNhuZATIZYoUEwJw9r7tGWpD1ANv5JXSdB35SrGxw+JDrAICEcDohrZ/ViEdJ1JaRZJmI7b2vuqbJiKtda3kdvLtsbV/AIOgpCebg0NrBrAXlbUM0gD/KFKGpUCqzoaxxONnjcFzOM5sA53NpWkyDFM/tsQ/i1Vm+BiH7suVmewd1JlD97kuAikZEuL4XBqw6y7cQOphnjuaT8WkMqCNPVWfX72nbh7RcdCR6d1+XsiriDCqp9uY9ynAAQLJkpywfWzR2ZUpfiLMaki3JoFaWDmdeetnohTg7L514SJpjFTeWe551BXmYPMe67JBkQsF/b+Dbhmh+JUOAJXZgfGH23/7LQ1ZkBTuBgcBAYCAwEBgIDAQGAgOBgQIz8A+bidFyVhTO0QAAAABJRU5ErkJggg==") no-repeat center;
    background-size: 0.3rem 0.3rem;
    position: absolute;
    top: 0;
    left: 100%;
    z-index: 50;
}
.p_check_list{
    background: #ffffff;
}
.p_check_list li .base{
    position: relative;
    margin-left: 0.15rem;
    padding-right: 0.15rem;
    border-bottom: 1px solid rgba(221,221,221,0.5);
}
.p_check_list li .base p{
    padding: 0.11rem 0;
    font-size: 0.16rem;
    line-height: 0.22rem;
}
.p_check_list li .base .icon_radio{
    display: block;
    padding: 0.11rem 0 0.11rem 0.28rem;
    font-size: 0.16rem;
    line-height: 0.22rem;
    color: #333333;
}
.p_check_list li .base .icon_radio:before{
    top: 50%;
    margin-top: -0.09rem;
    left: 0;
}
.p_check_list li .base .icon_radio.checked:before{
    border-color: #2277cc;
    background: url(../images/check_ic03.png) no-repeat center;
    background-size: 0.18rem;
}
.upload_txtcont{
    background: #f9f9f9;
    padding: 0.1rem 0.15rem;
    font-size: 0.12rem;
    line-height: 0.14rem;
    color: #ED1C24;
}
.upload_txtcont p{
    padding: 0.05rem 0;
}
.assets_rzbox{
    background: #ffffff;
    margin-bottom: 0.1rem;
    padding: 0.15rem;
    font-size: 0.14rem;
    line-height: 0.2rem;
}
.assets_rzbox .title{
    font-size: 0.18rem;
    line-height: 0.25rem;
    font-weight: 500;
    text-align: center;
    margin-bottom: 0.14rem;
}
.assets_rzbox .info{
    margin-top: 0.1rem;
    color: #666666;
}
.assets_rzbox .info dd{
    margin-top: 0.06rem;
}
.file_info_module{
    background: #ffffff;
    padding: 0.15rem;
    margin-bottom: 0.1rem;
}
.file_info_module .title{
    font-size: 0.16rem;
    line-height: 0.22rem;
    font-weight: 500;
    margin-bottom: 0.12rem;
}
.file_info_module .tips{
    font-size: 0.12rem;
    line-height: 0.18rem;
    color: #999999;
    margin-top: 0.08rem;
}
.file_upload_list{
    overflow: hidden;
}
.file_upload_list li{
    padding: 0.06rem 0;
    width: 0.5rem;
    margin-right: 0.18rem;
    float: left;
    position: relative;
}
.file_upload_list li .pic{
    height: 0.5rem;
    overflow: hidden;
    background: #eeeeee;
}
.file_upload_list li .pic img{
    display: block;
    width: 100%;
    position: relative;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
}
.file_upload_list li .add_btn{
    display: block;
    width: 0.5rem;
    height: 0.5rem;
    background: #F6F6F6;
    position: relative;
}
.file_upload_list li .add_btn:before,
.file_upload_list li .add_btn:after{
    content: "";
    width: 0.2rem;
    height: 0.02rem;
    background: #BFC1C2;
    position: absolute;
    top: 50%;
    margin-top: -0.01rem;
    left: 50%;
    margin-left: -0.1rem;
}
.file_upload_list li .add_btn:after{
    -webkit-transform: rotate(90deg);
    transform: rotate(90deg);
}
.file_upload_list li .delete{
    display: block;
    width: 0.16rem;
    height: 0.16rem;
    border-radius: 50%;
    background: rgba(0,0,0,0.60);
    position: absolute;
    top: 0;
    right: -0.06rem;
    z-index: 50;
}
.file_upload_list li .delete:before,
.file_upload_list li .delete:after{
    content: "";
    width: 0.1rem;
    height: 1px;
    background: #ffffff;
    position: absolute;
    top: 50%;
    left: 50%;
    margin-left: -0.05rem;
}
.file_upload_list li .delete:before{
    -webkit-transform: rotate(45deg);
    transform: rotate(45deg);
}
.file_upload_list li .delete:after{
    -webkit-transform: rotate(135deg);
    transform: rotate(135deg);
}
.file_example_list{
    overflow: hidden;
}
.file_example_list li{
    width: 0.5rem;
    float: left;
    padding: 0.06rem 0;
    margin-right: 0.18rem;
}
.file_example_list li .pic{
    height: 0.5rem;
    overflow: hidden;
    background: #eeeeee;
}
.file_example_list li .pic img{
    display: block;
    width: 100%;
    position: relative;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
}
.black_bg{
    background: #000000 !important;
}
.black_bg .header_inner{
    background: #000000;
}
.file_img_big img{
    display: block;
    width: 100%;
}
/*-- add 20220118 end --*/

/*-- add 20201020 end --*/

@media (min-device-width : 320px) and (max-device-width : 360px) and (-webkit-min-device-pixel-ratio : 2){
	.login_box{
		padding: 17% 0 1.05rem;
	}
	.login_logo{
		margin-bottom: 13%;
	}
	.no_acctbox{
		padding: 0.15rem 0;
	}
	.result_main h5{
		font-size: 0.18rem;
	}
	.no_acctbox.spel h5{
		font-size: 0.18rem;
	}
	.approp_result h5{
		font-size: 0.18rem;
		line-height: 0.5rem;
	}
	.approp_result .icon{
		width: 0.5rem;
		height: 0.5rem;
	}
	.bk_transferlink a p{
		font-size: 0.12rem;
	}
	.ui.field .date_input .dropdown{
		font-size: 0.15rem;
	}
	.ui.field .date_input .line{
		width: 0.08rem;
		margin-left: 0.03rem;
		margin-right: 0.03rem;
	}
}
/*-- add 20220118 end --*/

/*-- add 20220411 start --*/
.padding-left-15{
	padding: 0 0.15rem;
}
.type_selelist{
	background: #ffffff;
	padding-left: 0.16rem;
}
.type_selelist li{
	border-bottom: 1px solid #EFEFEF;
	padding: 0.12rem 0.4rem 0.12rem 0;
	font-size: 0.16rem;
	line-height: 0.22rem;
	color: #000000;
	position: relative;
}
.type_selelist li:last-child{
	border-bottom: 0 none;
}
.type_selelist li:after{
	content: "";
	width: 0.06rem;
	height: 0.12rem;
	background: url(../images/arrow04.png) no-repeat center;
	background-size: 100%;
	position: absolute;
	top: 50%;
	margin-top: -0.06rem;
	right: 0.16rem;
}
.mt30{
	margin-top: 0.3rem !important;
}
.dz_cm_list{
	background: #ffffff;
	padding-left: 0.16rem;
}
.dz_cm_list li{
	display: flex;
	width: 100%;
	border-bottom: 1px solid #EFEFEF;
	position: relative;
	padding: 0.14rem 0.38rem 0.14rem 0;
	font-size: 0.16rem;
	line-height: 0.22rem;
	color: #333333;
	align-items: center;
}
.dz_cm_list li:last-child{
	border-bottom: 0 none;
}
.dz_cm_list li:after{
	content: "";
	width: 0.18rem;
	height: 0.18rem;
	background: url(../images/arrow05.png) no-repeat center;
	background-size: 100%;
	position: absolute;
	top: 50%;
	margin-top: -0.09rem;
	right: 0.16rem;
}
.dz_cm_list li .name{
	width: 45%;
}
.dz_cm_list li .date{
	width: 30%;
}
.dz_cm_list li .state{
	width: 25%;
	text-align: right;
}
.dz_cm_list li  .item em{
	display: block;
	font-size: 0.14rem;
	line-height: 0.2rem;
	color: #999999;
}
.dz_cm_list li .state .ing{
	color: #D2B380;
}
.dz_cm_list li .state .ok{
	color: #285FC1;
}
.dz_cm_list li .state .error{
	color: #B80205;
}
.tab_nav ul li a{
	color: #666666;
}
.tab_nav ul li.active a{
	color: #000000;
}
.tab_nav ul li.active a span:after{
	width: 0.32rem;
	left: 50%;
	margin-left: -0.16rem;
}
.right_formbox{
	background: #ffffff;
	padding-left: 0.16rem;
	margin-bottom: 0.05rem;
}
.input_item{
	border-bottom: 1px solid #EFEFEF;
	padding-right: 0.16rem;
	display: flex;
}
.input_item:last-child{
	border-bottom: 0 none;
}
.input_item .tit{
	display: block;
	min-width: 1.1rem;
	margin-right: 0.15rem;
	font-size: 0.16rem;
	line-height: 0.22rem;
	padding: 0.12rem 0;
	color: #666666;
}
.input_item .ct{
	flex: 1;
	width: 100%;
}
.p_input{
	display: block;
	width: 100%;
	height: 0.46rem;
	padding: 0.12rem 0;
	line-height: 0.22rem;
	font-size: 0.16rem;
	color: #333333;
	outline: none;
	border: 0 none;
}
.p_input[disabled="disabled"]{
	background: #ffffff;
	color: #666666;
}
.p_input::-moz-placeholder {
	color: #999999;
}
.p_input::-webkit-input-placeholder {
	color: #999999;
}
.p_dropdown{
	display: block;
	width: 100%;
	padding: 0.12rem 0.22rem 0.12rem 0;
	font-size: 0.16rem;
	line-height: 0.22rem;
	min-height: 0.46rem;
	color: #333333;
	position: relative;
}
.p_dropdown:after{
	content: "";
	width: 0.18rem;
	height: 0.18rem;
	background: url(../images/arrow05.png) no-repeat center;
	background-size: 100%;
	position: absolute;
	top: 50%;
	margin-top: -0.09rem;
	right: 0;
}
.p_dropdown:empty:before{
	content: attr(placeholder);
	color: #999999;
}
.p_icon_radio {
	min-height: 0.24rem;
	line-height: 0.24rem;
	display: inline-block;
	padding-left: 0.3rem;
	position: relative;
}

.p_icon_radio:before {
	display: block;
	-moz-box-sizing: border-box;
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
	content: "";
	width: 0.18rem;
	height: 0.18rem;
	position: absolute;
	top: 0.03rem;
	left: 0;
	border: 1px solid #ccc;
	-moz-border-radius: 100%;
	-webkit-border-radius: 100%;
	border-radius: 100%;
}

.p_icon_radio.checked:before {
	border-color: #d2b380;
	background: #d2b380 url(../images/check_ic01.png) no-repeat center;
	background-size: 0.18rem;
}

.p_icon_radio.disable:before {
	opacity: 0.5;
}
.radio_list{
	padding: 0.11rem 0;
}
.radio_list .p_icon_radio{
	font-size: 0.16rem;
	line-height: 0.24rem;
	vertical-align: top;
	margin-right: 0.25rem;
}
.radio_list .p_icon_radio:last-child{
	margin-right: 0;
}
.p_txt_info{
	padding: 0.12rem 0;
	line-height: 0.22rem;
	font-size: 0.16rem;
	color: #666666;
}
.right_formbox .input_item.text .radio_list{
	text-align: right;
}
.right_formbox .input_item.text .p_input{
	text-align: right;
}
.right_formbox .input_item.text .p_dropdown{
	text-align: right;
}
.right_formbox .input_item.text .p_txt_info{
	text-align: right;
}
.p_unit_span{
	display: block;
	margin-left: 0.08rem;
	padding: 0.12rem 0;
	font-size: 0.14rem;
	line-height: 0.22rem;
	color: #d2b380;
}
.p_sub_title{
	margin: 0.15rem 0.16rem 0.05rem;
	font-size: 0.16rem;
	line-height: 0.24rem;
	font-weight: normal;
	color: #999999;
}
.right_check_list{
	background: #ffffff;
	padding-left: 0.16rem;
}
.right_check_list li{
	border-bottom: 1px solid #EFEFEF;
}
.right_check_list li:last-child{
	border-bottom: 0 none;
}
.right_check_list li .p_icon_radio{
	display: block;
	padding: 0.12rem 0.5rem 0.12rem 0;
	font-size: 0.16rem;
	line-height: 0.22rem;
	color: #333333;
}
.right_check_list li .p_icon_radio:before{
	left: auto;
	right: 0.16rem;
	top: 50%;
	margin-top: -0.09rem;
}
.text-align-center{
	text-align: center;
}

/*-- add 20220411 end --*/

.photo_btn {
	width: 0.24rem;
	height: 0.24rem;
	background: url(../images/icon_photo02.png) no-repeat center;
	background-size: 0.24rem;
	position: absolute;
	top: 50%;
	margin-top: -0.12rem;
	right: 0.15rem;
	z-index: 50;
}






@media (min-device-width : 320px) and (max-device-width : 360px) and (-webkit-min-device-pixel-ratio : 2){
	.login_box{
		padding: 17% 0 1.05rem;
	}
	.login_logo{
		margin-bottom: 13%;
	}
	.no_acctbox{
		padding: 0.15rem 0;
	}
	.result_main h5{
		font-size: 0.18rem;
	}
	.no_acctbox.spel h5{
		font-size: 0.18rem;
	}
	.approp_result h5{
		font-size: 0.18rem;
		line-height: 0.5rem;
	}
	.approp_result .icon{
		width: 0.5rem;
		height: 0.5rem;
	}
	.bk_transferlink a p{
		font-size: 0.12rem;
	}
	.ui.field .date_input .dropdown{
		font-size: 0.15rem;
	}
	.ui.field .date_input .line{
		width: 0.08rem;
		margin-left: 0.03rem;
		margin-right: 0.03rem;
	}
}
/*问卷回访 2022-04-20*/
.visit_item{
	background: #fff;
	margin-top: 0.1rem;
}
.visit_item:first-child{
	margin-top: 0;
}
.visit_item h5{
	padding: 0.2rem 0.15rem 0.15rem 0.45rem;
	position: relative;
	border-bottom: 1px solid #eee;
	font-size: 0.16rem;
	line-height: 0.24rem;
	font-weight: 500;
	color: #222;
}
.visit_item h5 .num{
	position: absolute;
	top: 0.2rem;
	left: 0.15rem;
}
.visit_item ul{
	overflow: hidden;
	padding: 0.02rem 0.3rem;
}
.visit_item ul li{
	width: 50%;
	float: left;
	padding: 0 0.12rem;
}
.visit_item ul li .icon_radio{
	display: block;
	padding: 0.11rem 0.15rem 0.11rem 0.32rem;
	font-size: 0.18rem;
	color: #999;
}
.visit_item ul li .icon_radio:before{
	top: 0.14rem;
}
.visit_item ul li .icon_radio.checked{
	color: #FF4540;
}

/*-- add 20230906 --*/
.dialog_overlay{
	width: 100%;
	height: 100%;
	background: rgba(0,0,0,0.4);
	position: fixed;
	top: 0;
	left: 0;
	z-index: 2000;
}
.popup_layer {
	width: 100%;
	background: #fff;
	height: 5.68rem;
	max-height: 5.68rem;
	display: -webkit-box;
	display: -webkit-flex;
	display: flex;
	-webkit-box-orient: vertical;
	box-orient: vertical;
	-webkit-flex-direction: column;
	flex-direction: column;
	position: absolute;
	bottom: 0;
	left: 0;
	z-index: 9999;
	-webkit-transform: translateY(110%);
	transform: translateY(110%);
	-webkit-transition: transform 0.2s linear;
	transition: transform 0.2s linear;
}
.popup_layer.show {
	-webkit-transform: translateY(0);
	transform: translateY(0);
}
.popup_layer .popup_lycont{
	-moz-box-flex: 1;
	-webkit-box-flex: 1;
	-webkit-flex: 1;
	flex: 1;
	min-height: 0;
	height: 100% !important;
	max-height: none;
}
.popup_layer.spel{
	height: auto;
	max-height: 5.68rem;
}
.popup_lytit {
	height: 0.5rem;
	line-height: 0.5rem;
	text-align: center;
}

.popup_lytit h3 {
	height: 0.5rem;
	font-size: 0.16rem;
	font-weight: 500;
	margin: 0 0.5rem;
	color: #333333;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}

.popup_lytit a.cancel {
	width: 0.5rem;
	height: 0.5rem;
	background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAEISURBVFhH7dcBDoIwDIVhjupN4FoD7qavyZ4ZMnSUbiVmf7IYxXVfNJg49Hq9fy6E8JAVn5p3ab5sXJblKWue5zG+bNa6riPnq5Ap0BqZ4mSpP0VsntJBFsgMboqXdGGIGdIcxzDsMrIajmGoGlkdxzD8NLIZjuGQYmRzHMNhP5FuOIZDD5HuOIbDd8jb4BgQG2S63HEMmB3yNjjp82uVVfIT1KQcjssdmbsh8Hh4dzft292K577Ikp8SvO6DLMExXG+LPINjeF8bpAbH8P66yCs4hn11kBY4hv22SEscwxwbJDCbv50WOIZ5G6ScFS+VlwItcQxz30gVUJKN6s0F1Z7f6/X8G4YXUCLstz7dJeAAAAAASUVORK5CYII=') no-repeat center;
	background-size: 0.2rem;
	position: absolute;
	top: 0;
	right: 0;
	z-index: 50;
}
.read_combox{
	padding: 0.12rem 0.16rem 0.08rem;
}
.read_txt{
	font-size: 0;
	line-height: 0;
	margin-bottom: 0.2rem;
	padding-left: 1px;
	max-height: 2.8rem;
	overflow: auto;
}
.read_txt span{
	display: inline-block;
	vertical-align: top;
	margin-bottom: 0.05rem;
	font-size: 0.16rem;
	width: 0.38rem;
	height: 0.4rem;
	text-align: center;
	line-height: 0.38rem;
	color: #333333;
	border: 1px solid rgba(186,1,5,0.5);
	margin-left: -1px;
}
.read_cm_title{
	display: flex;
	margin-bottom: 0.16rem;
	align-items: center;
}
.read_cm_title h5{
	font-size: 0.16rem;
	line-height: 0.24rem;
	color: #666666;
	font-weight: normal;
	flex: 1;
}
.read_cm_title .btn{
	display: block;
	width: 1.2rem;
	height: 0.32rem;
	line-height: 0.32rem;
	text-align: center;
	background: #FFD3D1;
	border-radius: 0.04rem;
	font-size: 0.14rem;
	color: #BA0105;
}
.read_cm_tarea textarea{
	display: block;
	width: 100%;
	height: 1.2rem;
	background: #F8F8F8;
	border: 0 none;
	border-radius: 0.06rem;
	font-size: 0.16rem;
	line-height: 0.24rem;
	color: #333333;
	padding: 0.1rem 0.12rem;
	overflow: auto;
	resize: none;
	outline: none;
	font-weight: 500;
	font-family: -apple-system-font, "Helvetica Neue", sans-serif;
}
.read_cm_tarea textarea::-moz-placeholder {
	color: #BBBBBB;
}
.read_cm_tarea textarea::-webkit-input-placeholder {
	color: #BBBBBB;
}
.popup_layer .ce_btn{
	padding: 0.12rem 0.15rem;
}
/*-- add 20240320 --*/
.yj_info_wrap{
	background: #ffffff;
	padding: 0.15rem;
	margin-top: 0.08rem;
}
.yj_info_wrap .title{
	font-size: 0.16rem;
	line-height: 0.24rem;
	color: #333333;
	font-weight: 500;
	margin-bottom: 0.12rem;
}
.icon_exp{
	display: inline-block;
	width: 0.24rem;
	height: 0.24rem;
	vertical-align: top;
	background: url(../images/error_tipimg.png) no-repeat center;
	background-size: 0.16rem;
}
.yj_infotable{
	width: 100%;
	border: 1px solid rgba(221, 221, 221, 0.5);
}
.yj_infotable thead{
	background: linear-gradient(0deg, #FFF 33.67%, #FFE0E0 100%);
}
.yj_infotable th{
	text-align: center;
	border: 1px solid rgba(221, 221, 221, 0.5);
	padding: 0.1rem 0.05rem;
	font-size: 0.13rem;
	line-height: 0.2rem;
	color: #333333;
	font-weight: 500;
	vertical-align: middle;
}
.yj_infotable td{
	text-align: center;
	border: 1px solid rgba(221, 221, 221, 0.5);
	padding: 0.1rem 0.05rem;
	font-size: 0.13rem;
	line-height: 0.2rem;
	color: #666666;
	vertical-align: middle;
}
.yj_info_page{
	padding: 0.2rem 0.15rem;
}
.yj_info_title{
	margin-bottom: 0.2rem;
}
.yj_info_title h2{
	text-align: center;
	font-size: 0.16rem;
	line-height: 0.22rem;
	color: #333333;
	font-weight: 500;
}
.yj_info_title .info{
	display: flex;
	margin-top: 0.12rem;
	justify-content: space-between;
	font-size: 0.14rem;
	line-height: 0.22rem;
	color: #666666;
}
.yj_info_tips{
	color: #666666;
	font-size: 0.13rem;
	line-height: 0.2rem;
	margin-top: 0.2rem;
}

.scroll_style{
	overflow: scroll;
}

/*同花顺视频*/
.queue_box {
	background: #ffffff;
	padding: 0.5rem 0.2rem 0.2rem;
	min-height: 3.1rem;
	margin-bottom: 0.08rem;
	text-align: center;
	line-height: 0.2rem;
	color: #999999;
}

.queue_box h5 {
	font-size: 0.24rem;
	line-height: 0.32rem;
	font-weight: normal;
	color: #333333;
	margin-bottom: 0.12rem;
}

.queue_level {
	width: 1.2rem;
	height: 1.2rem;
	margin: 0 auto 0.3rem;
	position: relative;
}

.queue_level .bg {
	display: block;
	width: 100%;
	height: 100%;
	background: url(../images/queue_bg.png) no-repeat center;
	background-size: 100%;
	-webkit-animation: allrotate 1.6s infinite linear;
	animation: allrotate 1.6s infinite linear;
}

.queue_level .pic {
	width: 100%;
	height: 100%;
	position: absolute;
	top: 0;
	left: 0;
	z-index: 50;
}

.queue_level .pic img {
	display: block;
	width: 100%;
	height: 100%;
}
/*协议table样式*/
.xy_cont table{
	width: 100% !important;
	border: 1px solid #333333;
	border-collapse: collapse;
	border-spacing: 0;
}
.xy_cont table th,
.xy_cont table td{
	border: 1px solid #333333;
}
.xy_cont table td p{
	text-indent: 0 !important;
}



/*-- add 20250417 start --*/
.txt_info_box{
	background: #ffffff;
	padding: 0.16rem 0.15rem;
	margin: 0.1rem 0;
}
.txt_info_box .title{
	font-size: 0.16rem;
	line-height: 0.24rem;
	font-weight: 500;
	margin-bottom: 0.12rem;
}
.txt_info_table{
	width: 100%;
	border: 1px solid #eeeeee;
}
.txt_info_table th{
	border: 1px solid #eeeeee;
	background: #f5f5f5;
	font-size: 0.14rem;
	line-height: 0.2rem;
	padding: 0.08rem;
	text-align: center;
	white-space: nowrap;
}
.txt_info_table td{
	border: 1px solid #eeeeee;
	padding: 0.08rem;
	font-size: 0.14rem;
	line-height: 0.2rem;
	vertical-align: middle;
}
.txt_info_table td .imp{
	color: #BA0105;
}
.toast_tipbox{
	position: fixed;
	top: 50%;
	left: 0;
	right: 0;
	padding: 0 0.24rem;
	text-align: center;
	transform: translateY(-50%);
	z-index: 3000;
}
.toast_tipbox .toast_wrap{
	display: inline-flex;
	vertical-align: top;
	text-align: left;
	background: rgba(0,0,0,0.7);
	border-radius: 0.06rem;
	padding: 0.12rem 0.15rem;
	color: #ffffff;
	font-size: 0.16rem;
	line-height: 0.24rem;
}
.toast_tipbox .toast_wrap .icon{
	width: 0.24rem;
	height: 0.24rem;
	margin-right: 0.1rem;
}
.toast_tipbox .toast_wrap .icon img{
	display: block;
	width: 100%;
}
.toast_tipbox .toast_wrap p{
	flex: 1;
	min-width: 0;
}

/*-- add 20250417 end --*/

/* 销户业务 20250605 start */
.cancel_reason {
  margin-bottom: 0.1rem;
}

.cancel_reason .title {
  padding: 0.1rem 0.15rem;
  line-height: 0.2rem;
  font-weight: normal;
  font-size: 0.14rem;
  color: #999;
}

.cancel_reason ul {
  background: #fff;
  padding-left: 0.15rem;
}

.cancel_reason ul li {
  border-bottom: 1px solid #eeeeee;
}

.cancel_reason ul li:last-child {
  border-bottom: 0 none;
}

.cancel_reason ul li .icon_radio {
  display: block;
  padding: 0.11rem 0.32rem 0.11rem 0;
  font-size: 0.16rem;
  color: #000;
}

.cancel_reason ul li .icon_radio:before {
  left: auto;
  right: 0.15rem;
  top: 0.14rem;
}

.cancel_reason ul li .icon_radio.checked:before {
  border-color: #e22729;
  background: #e22729 url(../images/check_ic01.png) no-repeat center;
  background-size: 0.18rem;
}

.ui.field.note {
  padding-top: 0.3rem;
}

.ui.field.note>.ui.label {
  height: 0.24rem;
  line-height: 0.2rem;
  padding-top: 0.1rem;
}

.ui.field>.teare02 {
  display: block;
  width: 100%;
  height: 0.64rem;
  overflow: auto;
  padding: 0.05rem 0.1rem 0.05rem 0;
  line-height: 0.2rem;
  border: 0 none;
  font-family: Hiragino Sans GB, Helvetica, STHeiti STXihei, Microsoft YaHei,
    Arial;
  font-size: 0.14rem;
  color: #ababab;
  outline: none;
  -webkit-user-select: auto;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  position: relative;
}

.ui.field>.teare02:empty:before {
  content: attr(placeholder);
  color: #ccc;
  font-size: 0.14rem;
}

.ui.field>.teare02:focus:before {
  content: none;
}

/* .header.fixed_header {
  z-index: 200;
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.header.fixed_header .header_inner {
  background: none;
  -webkit-transition: all 0.5s;
  transition: all 0.5s;
}

.header.fixed_header.active .header_inner {
  background: #8C0002;
  background-image: -webkit-linear-gradient(90deg, #8C0002 0%, #BA0105 100%);
  background-image: -moz-linear-gradient(90deg, #8C0002 0%, #BA0105 100%);
  background-image: -o-linear-gradient(90deg, #8C0002 0%, #BA0105 100%);
  background-image: -webkit-gradient(linear, 0 100%, 0 0, from(#8C0002), to(#BA0105));
  background-image: linear-gradient(90deg, #8C0002 0%, #BA0105 100%);
} */

.h5_home_page {
  background: #ffffff;
  min-height: 100%;
}

.h5_banner_box {
  margin-bottom: -30%;
  min-height: 2.6rem;
  position: relative;
}

.h5_banner_box img {
  display: block;
  width: 100%;
}

.h5_banner_box .tit {
  width: 100%;
  text-align: center;
  padding: 0 0.2rem;
  color: #ffffff;
  font-size: 0.16rem;
  line-height: 0.24rem;
  position: absolute;
  bottom: 0;
  left: 0;
  margin-bottom: 45%;
  z-index: 10;
}

.h5_banner_box .tit h2 {
  font-size: 0.40rem;
  line-height: 1.5;
  font-weight: 700;
}

.bus_home_must {
  margin: 0 0.2rem 0.2rem;
  padding: 0.2rem;
  position: relative;
  z-index: 50;
  border-radius: 0.1rem;
  background: #FEFFFE;
  box-shadow: 0px 2px 10px 0px rgba(164, 70, 70, 0.12);
  overflow: hidden;
  margin-bottom: 0.24rem;
}

.bus_home_must .title {
  text-align: center;
  font-size: 0.2rem;
  font-weight: 700;
  line-height: 1.4;
  margin-bottom: 0.18rem;
  position: relative;
  z-index: 50;
  color: #000000;
}

.bus_home_must .title span {
  position: relative;
  display: inline-block;
  vertical-align: top;
}

.bus_home_must .title span:before {
  content: "";
  width: 100%;
  height: 0.1rem;
  background: #FFECEB;
  border-radius: 0.02rem;
  position: absolute;
  bottom: 0;
  left: 0;
}

.bus_home_must .title span em {
  position: relative;
  z-index: 50;
  font-weight: 500;
}

.bus_home_must .list {
  position: relative;
  z-index: 50;
}

.bus_home_must .list p {
  padding: 0.04rem 0 0.04rem 0.18rem;
  font-size: 0.14rem;
  line-height: 1.5;
  position: relative;
  color: #666666;
}

.bus_home_must .list p:before {
  content: "";
  width: 0.06rem;
  height: 0.06rem;
  background: #999999;
  border-radius: 50%;
  position: absolute;
  top: 0.11rem;
  left: 0;
}

.bus_home_tips {
  margin: 0.2rem;
}

.bus_home_tips h5 {
  font-size: 0.16rem;
  line-height: 1.5;
  font-weight: 500;
  margin-bottom: 0.05rem;
}

.bus_home_tips p {
  font-size: 0.14rem;
  line-height: 0.2rem;
  color: #999999;
}

.result_page {
  background: #ffffff;
}

.result_tips {
  color: #55555e;
  background: none;
  padding-left: .5rem;
  padding-right: .5rem;
  padding: .32rem .5rem;
  text-align: center;
}

.result_tips h5 {
  font-size: .24rem;
  line-height: 1.33333;
  font-weight: 400
}

.result_tips p {
  font-size: .14rem;
  line-height: 1.3;
  color: #87878d;
  margin-top: .06rem
}

.result_tips .icon {
  display: block;
  width: .56rem;
  height: .56rem;
  font-size: .56rem;
  border-radius: 50%;
  line-height: 1;
  font-family: wt-iconfont !important;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  margin: 0 auto .3rem;
  position: relative;
  z-index: 10
}

.result_tips .icon.ing {
  color: #ff2840;
  background: url(../images/result_ing.png) no-repeat center;
  background-size: 100%;
}

.result_tips .icon.ok {
  background: url(../images/result_suc.png) no-repeat center;
  background-size: 100%;
}

.result_tips .icon.fail {
  background: url(../images/result_fail.png) no-repeat center;
  background-size: 100%;
}

.result_info {
  padding: 0 .16rem
}

.result_info ul {
  border-top: 1px solid var(--borderColor,hsla(0,0%,89.8%,.5))
}

.result_info ul li {
  border-bottom: 1px solid var(--borderColor,hsla(0,0%,89.8%,.5));
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding: .16rem 0;
  font-size: .16rem;
  line-height: 1.5
}

.result_info ul li .tit {
  display: block;
  min-width: 1rem
}

.result_info ul li p {
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  color: var(--tColorGray,#55555e);
  text-align: right
}

.result_info ul li p.error {
  color: var(--errorColor,#ff7015)
}

.result_info ul li .state {
  color: var(--tColorLightgray,#87878d)
}

.result_info ul li .state.error {
  color: var(--errorColor,#ff7015)
}
.h5_white_bg,.white_bg {
  background: #fff!important
}
/* .ce_btn {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding: .15rem .16rem
}

.ce_btn a {
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  margin-left: .12rem
}

.ce_btn a:first-child {
  margin-left: 0
}
.ce_btn.black {
  display: block
} */

.p_button {
  display: block;
  height: .44rem;
  line-height: 2.75;
  text-align: center;
  font-size: .16rem;
  border-radius: .5rem;
  font-weight: 500
}

.p_button.border {
  background: var(--buttonBorderBg,#fff0f0);
  color: var(--buttonBg1,#f93838)
}

.p_button.disabled {
  background: #ccc!important
}
.p_button {
  -webkit-transition: all .3s;
  transition: all .3s
}
.p_button {
  background: -webkit-linear-gradient(left,var(--buttonBg1,#f93838),var(--buttonBg2,#f93838));
  background: linear-gradient(90deg,var(--buttonBg1,#f93838),var(--buttonBg2,#f93838));
  color: #fff
}

.p_button.disabled {
  background: -webkit-linear-gradient(left,var(--buttonBg1,#f93838),var(--buttonBg2,#f93838))!important;
  background: linear-gradient(90deg,var(--buttonBg1,#f93838),var(--buttonBg2,#f93838))!important;
  color: #fff!important;
  opacity: .3
}

.p_button:active {
  background: -webkit-linear-gradient(left,#cc2033,#cc2033);
  background: linear-gradient(90deg,#cc2033,#cc2033)
}

.p_button.border:active {
  background: #fff0f0
}

.ce_btn.black .p_button {
  margin-top: .12rem;
  margin-left: 0
}
.ce_btn.black .p_button:first-child {
  margin-top: 0
}
.xh_acct_box {
  padding-top: 0.1rem;
}
.xh_acct_cont {
  background: #fff;
  margin-bottom: 0.1rem;
  position: relative;
}
.xh_user_info {
  padding: 0.1rem 0.15rem;
  line-height: 0.2rem;
}
.xh_user_info p {
  font-size: 0.16rem;
  color: #000;
}
.xh_user_info em {
  display: block;
  font-size: 0.13rem;
  color: #888;
}
.check_all {
  position: absolute;
  top: 50%;
  margin-top: -0.12rem;
  right: 0.15rem;
}
.check_all .icon_radio {
  padding-left: 0;
  padding-right: 0.32rem;
  font-size: 0.16rem;
  color: #000;
}
.check_all .icon_radio:before {
  left: auto;
  right: 0;
}
.check_all .icon_radio.checked:before {
  border: 1px solid #e22729;
  background: #e22729 url(../images/check_ic01.png) no-repeat center;
  background-size: 0.18rem;
}
.xh_acct_box .title {
  height: 0.32rem;
  padding: 0 0.15rem;
  line-height: 0.32rem;
  font-weight: normal;
  font-size: 0.14rem;
  color: #888;
  position: relative;
}
.xh_acct_box .title .info {
  font-size: 0.14rem;
  position: absolute;
  top: 0;
  right: 0.15rem;
}
.xh_acct_box .title .info {
  font-size: 0.12rem;
  position: absolute;
  top: 0;
  right: 0.15rem;
}
.xh_fund_acc_list {
  position: relative;
}
.xh_acct_cont ul {
  padding-left: 0.15rem;
}
.xh_acct_cont ul li {
  border-bottom: 1px solid #eee;
  position: relative;
  padding: 0.1rem 0.15rem 0 0;
  line-height: 0.2rem;
}
.xh_acct_cont ul li:last-child {
  border-bottom: 0 none;
}
.xh_acct_cont ul li p {
  font-size: 0.16rem;
  color: #000;
}
.otc_xh_account ul li p {
  padding: 0.1rem 0 0.1rem 0;
}
.xh_acct_cont ul li em {
  display: block;
  font-size: 0.13rem;
  color: #888;
}
.xh_acct_cont ul li:not(.xh_fund_acc_box):before {
  display: block;
  content: "";
  width: 0.16rem;
  height: 0.16rem;
  position: absolute;
  top: 50%;
  margin-top: -0.09rem;
  right: 0.15rem;
  border: 1px solid #ccc;
  -moz-border-radius: 100%;
  -webkit-border-radius: 100%;
  border-radius: 100%;
}
.xh_acct_cont ul li:not(.xh_fund_acc_box).checked:before {
  border: 1px solid #e22729;
  background: #e22729 url(../images/check_ic01.png) no-repeat center;
  background-size: 0.18rem;
}
.xh_acct_cont ul li .xh_fund_acc_list:before {
  display: block;
  content: "";
  width: 0.16rem;
  height: 0.16rem;
  position: absolute;
  top: 50%;
  margin-top: -0.09rem;
  right: 0.01rem;
  border: 1px solid #ccc;
  -moz-border-radius: 100%;
  -webkit-border-radius: 100%;
  border-radius: 100%;
}
.xh_fund_acc_box .icon_eye {
  right: 0.01rem;
}
.xh_acct_cont ul li .xh_fund_acc_list.checked:before {
  border: 1px solid #e22729;
  background: #e22729 url(../images/check_ic01.png) no-repeat center;
  background-size: 0.18rem;
}
.xh_zd_box {
  margin-left: 0.15rem;
  border-top: 1px solid #eee;
}
.xh_zd_box .tit {
  padding: 0.1rem 0.15rem 0.1rem 0;
  line-height: 0.24rem;
  color: #000;
  position: relative;
  overflow: hidden;
}
.xh_zd_box .tit h5 {
  font-weight: normal;
  font-size: 0.16rem;
  float: left;
}
.xh_zd_box .tit .link {
  display: inline-block;
  float: left;
  width: 0.24rem;
  height: 0.24rem;
  background: url(../images/tips_ic01.png) no-repeat center;
  background-size: 0.16rem 0.16rem;
}
.xh_zd_box .tit .check {
  height: 0.24rem;
  position: absolute;
  top: 0.1rem;
  right: 0.15rem;
}
.xh_zd_box .tit .check .icon_radio {
  font-size: 0.16rem;
  float: left;
  margin-left: 0.2rem;
  color: #000;
}
.xh_zd_box .tit .check .icon_radio:first-child {
  margin-left: 0;
}
.xh_zd_box .cont {
  border-top: 1px solid #eee;
  padding: 0.08rem 0.15rem 0.08rem 0;
  line-height: 0.18rem;
  color: #999;
  font-size: 0.12rem;
}
.xh_zd_box .cont p {
  padding-left: 0.24rem;
  position: relative;
}
.xh_zd_box .cont p:before {
  content: "";
  width: 0.15rem;
  height: 0.15rem;
  background: url(../images/tips_ic02.png) no-repeat center;
  background-size: 100% 100%;
  position: absolute;
  top: 0.01rem;
  left: 0;
}
.xh_acct_cont .add_btn {
  border-top: 1px solid #eee;
}
.xh_mustlist ul {
  background: #fff;
  padding-left: 0.15rem;
}
.xh_mustlist ul li {
  border-bottom: 1px solid #eee;
  padding: 0.1rem 0.15rem 0.1rem 0;
}
.xh_mustlist ul li:last-child {
  border-bottom: 0 none;
}
.xh_mustlist ul li h5 {
  line-height: 0.24rem;
  padding: 0.05rem 0 0.05rem 0.1rem;
  position: relative;
  font-size: 0.16rem;
  font-weight: normal;
  color: #000;
}
.xh_mustlist ul li h5:before {
  content: "";
  width: 0.03rem;
  height: 0.16rem;
  background: #e22729;
  -moz-border-radius: 0.02rem;
  -webkit-border-radius: 0.02rem;
  border-radius: 0.02rem;
  position: absolute;
  top: 0.09rem;
  left: 0;
}
.xh_mustlist ul li .cont {
  margin-top: 0.03rem;
  font-size: 0.14rem;
  color: #666;
  line-height: 0.22rem;
}
.xh_must_box .title {
  padding: 0.12rem 0.15rem;
  line-height: 0.2rem;
  font-size: 0.16rem;
  font-weight: normal;
  color: #000;
}
.xh_must_box .title span {
  padding-left: 0.12rem;
  position: relative;
}
.xh_must_box .title span:before {
  content: "";
  width: 0.03rem;
  height: 0.18rem;
  background: #2277cc;
  -moz-border-radius: 0.03rem;
  -webkit-border-radius: 0.03rem;
  border-radius: 0.03rem;
  position: absolute;
  top: 50%;
  margin-top: -0.09rem;
  left: 0;
}
.xh_must_item {
  background: #fff;
  margin: 0.1rem 0;
}
.xh_must_item:first-child {
  margin-top: 0;
}
.xh_must_item .tit {
  padding: 0.1rem 0.15rem;
  line-height: 0.2rem;
  position: relative;
}
.xh_must_item .tit p {
  font-size: 0.16rem;
  color: #000;
}
.xh_must_item .tit em {
  display: block;
  font-size: 0.13rem;
  color: #999;
}
.xh_must_item .tit .status {
  font-size: 0.14rem;
  position: absolute;
  top: 50%;
  margin-top: -0.1rem;
  right: 0.15rem;
  color: #999;
}
.xh_must_item .tit .status.error {
  color: #f85e6b;
}
.xh_must_item .tit .status.ok {
  color: #999;
}
.xh_must_item .list {
  border-top: 1px solid #eee;
  margin-left: 0.15rem;
  padding-top: 0.15rem;
  padding-bottom: 0.15rem;
}
.xh_must_item .list li {
  position: relative;
}
.xh_must_item .list li:before {
  content: "";
  width: 1px;
  height: 100%;
  position: absolute;
  top: 0.22rem;
  left: 0.23rem;
}
.xh_must_item .list li:last-child:before {
  display: none;
}
.xh_must_item .list li p {
  height: 0.44rem;
  padding: 0.12rem 0.15rem 0.12rem 0.45rem;
  position: relative;
  line-height: 0.2rem;
  font-size: 0.14rem;
  color: #ff9da6;
}
.xh_must_item .list li p:before {
  content: "";
  width: 0.26rem;
  height: 0.26rem;
  position: absolute;
  top: 50%;
  margin-top: -0.13rem;
  left: 0.11rem;
  z-index: 50;
}
.xh_must_item .list li.ok:before {
  background: #2277cc;
}
.xh_must_item .list li.ok p:before {
  background: #fff url(../images/tag_ok.png) no-repeat center;
  background-size: 0.18rem;
}
.xh_must_item .list li.error:before {
  background: #f85e6b;
}
.xh_must_item .list li.error p:before {
  background: #fff url(../images/tag_error.png) no-repeat center;
  background-size: 0.18rem;
}
.xh_must_item .list li .link {
  height: 0.24rem;
  line-height: 0.24rem;
  position: absolute;
  top: 0.11rem;
  right: 0.1rem;
  padding-right: 0.15rem;
  font-size: 0.14rem;
  color: #2277cc;
}
.xh_must_item .list li .link:after {
  display: block;
  content: "";
  width: 0.06rem;
  height: 0.12rem;
  position: absolute;
  top: 50%;
  margin-top: -0.06rem;
  right: 0;
  background: url(../images/arrow01.png) no-repeat center;
  background-size: 100%;
}
.xh_result_info {
  margin: 0.1rem 0;
}
.xh_result_info .tit {
  padding: 0 0.15rem;
  height: 0.33rem;
  line-height: 0.33rem;
  font-size: 0.14rem;
  color: #999;
  font-weight: normal;
}
.xh_result_info .cont {
  background: #fff;
}
.xh_result_info ul {
  padding-left: 0.15rem;
}
.xh_result_info ul li {
  border-bottom: 1px solid #eee;
  position: relative;
  padding: 0.12rem 0 0.12rem 0;
  line-height: 0.22rem;
  min-height: 0.44rem;
  font-size: 0.16rem;
  color: #000;
}
.xh_result_info ul li:last-child {
  border-bottom: 0 none;
}
.xh_result_info ul li span {
  display: block;
}
.xh_result_info ul li em {
  font-size: 0.14rem;
  color: #999;
  position: absolute;
  top: 0.12rem;
  right: 0.15rem;
}
.xh_result_info ul li em.ing {
  color: #999;
}
.xh_result_info ul li em.ok {
  color: #999;
}
.xh_result_info ul li em.error {
  color: #e22729;
}
.xh_result_info ul li .fail_reason {
  border-top: 1px solid #efefef;
  padding: 0.12rem 0.15rem 0 1.2rem;
  position: relative;
  margin-bottom: 0;
  margin-top: 0.12rem;
}
.xh_result_info ul li .fail_reason span {
  left: 0;
}
/* 销户业务 20250605 end */
