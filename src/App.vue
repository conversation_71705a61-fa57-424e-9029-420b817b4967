<template>
  <div id="app" class="app_wrap page_box bneedsfocus">
    <section class="main fixed" v-bind:class="{ white_bg: isWhite, scroll_style: isScroll }">
      <transition :name="`${$store.state.router.direction}`">
        <keep-alive :include="cachePath">
          <router-view v-if="isRouterAlive" />
        </keep-alive>
      </transition>
    </section>
    <loading></loading>
  </div>
</template>

<script>
import Loading from '@/views/common/loading.vue'
export default {
  name: 'App',
  components: { Loading },
  provide () {
    return {
      reload: this.reload
    }
  },
  data () {
    return {
      // 头部的背景色样式，默认为红色，部分特殊页面设置为白色
      // isWhite: this.$store.state.isWhite
      isRouterAlive: true
    }
  },
  computed: {
    cachePath () {
      return this.$store.state.cachePath
    },
    isWhite () {
      return this.$store.state.isWhite
    },
    isScroll () {
      return this.$store.state.isScroll
    }
  },
  created () {
    if (location.href.indexOf('thsgb') > -1 || $h.getSession('fromTHSGB')) {
      import('@/plugins/ths/bridge.js')
    }
  },
  mounted () {
    window.$this = this
    // document.addEventListener('click', function(argument) {
    //     console.log(document.activeElement)
    // })
  },
  methods: {
    pageBack () {
      this.$store.commit('updateIsWhite', false)
      this.$router.go(-1)
    },
    pageBusinessRecords () {
      this.$store.commit('updateIsWhite', true)
      this.$router.push({ name: 'businessRecords', params: {} })
    },
    // 重新加载当前页面
    reload () {
      this.isRouterAlive = false
      this.$nextTick(function () {
        this.isRouterAlive = true
      })
    }
  }
}
</script>
<style>
/* page transition css  start*/
.app_wrap {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  overflow: hidden;
  height: 100%;
}
.page_header {
  position: relative;
  z-index: 0;
}
.page_main {
  position: absolute;
  left: 0;
  top: 0px;
  right: 0;
  bottom: 0;
  display: -webkit-box;
  display: flex;
  -webkit-box-orient: vertical;
  box-orient: vertical;
  flex-direction: column;
}
.left-enter-active,
.left-leave-active,
.right-enter-active,
.right-leave-active {
  transition: all 0.3s;
}
.left-enter {
  opacity: 0.5;
  transform: translate3d(100%, 0, 0);
}
.left-leave-active {
  opacity: 0.5;
  transform: translate3d(-100%, 0, 0);
}
.right-enter {
  opacity: 0.5;
  transform: translate3d(-100%, 0, 0);
}
.right-leave-active {
  opacity: 0.5;
  transform: translate3d(100%, 0, 0);
}
/* page transition css  end*/

/* thinkive-hui root css */
html {
  font-size: 100px;
  touch-action: none;
}
</style>
