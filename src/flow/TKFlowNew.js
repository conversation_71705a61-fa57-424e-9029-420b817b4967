import crypto from 'crypto-js'
import {
  commonService
} from '@/service/comServiceNew'

/**
 * 创建流程编号
 *
 * @param {userId} 用户编号
 * @param {flowName} 流程名称
 */
function createFlowId (userId, flowName) {
  return crypto.MD5([userId, flowName].join('')).toString(crypto.enc.Hex)
}

/**
 * 加载流程 流程开始时调用
 *
 * @param {flowId} 流程编号
 * @param {flowName} 流程名称
 * @param {callback} 请求成功回调
 */
function loadFlow (flowId, flowName, ctrlParam) {
  return invoke(7001, {
    flow_id: flowId,
    flow_name: flowName
  }, ctrlParam)
}

/**
 * 执行流程的当前步骤
 *
 * @param {flowId} 流程编号
 * @param {flowName} 流程名称
 * @param {callback} 请求成功回调
 */
function executeFlow (flowId, flowName, parameters, ctrlParam) {
  return invoke(7002, Object.assign({
    flow_id: flowId,
    flow_name: flowName
  }, parameters || {}), ctrlParam)
}

/**
 * 回退当前流程到指定步骤
 *
 * @param {flowId} 流程编号
 * @param {flowName} 流程名称
 * @param {stepName} 步骤名称
 * @param {callback} 请求成功回调
 */
function rollbackFlow (flowId, flowName, stepName, ctrlParam) {
  return invoke(7003, {
    flow_id: flowId,
    flow_name: flowName,
    step_name: stepName
  }, ctrlParam)
}

/**
 * 查询流程执行日志
 *
 * @param {flowId} 流程编号
 * @param {flowName} 流程名称
 * @param {callback} 请求成功回调
 */
function queryFlowLogs (flowId, flowName, ctrlParam) {
  return invoke(7004, {
    flow_id: flowId,
    flow_name: flowName
  }, ctrlParam)
}

/**
 * 驳回流程到指定步骤
 *
 * @param {flowId} 流程编号
 * @param {flowName} 流程名称
 * @param {callback} 请求成功回调
 */
function rejectFlow (flowId, flowName, stepNames, ctrlParam) {
  stepNames = stepNames || ''
  return invoke(7005, {
    flow_id: flowId,
    flow_name: flowName,
    step_name: stepNames
  }, ctrlParam)
}

/**
 * 查询流程所有的步骤
 *
 * @param {flowName} 流程名称
 * @param {callback} 请求成功回调
 */
function queryFlowInfo (flowName, ctrlParam) {
  return invoke(7006, {
    flow_name: flowName
  }, ctrlParam)
}

/**
 * 初始化流程步骤参数
 *
 * @param {flowId} 流程编号
 * @param {flowName} 流程名称
 * @param {callback} 请求成功回调
 */
function initFlowStepParameters (flowId, flowName, parameters, ctrlParam) {
  return invoke(7007, Object.assign({
    flow_id: flowId,
    flow_name: flowName,
  }, parameters || {}), ctrlParam)
}

/**
 * 执行流程的拦截器
 *
 * @param {flowName} 流程名称
 */
function executeFlowInterceptor (flowId, flowName, parameters, ctrlParam) {
  return invoke(7008, Object.assign({
    flow_id: flowId,
    flow_name: flowName
  }, parameters || {}), ctrlParam)
}

/**
 *
 * @param {funcNo} 调用功能号
 * @param {parameters} 调用参数
 * @param {sc} 调用成功回调
 * @param {ec} 调用失败回调
 */
function invoke (funcNo, parameters, ctrlParam) {
  return commonService(funcNo, parameters, ctrlParam)
}
export default {
  createFlowId,
  executeFlowInterceptor,
  initFlowStepParameters,
  queryFlowInfo,
  rejectFlow,
  queryFlowLogs,
  rollbackFlow,
  executeFlow,
  loadFlow
}
