/**
 * 原生壳子调用H5方法
 * 集合写在这里，供原生调用H5
 */

import {
  ssoLogin,
  closeYgt
} from '@/common/sso'
/**
 * 公用
 * 日期控件回调H5
 * 说明：日期控件的调用必须要写在页面UI的事件处理函数中，否则多模块开发原生无法获取到当前激活的模块从而导致无法回调H5
 * @param funcNo 50251
 * @param date String 日期 Y
 * @param selector String H5的元素选择器 N
 */
export const function50251 = function (paramMap) {
  alert(JSON.stringify(paramMap))
}

/**
 * 拍照回调
 */
export const function60050 = function (paramMap) {
  window.imgCallBack && window.imgCallBack(paramMap)
}

/**
 * 双向视频回调
 */
export const function60051 = function (paramMap) {
  window.videoCallBack && window.videoCallBack(paramMap)
}

/**
 * 通知H5登录信息（状态）更新
 * actionType : 1：登陆、2：退出登陆 3：开户完成事件4：通知主题改变 5:业务办理结束
 * 1001：微信小确灵授权页面
 * 或其他扩展类型（10X开头，如100，101）
 * externalRadio：直接将数据给外部发广播通知
 */
export const function60098 = function (paramMap) {
  // 模拟登录&模拟退出
  if (paramMap.actionType === '1' && (paramMap.accountType === '1A' || paramMap.accountType === '1B')) {
    ssoLogin(null, paramMap.accountType)
  } else if (paramMap.actionType === '2') {
    closeYgt(0, paramMap.accountType)
  }
  // window.ssoCallBack && window.ssoCallBack(paramMap)
}

/**
 * 银行卡识别回调
 */
export const function60052 = function (paramMap) {
  window.bankOcrCallBack && window.bankOcrCallBack(paramMap)
}

/**
 * 监听手机返回键
 */
export const function50107 = function (paramMap) {
  window.phoneBackBtnCallBack && window.phoneBackBtnCallBack(paramMap)
}
