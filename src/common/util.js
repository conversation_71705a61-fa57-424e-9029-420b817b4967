import {
  getDict, businessList
} from '@/service/comServiceNew'

/**
 * 身份证号获取年龄 (注：恒生系统设定只要过了生日，年龄自动+1)
 * @param {*} c 身份证号码 15或18位
 * @returns
 */
export function idCardToAge (c) {
  var UUserCard = c
  if (UUserCard != null && UUserCard != '') {
    // 获取年龄
    var myDate = new Date()
    var year = myDate.getFullYear() + ''
    var month = myDate.getMonth() + 1
    var day = myDate.getDate()
    // var age = myDate.getFullYear() - UUserCard.substring(6, 10) - 1
    // if (UUserCard.substring(10, 12) < month || UUserCard.substring(10, 12) == month && UUserCard.substring(12, 14) <= day) {
    //   age++
    // }
    // return age
    // 获取年龄
    // var myDate = new Date()
    // var month = myDate.getMonth() + 1;
    // var day = myDate.getDate();
    month < 10 && (month = '0' + month)
    day < 10 && (day = '0' + day)
    var age = ((year + month + day) - UUserCard.substring(6, 14)) / 10000;
    if (((year + month + day) - UUserCard.substring(6, 14)) % 10000 === 0) {
      return --age
    } else {
      return Math.floor(age)
    }
  }
}
/**
 * 身份证号获取性别值
 * @param c 身份证号码 15或18位
 * @returns {number} 0男 1女     注意大中台版本性别数据字典是 0男 1女
 */
export function idCardToSex (c) {
  let i = 1
  if (c) {
    i = c.length === 15 ? +c.charAt(14) % 2 : +c.charAt(16) % 2
  }
  return (i + 1) % 2
}

/**
 * 身份证号获取出生日期
 * @param c 身份证号码 15或18位
 * @returns 返回格式 1994-07-01
 */
export function idCardToBirthday (c) {
  if (c) {
    if (c.length === 18) {
      return c.replace(/\d{6}(\d{4})(\d{2})(\d{2})\d{3}[\dXx]/, '$1-$2-$3')
    } else if (c.length === 15) {
      return c.replace(/\d{6}(\d{2})(\d{2})(\d{2})\d{3}/, '19$1-$2-$3')
    }
  }
}

/**
 * 查询字典方法 参数为对象
 * 可以直接传入key或者value来返回对应的值 比如[{a:1,b:2}]  传入key=a返回1 传入value=1返回a
 * 也可以不传key和value 直接返回整个字典数组
 * @param d{type 数据字典类型 Y, key 查询键, value 查询值}
 * @param f 回调函数 参数返回数组或对象 [{key:1, value:2}]
 */
export function queryDictionary (d, f) {
  let dictionaryCacheName = 'dictionary_' + d.type
  let callback = function (data) {
    if (data && data.length > 0) {
      let arr = []
      for (let s = 0;s < data.length;s++) {
        let t = {
          key: data[s].itemValue,
          value: data[s].itemName,
          type: d.type,
          index: s
        }
        if ((d.key && d.key === t.key) || (d.value && d.value === t.value)) {
          f(t)
          return
        }
        arr.push(t)
      }
      f(arr)
      return
    }
    f('')
  }

  let dc = sessionStorage.getItem(dictionaryCacheName)
  if (dc) {
    callback(JSON.parse(dc))
  } else {
    getDict({
      enumNo: d.type
    }).then(data => {
      if (data.error_no === '0' && data.results && data.results.length > 0) {
        sessionStorage.setItem(
          dictionaryCacheName,
          JSON.stringify(data.results)
        )
        callback(data.results)
      } else {
        callback()
      }
    })
  }
}

/**
 * 校验输入框数据格式
 * @param $el 需要检测格式的input对象
 * @param isShowTip 是否弹出错误提示
 * @returns 返回检验结果，通过为空字符串
 */
export function checkInput ($el, isShowTip) {
  if (isShowTip == undefined) {
    isShowTip = true
  }
  let flag = ''
  let $this = $el
  let val = $this.value.trim()
  // 处理：非必填项如果为空时，不做校验，不为空时做校验
  if (!!$this.must && $this.must === 'no') {
    if ($h.isEmptyString(val)) {
      return flag
    }
  }
  if ($h.isEmptyString(val)) {
    flag = '请输入' + $this.name
    isShowTip ? _hvueToast({
      mes: flag
    }) : ''
    return flag
  }
  if (!!$this.minlength && val.length < $this.minlength) {
    flag = '请输入最低为' + $this.minlength + '位的' + $this.name
    isShowTip ? _hvueToast({
      mes: flag
    }) : ''
    return flag
  }
  if ($this.format) {
    if ($this.format === 'tel,phone') { // 电话或手机号
      if (!$h.isTel(val) && !(/^1\d{10}$/.test(val))) {
        flag = $this.name + '输入格式有误，请重填'
        isShowTip ? _hvueToast({
          mes: flag
        }) : ''
        return flag
      }
    } else if ($this.format === 'name') {
      // let reg = /^[\u4E00-\u9FA5]{2,4}$/
      let reg = /^[\u4e00-\u9fa5\.·]{1,10}$/
      if (!reg.test(val)) {
        flag = $this.name + '输入格式有误，请重填'
        isShowTip ? _hvueToast({
          mes: flag
        }) : ''
        return flag
      }
      if (val.length < 2) {
        flag = $this.name + '输入格式有误，请重填'
        isShowTip ? _hvueToast({
          mes: flag
        }) : ''
        return flag
      }
    } else if ($this.format === 'pwd') {
      if (!$h.isNum(val)) {
        flag = $this.name + '输入格式有误，请重填'
        isShowTip ? _hvueToast({
          mes: flag
        }) : ''
        return flag
      }
      if (!isStrongPwd(val)) {
        flag = '请勿输入简单组合的' + $this.name
        isShowTip ? _hvueToast({
          mes: flag
        }) : ''
        return flag
      }
    } else if (($this.format === 'enNum' && !$h.isEnNum(val)) || ($this.format === 'phone' && !(/1\d{10}/.test(val))) || ($this.format === 'email' && !$h.isEmail(val)) || ($this.format === 'cnEnNum' && !$h.isCnEnNum_(val)) || ($this.format === 'num' && !$h.isNum(val))) {
      // 英文数字/手机号/电子邮箱/身份证号/中文英文数字/数字
      flag = $this.name + '输入格式有误，请重填'
      isShowTip ? _hvueToast({
        mes: flag
      }) : ''
      return flag
    } else if ($this.format === 'idno' && !checkIDCard(val)) {
      // 英文数字/手机号/电子邮箱/身份证号/中文英文数字/数字
      flag = $this.name + '输入格式有误，请重填'
      isShowTip ? _hvueToast({
        mes: flag
      }) : ''
      return flag
    }
  }
  return flag
}
/**
 * 判断字符串是否为强密码
 * @param {*} str
 */
export function isStrongPwd (str) {
  // 匹配4位顺增或顺降
  let pattern = /(?:0(?=1)|1(?=2)|2(?=3)|3(?=4)|4(?=5)|5(?=6)|6(?=7)|7(?=8)|8(?=9)){3}\d/
  if (pattern.test(str)) {
    return false
  }
  pattern = /(?:9(?=8)|8(?=7)|7(?=6)|6(?=5)|5(?=4)|4(?=3)|3(?=2)|2(?=1)|1(?=0)){3}\d/
  if (pattern.test(str)) {
    return false
  }
  // 匹配3位以上的重复数字 字母
  pattern = /(\w)*(\w)\2{2}(\w)*/g
  if (pattern.test(str)) {
    return false
  }
  return true
}

// 函数参数必须是字符串，因为二代身份证号码是十八位，而在javascript中，十八位的数值会超出计算范围，造成不精确的结果，导致最后两位和计算的值不一致，从而该函数出现错误。
// 详情查看javascript的数值范围
export function checkIDCard (idcode) {
  // 加权因子
  let weight_factor = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
  // 校验码
  let check_code = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2']

  let code = idcode + ''
  let last = idcode[17] // 最后一位

  let seventeen = code.substring(0, 17)

  // ISO 7064:1983.MOD 11-2
  // 判断最后一位校验码是否正确
  let arr = seventeen.split('')
  let len = arr.length
  let num = 0
  for (let i = 0;i < len;i++) {
    num = num + arr[i] * weight_factor[i]
  }

  // 获取余数
  let resisue = num % 11
  let last_no = check_code[resisue]

  // 格式的正则
  // 正则思路
  /**
   * 第一位不可能是0
   * 第二位到第六位可以是0-9
   * 第七位到第十位是年份，所以七八位为19或者20
   * 十一位和十二位是月份，这两位是01-12之间的数值
   * 十三位和十四位是日期，是从01-31之间的数值
   * 十五，十六，十七都是数字0-9
   * 十八位可能是数字0-9，也可能是X
   */
  let idcard_patter = /^[1-9][0-9]{5}([1][9][0-9]{2}|[2][0][0|1][0-9])([0][1-9]|[1][0|1|2])([0][1-9]|[1|2][0-9]|[3][0|1])[0-9]{3}([0-9]|[X])$/

  // 判断格式是否正确
  let format = idcard_patter.test(idcode)

  // 返回验证结果，校验码和格式同时正确才算是合法的身份证号码
  return !!(last === last_no && format)
}

/**
 * 查询业务标签配置
 * @param {业务编号} code
 */
export function queryBusiness (code, f) {
  let callback = function (data) {
    if (data && data.length > 0) {
      let arr = []
      for (let s = 0;s < data.length;s++) {
        let t = data[s]
        if (code && code === t.businessCode) {
          f(t)
          return
        }
        arr.push(t)
      }
      f(arr)
      return
    }
    f('')
  }

  let dc = $h.getSession('allBusiness')
  if (dc) {
    callback(dc)
  } else {
    businessList({ source: 2 }, {}).then(
      res => {
        if (res.error_no === '0') {
          let allBusiness = []
          let allBusinessArr = []
          let hotLable = []
          for (let i = 0;i < res.ismpCompQueryMenu.length;i++) {
            let el = res.ismpCompQueryMenu[i]
            let menus = (el.businessMenu = JSON.parse(el.businessMenu))
            for (let j = 0;j < menus.length;j++) {
              let menu = menus[j]
              menu.mobileImgUrl =
                (process.env.NODE_ENV == 'development' ? '' : ROUTER_BASE) +
                menu.mobileImgUrl
              if (menu.isHot === '1') {
                hotLable.push(menu)
              }
              allBusiness.push(menu)
            }
            allBusinessArr.push(el)
          }
          $h.setSession('allBusinessArr', allBusinessArr)
          $h.setSession('allBusiness', allBusiness)
          $h.setSession('hotLable', hotLable)
          callback(allBusiness)
        } else {
          _hvueToast({
            icon: 'error',
            mes: res.error_info
          })
        }
      },
      err => {
        console.log(err)
      }
    )
  }
}

/**
 * @desc 同花顺code映射业务办理
 * @param {String}} step
 */
export function thscodeToType(thscode){
  // http://127.0.0.1:8183/osoa_h5_dev/views/business?type=zhzh
  let thscodeObj = {
    grzl: 'zlxg',
    sfz: 'sfzgx',
    sjh: 'xgsjh',
    fxcp: 'fxcp',
    xgmm: 'xgmm',
    cyb: 'cybkt',
    kcb: 'kcbkt',
    kzz: 'kzzkt',
    ggt_h: 'hgtkt',
    ggt_s: 'sgtkt',
    bjs: 'bjstzz',
    xsb: 'xsbkt',
    rzrq: '', //--
    czmm: 'czmm',
    fxjskt: 'fxjskt',
    cgyhbg: 'sfcg',
    kcbcz: 'kcbczc'
  }
  return thscodeObj[thscode];
}

/**
 * 校验证件类型是否港澳台通行证/居住证
 */
export function checkHMTCardType() {
  const foreignFlag = ['I']; //外国人证件
  const HkMacTwFlag = ['G', 'H', 'l']; //港澳通行证是G，台湾通行证是H 港澳台居住证是l
  const ygtUserInfo = $h.getSession('ygtUserInfo', { decrypt: false }); // 用户信息
  return ygtUserInfo && (HkMacTwFlag.includes(ygtUserInfo.identityType) || foreignFlag.includes(ygtUserInfo.identityType));
}
