import '@/nativeShell/nativeCallH5'
import {
  userLogin,
  decodeRsa
} from '@/service/comServiceNew'

/**
 * 统一登录状态判断
 * @param {*} to
 * @param {*} next
 * @param {*} accountType 登录类型 // 普通交易：“accountType” ：“1A” 两融：“accountType” ：“1B”手机号码：“accountType” ：“2C”
 */
export function checkLogin (to, next, accountType, vueComponentObj) {
  accountType = accountType || '1A' // 默认登录普通交易账户
  let param = {
    'funcNo': '50041',
    // 'key': 'fxcAccountInfo'
    'key': 'fxcAccountInfo_' + accountType
  }
  let resultVo = $h.callMessageNative(param)
  if (resultVo.error_no != 0) {
    _hvueToast({
      mes: resultVo.error_info
    })
  } else {
    let common_login_state = resultVo.results[0].value
    if (common_login_state) {
      if (to.path === '/resetPwd' || to.query.type === 'czmm') {
        closeYgt(1, accountType, 0, 0)
        next()
        return
      }
      let ygtUserInfo = $h.getSession('ygtUserInfo', {decrypt: false})
      // 兼容ios返回结果为字符串
      if (typeof common_login_state === 'string' && common_login_state !== '') {
        common_login_state = JSON.parse(common_login_state)
      }
      if (!!ygtUserInfo && !!ygtUserInfo.userId && common_login_state.stock_account == ygtUserInfo.fundAccount) {
        // 已做模拟登录,直接下一步
        next()
      } else {
        // 去做模拟登录
        ssoLogin(next, accountType, vueComponentObj)
      }
    } else {
      if (to.path === '/resetPwd' || to.query.type === 'czmm') {
        next()
        return
      }
      // 未统一登录时，保存目标页面，调用sdk打开统一登录页面
      $h.setSession('toPage', to)
      let param = {
        'funcNo': '60099',
        'moduleName': 'open',
        'actionType': '1', // 1：登陆、2：退出登陆 3：开户完成事件4：通知主题改变 5:业务办理结束
        params: {
          'accountType': accountType // 普通交易：“accountType” ：“1A” 两融：“accountType” ：“1B”手机号码：“accountType” ：“2C”
        }
      }
      $h.callMessageNative(param)
    }
  }
}

/**
 * 网厅的模拟登陆
 * @param {*} next
 * @param {*} accountType 登录类型 // 普通交易：“accountType” ：“1A” 两融：“accountType” ：“1B”手机号码：“accountType” ：“2C”
 */
export function ssoLogin (next, accountType, vueComponentObj) {
  accountType = accountType || '1A' // 默认登录普通交易账户
  // 获取统一登录信息
  let param = {
    'funcNo': '50041',
    // 'key': 'fxcAccountInfo'
    'key': 'fxcAccountInfo_' + accountType
  }
  let resultVo = $h.callMessageNative(param)
  let common_login_state = resultVo.results[0].value
  // 兼容ios返回结果为字符串
  if (typeof common_login_state === 'string' && common_login_state !== '') {
    common_login_state = JSON.parse(common_login_state)
  }
  // 网厅暂时只支持普通交易账户登录
  // if (accountType != '1A') {
  //   $this.$router.push({
  //     name: 'index'
  //   })
  //   _hvueAlert({
  //     title: '信用账户登录信息',
  //     mes: common_login_state
  //   })
  //   return
  // }

  // 获取统一登录token传给后段
  let param2 = {
    'funcNo': '50041',
    'key': 'temp_token_ismp'
  }
  let resultVo2 = $h.callMessageNative(param2)
  let temp_token_ismp = resultVo2.results[0].value
  // 调用网厅登录
  userLogin({
    accountType: accountType == '1A' ? '1' : '2',
    account: common_login_state.stock_account,
    password: common_login_state.password,
    tempTokenIsmp: temp_token_ismp
  }).then(
    res => {
      console.log(res)
      if (res.error_no === '0') {
        // 保存用户信息
        let userInfo = res.userInfo[0]
        userInfo.riskLevelDesc = (res.custTypeCheck !== undefined) ? res.custTypeCheck[0].riskLevelDesc : ''
        // $h.setSession('ygtUserInfo', userInfo, {encrypt: false})

        let ygtUserInfo = $h.getSession('ygtUserInfo', {decrypt: false}) || {}

        if (accountType == '1A') {
          if (ygtUserInfo.fundAccountXy) {
            userInfo.fundAccountXy = ygtUserInfo.fundAccountXy
          } else {
            userInfo.fundAccountXy = ''
          }
          $h.setSession('isLogin', true)
          $h.setSession('ygtUserInfo', userInfo, {encrypt: false})
        } else {
          userInfo.fundAccountXy = userInfo.fundAccount
          if (ygtUserInfo.fundAccount) {
            userInfo.fundAccount = ygtUserInfo.fundAccount
          } else {
            userInfo.fundAccount = ''
          }
          $h.setSession('isLoginXy', true)
          $h.setSession('ygtUserInfo', userInfo, {encrypt: false})
        }
        // 对比用户是否一致
        if (ygtUserInfo.userId && userInfo.userId !== ygtUserInfo.userId) {
          if (accountType == '1A') {
            _hvueToast({
              timeout: 4000,
              mes: '已登录的信用账户，与当前登录的账户不一致，将退出已登录的账户。',
              callback: () => {
                closeYgt(1, '1B')
                afterCallback(next, vueComponentObj)
              }
            })
          }
          if (accountType == '1B') {
            _hvueToast({
              timeout: 4000,
              mes: '已登录的普通账户，与当前登录的账户不一致，将退出已登录的账户。',
              callback: () => {
                closeYgt(1, '1A')
                afterCallback(next, vueComponentObj)
              }
            })
          }
        } else {
          afterCallback(next, vueComponentObj)
        }
      } else {
        _hvueToast({
          icon: 'error',
          mes: res.error_info
        })
      }
    },
    err => {
      console.log(err)
    }
  )
}

export function afterCallback (next, vueComponentObj) {
  if (next) {
    next()
  } else if ($hvue.platform === '0') {
    let toPage = $h.getSession('toPage')
    if (toPage) {
      vueComponentObj.push({
        name: toPage.name,
        params: toPage.params,
        query: toPage.query
      })
    } else {
      vueComponentObj.push({
        name: 'index'
      })
    }
  } else {
    window.location.reload()
  }
}

/**
 * 关闭webView
 * @param {*} isLogout 是否退出登录 1是 0否 默认否
 * @param {*} accountType 账户类型 默认1A 普通交易：“accountType” ：“1A” 两融：“accountType” ：“1B”手机号码：“accountType” ：“2C”
 * @param {*} isOpenLogin 是否打开登录窗口 1是 0否 默认否
 * @param {*} isCloseYgt 是否关闭模块，1是 0否 默认否
 */
export function closeYgt (isLogout, accountType, isOpenLogin, isCloseYgt, vueComponentObj) {
  isLogout = isLogout || 0
  accountType = accountType || '1A'
  isOpenLogin = isOpenLogin || 0
  isCloseYgt = isCloseYgt || 0

  // 退出操作时，清除云柜台用户缓存数据
  if (isLogout == 1) {
    let ygtUserInfo = $h.getSession('ygtUserInfo', {decrypt: false}) || {}
    if (accountType == '1B') { // 退出两融账号
      $h.clearSession('isLoginXy')
      ygtUserInfo.fundAccountXy = ''
    } else if (accountType == '1A') {
      $h.clearSession('isLogin')
      ygtUserInfo.fundAccount = ''
    }
    $h.setSession('ygtUserInfo', ygtUserInfo, {encrypt: false})
    if (ygtUserInfo.fundAccountXy == '' && ygtUserInfo.fundAccount == '') {
      $h.clearSession('ygtUserInfo')
    }
    $h.clearSession('cardDetails')
    $h.clearSession('serivalId')
    $h.clearSession('ocrUserInfo')
    $h.clearSession('rejectList')
    let param = {
      'funcNo': '60099',
      'moduleName': 'open',
      'actionType': '2', // 1：登陆、2：退出登陆 3：开户完成事件4：通知主题改变 5:业务办理结束
      params: {
        'accountType': accountType
      }
    }
    if ($hvue.platform !== '0') {
      $h.callMessageNative(param)
    }
  }
  if (isOpenLogin == 1) {
    if ($hvue.platform === '0') {
      vueComponentObj.push({
        name: 'login',
        query: {loginType: accountType == '1A' ? '1' : '2'}
      })
      return
    }
    let param = {
      'funcNo': '60099',
      'moduleName': 'open',
      'actionType': '1', // 1：登陆、2：退出登陆 3：开户完成事件4：通知主题改变 5:业务办理结束
      params: {
        'accountType': accountType // 普通交易：“accountType” ：“1A” 两融：“accountType” ：“1B”手机号码：“accountType” ：“2C”
      }
    }
    $h.callMessageNative(param)
  }
  if (isCloseYgt == 1) {
    if ($h.getSession('fromTHSGB')) {
      $h.clearSession();
      // 同花顺关闭webview的方法
      callNativeHandler(
        'JSWangTingEvent',
        {
          action: 'CloseWebVC', // 关闭当前webView
          param: {}
        },
        function () {}
      )
    } else {
      $h.callMessageNative({
        'funcNo': '50114',
        'moduleName': 'open',
        'targetModule': 'open'
      })
    }
  }
}

//获取同花顺公版的资金账号
export function ssoTHSGBFund(encryData) {
  return new Promise((resolve, reject) => {
    let reqParams = {
      encryData: encryData
    }
    decodeRsa(reqParams).then((res) => {
      if (res.error_no === '0') {
        let results = res.DataSet;
        results.length ? resolve(results[0]) : reject();
      } else {
        _hvueToast({
          icon: 'error',
          mes: res.error_info
        });
        reject()
      }
    })

  })
}
