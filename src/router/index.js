import {
  setPageTitle,
  setTransitionName
} from 'thinkive-hvue'
import Vue from 'vue'
import Router from 'vue-router'

// 可以根据菜单栏目 把页面路由分组到各栏目模块中，再引入来扩展
import ISMP from './ismp'

import '@/nativeShell/nativeCallH5'

import {
  checkLogin
} from '@/common/sso'

Vue.use(Router)

/*
  路由懒加载采用官方推荐的ES6 import()语法，
  webpackChunkName相同会打包成一个模块，不同则为不同模块
*/

const router = new Router({
  // 路由模式：history, hash. 默认设置为 history
  mode: 'history',
  // 采用history模式时，要设置base路径; hash模式不用设置(注释掉)
  // 环境变量ROUTER_BASE同步config.build.assetsPublicPath设置
  base: ROUTER_BASE,
  scrollBehavior (to, from, savedPosition) {
    // 页面滚动行为, 保持缓存页面的滚动位置, 否则返回页面顶部
    if (savedPosition) {
      return savedPosition
    } else {
      return {
        x: 0,
        y: 0
      }
    }
  },
  routes: [
    {
      path: '/index',
      name: 'index',
      component: () =>
                import(/* webpackChunkName: "index" */ '@/views/index.vue'),
      meta: {
        title: '首页'
      }
    },

    {
      path: '/',
      redirect: 'index'
    },

    ...ISMP
  ]
})

router.beforeEach((to, from, next) => {
  // debugger
  let fromTHSGB = $h.getSession('fromTHSGB') // 是否从同花顺公版进来的
  let op_station = $h.getSession('op_station')// 同花顺公版的渠道信息
  let source = $h.getSession('source')// 单独跳转业务时的来源
  let thsgb_token = $h.getSession('thsgb_token')// 同花顺带过来的token(资金账号)
  let thscode = $h.getSession('thscode')// 同花顺带过来的业务code(单独跳转才有)

  if (!fromTHSGB) {
    fromTHSGB = to.query.channel === 'thsgb'
    $h.setSession('fromTHSGB', fromTHSGB)
  }
  if (!op_station) {
    op_station = to.query.op_station || ''
    $h.setSession('op_station', op_station)
  }
  if (!source) {
    source = to.query.source || ''
    $h.setSession('source', source)
  }
  if (!thsgb_token) {
    thsgb_token = to.query.token || ''
    $h.setSession('thsgb_token', thsgb_token)
  }
  if (!thscode) {
    thscode = to.query.thscode || ''
    $h.setSession('thscode', thscode)
  }
  // 页面切换动画
  setTransitionName()
  let loginType = to.query.loginType || $h.getSession('loginType') || 1
  let ygtUserInfo = $h.getSession('ygtUserInfo', {decrypt: false}) || {}
  // h5访问 或 登录和重置密码 不做登录拦截
  if (to.matched.some(record => record.path === '/login') ||
  to.matched.some(record => record.path === '/index') ||
  to.matched.some(record => record.path === '/resetPwd') || to.query.type === 'czmm' || to.query.type === 'zhzh') {
    next()
  } else {
    // 如果是从交易跳转过来的做特殊处理
    // let source = to.query.source || ''
    // let type = to.query.type || ''
    // router.push({ name: 'login', query: {type: type, source: source, loginType: loginType} })
    if ($hvue.platform === '0') {
      if (!!ygtUserInfo && !!ygtUserInfo.userId && ((loginType == 1 && ygtUserInfo.fundAccount != '' && $h.getSession('isLogin')) || (loginType == 2 && ygtUserInfo.fundAccountXy != '' && $h.getSession('isLoginXy')))) {
        // 已做H5登录,直接下一步
        next()
        return
      }
    }
    let accountType = loginType == 1 ? '1A' : '1B'
    let softIdentifier = $h.getSession('softIdentifier') || ''// 软件包名
    console.log('softIdentifier----' + softIdentifier)
    if (softIdentifier == '' && !$h.getSession('fromTHSGB')) {
      let param = {
        funcNo: '50001',
        moduleName: 'open' // 必须为open
      }
      let res = $h.callMessageNative(param)
      softIdentifier = res.results[0].softIdentifier
      $h.setSession('softIdentifier', softIdentifier)// 软件包名
    }
    // if (process.env.NODE_ENV == 'production') {
    if (softIdentifier !== 'com.thinkive.dtzquat' && softIdentifier !== 'com.dtzq.thinkivedzjgj' &&
    softIdentifier !== 'com.thinkive.investdtzq') { // 非金管家，走H5登录
      let ygtUserInfo = $h.getSession('ygtUserInfo', {decrypt: false})
      if (!!ygtUserInfo && !!ygtUserInfo.userId && ((loginType == 1 && ygtUserInfo.fundAccount != '' && $h.getSession('isLogin')) || (loginType == 2 && ygtUserInfo.fundAccountXy != '' && $h.getSession('isLoginXy')))) {
        // 已做H5登录,直接下一步
        next()
      } else {
        // 去做H5登录
        router.push({
          name: 'login',
          query: {loginType: loginType}
        })
      }
      return
    }
    // }

    // 金管家检查统一登录状态
    checkLogin(to, next, accountType, router)
  }
})

router.afterEach((route) => {
  // 页面切换更改title
  // 这里可以根据业务需求调整取title的顺序
  // 默认先取业务跳转参数query中title，再取路由元信息中title
  if (route.query.title) {
    setPageTitle(route.query.title)
  } else if (route.meta.title) {
    setPageTitle(route.meta.title)
  }
})

export default router
