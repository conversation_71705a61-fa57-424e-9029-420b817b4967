export default [
  {
    path: '/business',
    name: 'business',
    component: () =>
            import(/* webpackChunkName: "business" */ '@/views/business.vue')
  },

  {
    path: '/agreementDetail',
    name: 'agreementDetail',
    component: () =>
            import(/* webpackChunkName: "agreementDetail" */ '@/views/common/agreementDetail.vue'),
    meta: {
      title: '协议详情'
    }
  },

  {
    path: '/accountDetail',
    name: 'accountDetail',
    component: () =>
            import(/* webpackChunkName: "accountDetail" */ '@/views/common/accountDetail.vue')
  },

  {
    path: '/modifyCostPrice',
    name: 'modifyCostPrice',
    component: () =>
            import(/* webpackChunkName: "modifyCostPrice" */ '@/views/busComp/modifyCostPrice.vue')
  },

  {
    path: '/login',
    name: 'login',
    component: () =>
            import(/* webpackChunkName: "login" */ '@/views/login.vue'),
    meta: {
      title: '登录'
    }
  },

  {
    path: '/businessRecords',
    name: 'businessRecords',
    component: () =>
            import(/* webpackChunkName: "businessRecords" */ '@/views/businessRecords.vue'),
    meta: {
      title: '办理记录'
    }
  },

  {
    path: '/userMoney',
    name: 'userMoney',
    component: () =>
            import(/* webpackChunkName: "userMoney" */ '@/views/business/userMoney'),
    meta: {
      title: '我的资金'
    }
  },

  {
    path: '/accessDetail',
    name: 'accessDetail',
    component: () =>
            import(/* webpackChunkName: "accessDetail" */ '@/views/business/accessDetail'),
    meta: {
      title: '转取转存明细'
    }
  },

  {
    path: '/dzht',
    name: 'dzht',
    component: () =>
            import(/* webpackChunkName: "dzht" */ '@/views/business/dzht'),
    meta: {
      title: '电子合同',
      keepAlive: true
    }
  },

  {
    path: '/error',
    name: 'error',
    component: () =>
            import(/* webpackChunkName: "error" */ '@/views/common/error.vue'),
    meta: {
      title: '提示'
    }
  },

  {
    path: '/showProtocol',
    name: 'showProtocol',
    component: () =>
            import(/* webpackChunkName: "showProtocol" */ '@/components/showProtocol.vue'),
    meta: {
      title: '协议详情'
    }
  },
  {
    path: '/interceptZd',
    name: 'interceptZd',
    component: () =>
            import(/* webpackChunkName: "showProtocol" */ '@/components/interceptZd.vue'),
    meta: {
      title: '中登拦截'

    }
  },
  {
    path: '/qualificationCheck',
    name: 'qualificationCheck',
    component: () =>
            import(/* webpackChunkName: "showProtocol" */ '@/views/busComp/preUploadAssets.vue'),
    meta: {
      title: '准入条件'

    }
  },
  {
    path: '/recycleList',
    name: 'recycleList',
    component: () =>
            import(/* webpackChunkName: "recycleList" */ '@/views/busComp/recycleList.vue'),
    meta: {
      title: '固收平台申请'
    }
  },
  {
    path: '/blockTrade',
    name: 'blockTrade',
    component: () =>
        import(/* webpackChunkName: "blockTrade" */ '@/views/busComp/blockTrade.vue'),
    meta: {
      title: '大宗交易申请'
    }
  },
  {
    path: '/blockTradeInstitution',
    name: 'blockTradeInstitution',
    component: () =>
        import(/* webpackChunkName: "blockTradeInstitution" */ '@/views/busComp/blockTradeInstitution.vue'),
    meta: {
      title: '大宗交易申请机构户'
    }
  },
  {
    path: '/businessResultDG',
    name: 'businessResultDG',
    component: () =>
        import(/* webpackChunkName: "businessResultDG" */ '@/components/businessResultDG.vue'),
    meta: {
      title: '大宗交易固收平台结果页'
    }
  },
  {
    path: '/dzhfwj',
    name: 'dzhfwjList',
    component: () =>
        import(/* webpackChunkName: "dzhfwj" */ '@/views/business/dzhfwj.vue'),
    meta: {
      title: '问卷回访',
      keepAlive: true
    }
  },
  {
    path: '/xhBusinessIntro',
    name: 'xhBusinessIntro',
    component: () =>
      import(/* webpackChunkName: "xh" */ '@/views/business/xhBusinessIntro'),
    meta: {
      title: '销户介绍页',
      keepAlive: true
    }
  },
  {
    path: '/kcbczcIntro',
    name: 'kcbczcIntro',
    component: () =>
      import(/* webpackChunkName: "kcbczc" */ '@/views/business/kcbczcIntro'),
    meta: {
      title: '科创板成长层介绍',
      keepAlive: true
    }
  },
]
