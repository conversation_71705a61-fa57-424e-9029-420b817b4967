<!--
    中登拦截页
-->
<template>
    <div>
        <headComponent :headerTitle="title"></headComponent>
        <article class="content">
            <div class="zd_noticebox">
                <div class="pic"><img src="../assets/images/zd_not_icon.png"></div>
                <p>当前为非业务办理时间，请在业务办理时间<br>09:00-15:55 进行业务办理</p>
            </div>
            <div class="ce_btn mt20">
                <a class="ui button block rounded" @click.stop="back">确定</a>
            </div>
        </article>
    </div>
</template>
<script>
import headComponent from '@/components/headComponent'
export default {
    components: {
        headComponent,
    },
    name: 'interceptZd',
    props: {
        title: {
            type: String,
            default: '资管合格投资者',
        },
    },
    data () {
        return {}
    },
    mounted () {

    },
    methods: {
        back () {
            this.$router.push({ name: 'index' });
        },
    },
}
</script>
<style>
</style>
