<!--
    属性:
    isShow: <PERSON><PERSON><PERSON>, input组件是否可见，使用 v-model 绑定
    title： 标题
    value:  input文字内容
    placeholder:  默认提示内容
    checkType:  input校验类型
    maxLength： 最大长度
    事件:
    @onShow：弹窗显示时触发
    @onHide：弹窗隐藏时触发
    @onSure: 点击确定按钮时触发
-->
<template>
    <div v-if="isShow" class="page_main inputBox">
        <headComponent :headerTitle="title"></headComponent>
        <article class="content">
            <div class="user_main">
                <div class="input_form">
                    <div class="ui field text">
                        <label class="ui label">{{ title }}</label>
                        <input
                            v-model.trim="inputVal"
                            :maxlength="maxLength"
                            type="text"
                            class="ui input"
                            :placeholder="placeholder"
                        />
                        <a class="txt_close" @click.stop="inputVal=''" v-show="inputVal!=''"></a>
                    </div>
                </div>
                <div class="ce_btn mt20">
                    <a class="ui button block rounded" @click.stop="verification">确认修改</a>
                </div>
            </div>
        </article>
    </div>
</template>

<script>
import headComponent from '@/components/headComponent' // 头部
export default {
    name: 'inputBox',
    components: {
        headComponent,
    },
    model: {
        prop: 'isShow',
        event: 'change',
    },
    props: {
        isShow: {
            type: Boolean,
            default: false,
        },
        title: {
            type: String,
            default: '',
        },
        placeholder: {
            type: String,
            default: '',
        },
        value: {
            type: String,
            default: '请填写',
        },
        checkType: {
            type: String,
            default: 'number',
        },
        maxLength: {
            type: Number,
            default: 6,
        },
    },
    data () {
        return {
            inputVal: this.value,
            ignoreType: ['telePhone', 'quota'], // 选填项
        }
    },
    methods: {
        pageBack () {
            this.$emit('change', false)
        },
        // 校验格式
        verification () {
            if (!this.ignoreType.includes(this.checkType) && $h.isEmptyString(this.inputVal)) {
                _hvueToast({mes: '请输入' + this.title})
                return
            }

            switch (this.checkType) {
                case 'postcode':
                    if (!$h.isPostcode(this.inputVal)) {
                        _hvueToast({mes: '请输入6位数值的邮政编码'})
                        return
                    }
                    break;
                case 'number':
                    if (!$h.isNumber(this.inputVal)) {
                        _hvueToast({mes: '请输入整数格式的' + this.title})
                        return
                    }
                    break;
                case 'email':
                    if (!$h.isEmail(this.inputVal)) {
                        _hvueToast({mes: '请输入正确格式的' + this.title})
                        return
                    }
                    break;
                case 'float':
                    if (!/^\d+(\.\d{1,2})?$/.test(this.inputVal)) {
                        _hvueToast({mes: '请输入两位小数以内的' + this.title})
                        return
                    }
                    break;
                case 'income':
                    if (this.inputVal.indexOf('0') === 0) {
                        _hvueToast({mes: '请输入不为0开头的年收入'});
                        return
                    }
                    if (!/^[1-9]\d*(\.\d{1,2})?$/.test(this.inputVal)) {
                        _hvueToast({mes: '请输入两位小数以内的' + this.title})
                        return
                    }
                    if (parseFloat(this.inputVal) <= 500000) {
                        _hvueToast({mes: '年收入要大于50万'})
                        return
                    }
                    break;
                case 'cnEnNum':
                    if (!$h.isCnEnNum_(this.inputVal)) {
                        _hvueToast({mes: '请输入中文英文数字格式的' + this.title})
                        return
                    }
                    break;
                case 'seatNo':
                    if (!/^\d+$/.test(this.inputVal)) {
                        _hvueToast({mes: '请输入纯数字格式的' + this.title})
                        return
                    }
                    break;
                case 'areacode':
                    if (!/^\d{2,4}$/.test(this.inputVal)) {
                        _hvueToast({mes: '请输入2～4位数字的' + this.title})
                        return
                    }
                    break;
                // case 'telePhone':
                //     if (this.inputVal && !$h.isTel(this.inputVal)) {
                //         _hvueToast({mes: '请输入正确格式的' + this.title})
                //         return
                //     }
                //     break;
                case 'quota':
                  if (this.inputVal && !/^[1-9]\d*$/.test(this.inputVal)) {
                    _hvueToast({mes: `请输入整数格式的${this.title}`})
                    return
                  }
                  break;
                case 'branchCode':
                case 'contract':
                  if (!$h.isEnNum(this.inputVal)) {
                    _hvueToast({mes: `请输入正确格式的${this.title}`})
                    return
                  }
                  break;
                case 'trading':
                case 'securityCode':
                case 'contract':
                  if (!$h.isEnNum(this.inputVal)) {
                    _hvueToast({mes: `请输入正确格式的${this.title}`})
                    return
                  }
                  break;
                case 'price':
                  if (this.inputVal.indexOf('0') === 0) {
                    _hvueToast({mes: `请输入不为0开头的${this.title}`});
                    return
                  }
                  if (!/^[1-9]\d*(\.\d{1,2})?$/.test(this.inputVal)) {
                    _hvueToast({mes: `请输入两位小数以内的${this.title}`})
                    return
                  }
                  break;
                case 'amount':
                  if (!/^[1-9]\d+$/.test(this.inputVal)) {
                    _hvueToast({mes: `请输入整数格式的${this.title}`})
                    return
                  }
                  break;
            }
            this.$emit('selCallback', {value: this.inputVal})
            this.pageBack()
        },
    },
    mounted () {
        window.phoneBackBtnCallBack = this.pageBack
        console.log(this.value)
    },
    destroyed () {
    },
}
</script>
<style>
  .inputBox .ui.field.text{
    align-items: center;
  }
  .inputBox .ui.field.text > .ui.label {
    position: static;
  }
  .inputBox .ui.field.text > .ui.input{
    padding-left: 0.1rem;
  }
</style>
