<template>
  <div class="page_main">
    <headComponent headerTitle="协议详情"></headComponent>
    <article class="content">
      <div class="xy_box">
        <div class="title">《{{protocolName}}》</div>
        <div class="xy_cont" v-html="protocolContent"></div>
      </div>
    </article>
    <div class="bottom_check">
      <p class="tips" v-show="isShowTips">{{tips}}</p>
      <div class="ce_btn">
        <a
          v-if="showReadSeconds"
          class="ui button block rounded disable"
        >请认真阅读以上内容({{showTime || readSeconds}}s)</a>
        <a v-else class="ui button block rounded" @click.stop="confirmClick(true)">{{btnDesc}}</a>
      </div>
    </div>

    <div class="ui dialog-overlay" v-show="showCopyFlag"></div>
    <div class="popup_layer spel show" v-show="showCopyFlag">
      <div class="popup_lytit">
        <a class="cancel" @click.stop="showCopyFlag = false"></a>
        <h3>签署本协议需填写以下风险声明</h3>
      </div>
      <div class="popup_lycont">
        <div class="read_combox">
          <div class="read_txt">
            <span v-for="item in copyContentArr">{{item}}</span>
          </div>
          <div class="read_cm_title">
            <h5>填写声明</h5>
            <a class="btn" href="javascript:void(0);" @click.stop="copyAndFill">复制并填写声明</a>
          </div>
          <div class="read_cm_tarea">
            <textarea placeholder="请输入抄写内容" v-model="copy_content"></textarea>
          </div>
        </div>
      </div>
      <div class="ce_btn">
        <a class="ui button block rounded" :class="{'disabled': copy_content !== copyContent}" @click.stop="confirmClick(false)">确认提交</a>
      </div>

    </div>
  </div>
</template>

<script>
import headComponent from '@/components/headComponent' // 头部
import { getProtocolById, queryCommission } from '@/service/comServiceNew'
export default {
  components: { headComponent },
  props: {
    isShow: {
      type: Boolean,
      default: false
    },
    // 协议id
    agreementId: {
      type: String,
      default: ''
    },
    // 是否已经阅读
    isRead: {
      type: Boolean,
      default: false
    },
    // 协议最低阅读秒数
    readSeconds: {
      type: Number,
      default: 0
    },
    // 底部提示
    tips: {
      type: String,
      default: ''
    },
    // 是否展示底部提示
    isShowTips: {
      type: Boolean,
      default: false
    },
    // 协议名
    name: {
      type: String,
      default: ''
    },
    // 协议内容
    content: {
      type: String,
      default: ''
    },
    filePath: {
      type: String,
      default: ''
    },
    // 按钮描述
    btnDesc: {
      type: String,
      default: '我已阅读并同意'
    }
  },
  model: {
    prop: 'isShow',
    event: 'change'
  },
  data () {
    return {
      currentPage: 1,
      startX: 0, // 鼠标开始点击的x坐标
      startY: 0,

      protocolName: this.name, // 协议标题
      protocolContent: this.content, // 协议内容
      isShowContent: true, // 展示协议内容唯true 展示附件为false
      interval: null,
      showCopyFlag: false, // 是否展示一键抄写弹框
      needCopy: '', // 是否需要一键抄写
      copyContent: '', // 一键抄写的内容
      copyContentArr: [], // 抄写的内容数组
      copy_content: '', // 复制填充的内容
      showTime: '', // 阅读协议的时间
      yjBcode: ['zkgdh', 'jggdh'], // 需要展示佣金率的业务
      keyWords: {}
    }
  },
  computed: {
    showReadSeconds () {
      // 阅读时间接口倒计时为0
      if (this.showTime === 0) {
        return false
      } else if (this.showTime == 0 && this.readSeconds == 0) {// 阅读时间接口返回为空
          return false
      } else {
        return true
      }
    }
  },
  created () {
    if (this.agreementId) {
      this.queryYjl(this.getProtocol)
    }
    if (this.filePath == '') {
      this.isShowContent = true
    } else {
      // 如果需要用纯H5展示pdf，直接用这里的方法，但是不可以跨域，建议在app中调用原生查看pdf的50240方法
      this.isShowContent = false
      this.currentPage = 1
    }
  },
  mounted () {
    window.phoneBackBtnCallBack = this.pageBack
  },
  methods: {
    // 给目标添加事件，处理兼容
    addHandler (element, type, handler) {
      if (element.addEventListener) {
        element.addEventListener(type, handler, false)
      } else if (element.attachEvent) {
        element.attachEvent('on' + type, handler)
      } else {
        element['on' + type] = handler
      }
    },
    // 具体的滑动处理
    // （此处只需要处理上滑事件，所以处理较简单，还可以进行封装，处理各种滑动事件）
    handleTouchEvent (event) {
      switch (event.type) {
        case 'touchstart':
          this.startX = event.touches[0].pageX
          this.startY = event.touches[0].pageY
          break
        case 'touchend':
          var spanX = event.changedTouches[0].pageX - this.startX
          var spanY = event.changedTouches[0].pageY - this.startY
          // console.log('spanY', spanY)
          if (spanY < -30) { // 向上
            this.currentPage++
          }
          if (spanY > 30 && this.currentPage > 1) { // 向下
            this.currentPage--
          }
          if (Math.abs(spanX) > Math.abs(spanY)) {
            // 认定为水平方向滑动
          } else {
            // 认定为垂直方向滑动
          }
          break
        case 'touchmove':
          // 阻止默认行为
          event.preventDefault()
          break
      }
    },
    // 返回
    pageBack () {
      this.$emit('change', false)
    },
    confirmClick (flag) {
      // 一件抄录
      if (this.needCopy === '1' && flag) {
        this.showCopyFlag = true
      } else {
        if (this.copy_content === this.copyContent) {
          this.$emit('showProtocolCall', {
            needCopy: this.needCopy,
            copyContent: this.copyContent,
            keyWords: this.keyWords
          })
          this.pageBack()
        }
      }
    },
    // 复制并填充
    copyAndFill () {
      this.copy_content = this.copyContent
    },
    queryYjl (callback) {
      if (!this.yjBcode.includes(this.$route.query.type)) {
        callback && callback()
        return;
      }
      queryCommission({
        userId: $h.getSession('ygtUserInfo', {decrypt: false}).userId,
        businessCode: this.$route.query.type,
        stkbds: ['00', '10']
      }).then(res => {
        if (res.error_no === '0') {
          let originalArray = res.DataSet
          for(let item of originalArray){
            switch (item.market){
              case '00':
                this.keyWords.commission_sz = item.brokerage
                break;
              case '10':
                this.keyWords.commission_sh = item.brokerage
                break;
              default:
                break;
            }
          }
          callback && callback()
        } else {
          _hvueAlert({
            title: '提示',
            mes: res.error_info
          })
        }
      })
    },
    getProtocol () {
      let me = this;
      let param = {
        agreementId: this.agreementId,
        userId: $h.getSession('ygtUserInfo', {decrypt: false}).userId,
        keyWords: JSON.stringify(this.keyWords)
      }
      getProtocolById(param)
      .then(data => {
        if (data.error_no === '0') {
          let results = data.results || data.DataSet
          this.protocolName = results[0].agreeBrief
          this.protocolContent = results[0].agreeContent
          this.needCopy = results[0].needCopy
          this.copyContent = results[0].copyContent
          this.showTime = results[0].showTime
          let len = this.copyContent.length

          // 需要填充的内容
          if (this.needCopy === '1' && this.copyContent) {
            for (let i = 0; i < len; i++) {
              this.copyContentArr.push(this.copyContent.substr(i, 1))
            }
            this.protocolContent = this.protocolContent.replace(/\{declareContent\}/g, this.copyContent)
          }

          // 强制阅读
          if (this.showTime > 0) {
            me.interval = window.setInterval(() => {
              this.showTime--
              if (this.showTime === 0) {
                window.clearInterval(me.interval)
              }
            }, 1000)
          } else {
            if (me.readSeconds > 0) {
              me.interval = window.setInterval(() => {
                me.readSeconds--
                if (me.readSeconds === 0) {
                  window.clearInterval(me.interval)
                }
              }, 1000)
            }
          }
          // 强制阅读

        } else {
          _hvueToast({ mes: data.error_info })
        }
      })
      .catch(e => {
        _hvueToast({ mes: e.message })
      })
    }
  }
}
</script>
<style scoped>
.page_main{
  background-color: #fff;
}
</style>
