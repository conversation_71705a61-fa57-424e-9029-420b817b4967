<template>
  <div
    :class="cssClass"
    contenteditable="true"
    :placeholder="placeholder"
    v-html="innerText"
    @input="changeText"
    @focus="isChange = false"
    @blur="blurFunc"></div>
</template>

<script>
export default {
  name: 'DivEditable',
  props: {
    value: {
      type: String,
      default: ''
    },
    placeholder: {
      type: String,
      default: ''
    },
    cssClass: {
      type: String,
      default: 'teare02'
    }
  },
  data () {
    return {
      innerText: this.value,
      isChange: true
    }
  },
  watch: {
    value () {
      if (this.isChange) {
        this.value = this.value
        this.innerText = this.value
      }
    }
  },
  methods: {
    changeText () {
      let pastedData = this.$el.innerHTML;
      // 去除所有标签，保留纯文本
      const cleanedData = pastedData.replace(/<[^>]+>/g, '');
      this.$emit('input', cleanedData);
    },
    blurFunc () {
      this.isChange = true
      this.$emit('blurFunc')
    }
  }
}
</script>
