<template>
  <div v-show="showSelImgBox">
    <!-- 遮罩层 -->
    <div class="dialog_overlay" @click="close" style="display: block;"></div>
    <!-- 弹出层 -->
    <div class="upload_select show">
      <h5>请选择上传方式</h5>
      <ul>
        <li @click="selImg(1)">
          <a>拍照扫描</a>
        </li>
        <li @click="selImg(2)">
          <a>从相册上传</a>
        </li>
      </ul>
      <a class="cancel" @click="close">取消</a>
    </div>
  </div>
</template>

<script>
import '@/nativeShell/nativeCallH5';

export default {
  name: 'getImg_app',
  data() {
    return {
      showSelImgBox: false,
      imgType: '',
      userInfo: $h.getSession('userInfo')
    };
  },
  props: {
    isUpload: {
      type: String,
      default: '1'
    }
  },

  methods: {
    getImg(imgType) {
      this.imgType = imgType;
      this.showSelImgBox = true;
    },
    close() {
      this.showSelImgBox = false;
    },
    selImg(selType) {
      window.imgCallBack = this.getImgCallBack;
      let ocr = '0';
      if (selType !== 1) ocr = '1';
      var phoneConfig = {
        requestParam:
          'funcNo=501525&version=1&is_ocr=' +
          ocr +
          '&user_id=' +
          this.userInfo.user_id +
          '&file_name=' +
          this.userInfo.user_id +
          '.jpg&file_type=image&op_source=' +
          $hvue.platform +
          '&flow_type=' +
          $h.getSession('flow_type'),
        imgType: this.imgType === 'idfrontimg' ? '4' : '5', // 需上传图片类型 3大头照 4 身份证正面 5 反面
        url:
          `${$hvue.customConfig.serverUrl}/servlet/json` +
          (window.__fcjs_environment ? `;jsessionid=${this.userInfo.jsessionid}` : ''),
        funcNo: selType === 1 ? 60014 : 60013,
        action: selType !== 1 ? 'phone' : 'pai', // 60013  phone相册 pai拍照
        userId: this.userInfo.user_id,
        isAlbum: '1', //是否显示相册 1表示显示相册，0或其他表示不显示 默认隐藏
        isTake: '1', //是否显示拍照按钮
        compressSize: 200, //原生压缩大小 不传默认200k
        isUpload: this.isUpload,
        moduleName: 'open' // 必须为open
      };
      console.log(phoneConfig);
      let result = $h.callMessageNative(phoneConfig);
      this.close();
      if (result.error_no !== '0') {
        console.log({ mes: result.error_info });
      }
    },
    getImgCallBack(data) {
      console.log(data);
      let result = {
        idno: data.idNo,
        custname: data.custName,
        native: data.native,
        ethnicname: data.ethnicName,
        idbegindate: data.idbeginDate,
        idenddate: data.idendDate,
        policeorg: data.policeOrg,
        filepath: data.backFilePath || data.frontFilePath,
        secret: data.backSecret || data.frontSecret
      };
      this.$emit('getImgCallBack', {
        base64: this.filterBase64Pre(data.frontBase64 || data.backBase64),
        ocrInfo: result
      });
    },
    filterBase64Pre(ndata) {
      let arr = ndata.split('base64,');
      return arr[arr.length - 1];
    }
  },
  activated() {
    this.userInfo = $h.getSession('userInfo');
  },
  created() {
    this.userInfo = $h.getSession('userInfo');
  }
};
</script>
