<template>
  <div class="page_main" v-if="isShow">
    <headComponent :headerTitle="title"></headComponent>
    <article class="content">
      <slot>
        <ul class="select_list">
          <li
            v-for="(d, index) in dataList"
            @click.stop="selClick(d, index)"
            :class="{ active: selMap[index] }"
            :key="index"
          >
            <span>{{ d.value }}</span>
          </li>
        </ul>
      </slot>
    </article>
  </div>
</template>

<script>
import headComponent from '@/components/headComponent' // 头部
import { queryDictionary } from '@/common/util'
export default {
  name: 'selBox',
  components: {
    headComponent
  },
  model: {
    prop: 'isShow',
    event: 'change'
  },
  props: {
    isShow: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '请选择'
    },
    category: {
      type: String
    },
    initData: {
      type: Array,
      default: () => []
    },
    defaultStr: {
      type: String,
      default: ''
    },
    mult: {
      // 是否多选
      type: Boolean,
      default: false
    },
    idString: {
      // 指定id的key
      type: String,
      default: 'key'
    }
  },
  data () {
    return {
      dataList: [],
      selMap: {}
    }
  },
  mounted () {
    window.phoneBackBtnCallBack = this.pageBack
  },
  methods: {
    pageBack () {
      this.$emit('change', false)
    },
    selClick (item, index) {
      if (this.mult) {
        this.$set(this.selMap, index, !this.selMap[index])
      } else {
        if (this.category === 'ismp.identitycategory' && item.key !== '1') {
            _hvueAlert({
                title: '温馨提示',
                mes: '根据监管要求，线上仅支持特定自然人标识为普通自然人修改资料，若需支持修改为其他特定自然人，需临柜处理。',
            })
            return;
        }
        this.$emit('selCallback', { data: item, index: index })
        this.pageBack()
      }
    },
    confirmClick () {
      let sel = []
      for (let index in this.selMap) {
        sel.push(this.dataList[index])
      }
      if (sel.length === 0) {
        return
      }
      this.$emit('selCallback', sel)
      this.pageBack()
    },
    initDefault () {
      let that = this
      that.dataList.forEach((a, b) => {
        that.defaultStr.split(';').forEach(c => {
          if (c === a[that.idString]) {
            that.selMap[b] = true
          }
        })
      })
    }
  },
  created () {
    if (this.category) {
      queryDictionary({ type: this.category }, data => {
        this.dataList = this.initData.concat(data)
        this.initDefault()
      })
    } else {
      this.dataList = this.initData
      this.initDefault()
    }
  },
  destroyed () {}
}
</script>
