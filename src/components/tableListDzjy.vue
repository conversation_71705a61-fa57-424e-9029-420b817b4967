<!--

-->
<template>
  <div v-if="isShow">
    <ul class="dz_cm_list">
      <li :key="key" v-for="(item, key) in tableList"
          @click.prevent.stop="_onChange(item, key)"
      >
        <div class="item name">
          <p>{{item.dzjyList ? JSON.parse(item.dzjyList).securityName : ''}}</p>
          <em>{{item.dzjyList ? JSON.parse(item.dzjyList).securityCode : ''}}</em>
        </div>
        <div class="item date">
          <p>申请日期</p>
          <em>{{item.updateDate | formatDateYMD}}</em>
        </div>
        <div class="item state">
          <p :class="[classArr[activeKey].classN]">{{classArr[activeKey].info}}</p>
        </div>
      </li>
    </ul>
    <div class="text-align-center" v-if="!tableList.length">暂无数据</div>

  </div>
</template>
<script>
export default {
  name: 'tableListDzjy',
  model: {
    prop: 'isShow',
    event: 'change'
  },
  props: {
    isShow: {
      type: Boolean,
      default: false,
    },
    activeKey: {
      type: Number,
      default: 0,
    },
    tableList: {
      type: Array,
      default: () => [],
    }
  },
  data () {
    return {
      classArr: [
          {classN: 'ing', info: '申请中'},
          {classN: 'ok', info: '已完成'},
          {classN: 'error', info: '处理失败'},
      ]
    }
  },
  mounted () {
  },
  methods: {
    _onChange () {// 展示详情
      this.$emit('showDetail', ...arguments)
    },
  }
}
</script>
<style>
</style>
<
