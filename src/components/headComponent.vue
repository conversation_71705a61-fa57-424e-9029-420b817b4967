<!--
    属性:
    headerTitle: 标题
    isWhite:  是否为白色背景
    事件:
    @pageBack：点击返回按钮时触发
    插槽:
    slot: 默认插槽 头部主体内容, 使用slot时，slot内容将替换头部内容
-->
<template>
  <header class="page_header header" v-show="$route.path !=='/login'">
    <slot>
      <div class="header_inner" v-bind:class="{spel : backWhite}">
        <a class="icon_back" @click.stop="pageBack"></a>
        <h1 class="title text-center">{{ headerTitle }}</h1>
      </div>
    </slot>
  </header>
</template>
<script>
import {closeYgt} from '@/common/sso'

export default {
  props: ['headerTitle', 'isWhite'],
  data () {
    return {
      fromTHSGB: $h.getSession('fromTHSGB'), // 渠道同花顺公版
      thscode: $h.getSession('thscode'), // 渠道同花顺code
    }
  },
  methods: {
    pageBack () {
      // 基金开户打开协议时，取消固定按钮在底部
      if (this.$route.query.type === 'kfsjjkh') {
        this.$store.state.agreementIsFixed = true;
      }

      if (this.$route.path == '/index' || this.fromTHSGB && this.thscode) {
        closeYgt(0, '1A', 0, 1)
      } else {
        if (this.$parent.pageBack) {
          this.$parent.pageBack()
        } else {
          this.$parent.back()
        }
      }
    },
  },
  computed: {
    backWhite () {
      if (this.isWhite == 'true') {
        return true
      }
      return false
    },
  },
}
</script>
<style scoped>
.hidden-thsgb {
  display: none;
}
</style>
