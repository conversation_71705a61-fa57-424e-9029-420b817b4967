<template>
  <div>
    <input ref="input" @change="seledFile" v-if="createImgSelect" type="file" accept="image/*"/>
  </div>
</template>

<script>
import Exif from 'exif-js';
export default {
  name: 'getImg_browser',
  data() {
    return {
      createImgSelect: true,
      isUserCapture: true
    };
  },

  props: {
    maxSize: {
      type: Number,
      default: 10
    },
    userCapture: {
      type: Boolean,
      default: false
    },
    forceFront: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    getImg() {
      // 选择文件点击事件
      this.createImgSelect = false;
      this.$nextTick(() => {
        this.createImgSelect = true;
        this.$nextTick(() => {
          this.isUserCapture = $hvue.iBrowser.android
            ? navigator.userAgent.indexOf('QQ/') > -1
            : false;
          if (this.userCapture) {
            // 使用摄像头
            this.isUserCapture = !this.isUserCapture;
          }
          if (this.isUserCapture) {
            this.$refs.input.setAttribute('capture', 'camera');
          }
          if (this.forceFront) {
            this.$refs.input.setAttribute('capture', 'user');
          }
          this.$refs.input.click();
        });
      });
    },
    seledFile(o) {
      // 选择完文件回调
      let that = this;
      let file = o.currentTarget.files[0];
      if (this.check(file)) {
        let Orientation;
        // 去获取拍照时的信息，解决拍出来的照片旋转问题
        Exif.getData(file, function() {
          Orientation = Exif.getTag(this, 'Orientation');
        });
        // 创建一个reader
        let reader = new FileReader();
        // 将图片2将转成 base64 格式
        reader.readAsDataURL(file);
        // 读取成功后的回调
        reader.onloadend = function() {
          let result = this.result;
          let img = new Image();
          img.src = result;
          img.onload = function() {
            that.$emit('getImgCallBack', {
              base64: that.compress(img, Orientation)
            });
          };
        };
      }
    },
    check(file) {
      if (!/image/.test(file.type)) {
        this.$emit('onerror', { code: -1, msg: '文件格式不正确' });
        return false;
      }
      return true;
    },
    rotateImg(img, degree, canvas) {
      // degree 仅仅支持90 180 270 三个参数表示顺时针旋转度数
      if (degree !== 90 && degree !== 180 && degree !== 270) return;
      if (img == null) return;
      // img的高度和宽度不能在img元素隐藏后获取，否则会出错
      let height = canvas.height;
      let width = canvas.width;
      let ctx = canvas.getContext('2d');
      switch (degree) {
        case 90:
          canvas.width = height;
          canvas.height = width;
          ctx.rotate((degree * Math.PI) / 180);
          ctx.drawImage(img, 0, -height, width, height);
          break;
        case 180:
          canvas.width = width;
          canvas.height = height;
          ctx.rotate((degree * Math.PI) / 180);
          ctx.drawImage(img, -width, -height, width, height);
          break;
        case 270:
          canvas.width = height;
          canvas.height = width;
          ctx.rotate((degree * Math.PI) / 180);
          ctx.drawImage(img, -width, 0, width, height);
          break;
      }
    },
    compress(img, Orientation) {
      let canvas = document.createElement('canvas');
      let ctx = canvas.getContext('2d');
      let initSize = img.src.length;
      let width = img.width;
      let height = img.height;
      // 如果图片大于490000像素，计算压缩比并将大小压至490000以下
      let ratio;
      if ((ratio = (width * height) / 890000) > 1) {
        console.log('大于490000像素');
        ratio = Math.sqrt(ratio);
        width /= ratio;
        height /= ratio;
      } else {
        ratio = 1;
      }
      canvas.width = width;
      canvas.height = height;
      //        铺底色
      ctx.fillStyle = '#fff';
      ctx.fillRect(0, 0, canvas.width, canvas.height);
      ctx.drawImage(img, 0, 0, width, height);
      console.log('Orientation:' + Orientation);
      // 修复ios上传图片的时候 被旋转的问题
      if (Orientation !== '' && Orientation !== 1) {
        switch (Orientation) {
          case 6: // 需要顺时针90度旋转
            // this.rotateImg(img, 180, canvas);
            break;
          case 8: // 需要270度旋转
            this.rotateImg(img, 270, canvas);
            break;
          case 3: // 需要180度旋转
            this.rotateImg(img, 180, canvas);
            break;
        }
      }
      // 进行最小压缩
      let ndata = canvas.toDataURL('image/jpeg', 1);
      console.log('压缩前：' + initSize);
      console.log('压缩后：' + ndata.length);
      console.log('压缩率：' + ~~((100 * (initSize - ndata.length)) / initSize) + '%');
      if (initSize - ndata.length < 0) {
        return this.filterBase64Pre(img.src);
      }
      canvas.width = canvas.height = 0;
      ndata = this.filterBase64Pre(ndata);
      return ndata;
    },
    filterBase64Pre(ndata) {
      let arr = ndata.split('base64,');
      return arr[arr.length - 1];
    }
  }
};
</script>
<style scoped>
input {
  width: 0px;
  height: 0px;
  position: absolute;
  top: 0;
  left: 0;
  display: none;
}
</style>
