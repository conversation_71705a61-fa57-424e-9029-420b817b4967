<template>
  <div v-show="showSelImgBox">
    <div class="ui dialog-overlay"></div>
    <div class="upload_btn">
      <ul>
        <li>
          <a @click.stop="selImg('pai')">拍摄</a>
        </li>
        <li v-show="showAlbum" style="display:none;">
          <a @click.stop="selImg('phone')">从相册选择上传</a>
        </li>
      </ul>
      <div class="cancel_btn">
        <a @click.stop="close">取消</a>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'getImg_ths',
  data () {
    return {
      showSelImgBox: false,
      imgType: '',
    };
  },
  // model: {
  //   prop: 'showSelImgBox',
  //   event: 'change'
  // },
  props: {
    showAlbum: {// 是否展示相册
      type: Boolean,
      default: true
    },
  },
  methods: {
    getImg (imgType) {
      switch(imgType){
        case '3':
          this.imgType = '3';
          break;
        case '4':
          this.imgType = '1';
          break;
        case '5':
          this.imgType = '2';
          break;
        default:
          this.imgType = '4';
          break;
      }
      // 大头照不需要选择相册，直接拍摄
      if (this.imgType === '3') {
        this.selImg('pai');
      } else {
        this.showSelImgBox = true;
      }
    },
    selImg (selType) {
      this.close();
      var data = {
        action: 'CertificateImageChoose', // 拍照上传
        param: {
          imageType: parseInt(this.imgType),
          chooseImageType: selType === 'pai' ? 1 : 2,
          tips: this.imgType === '3' ? '' : this.imgType === '4' ? '请上传图片' : '请上传证件'
        }
      };
      callNativeHandler('JSWangTingEvent', data, (results) => {
        setTimeout(() => {
          if (results.param.errorMsg) return;
          this.getImgCallBack(results.param);
        }, 500)
      });
    },
    close () {
      this.showSelImgBox = false
    },
    getImgCallBack (data) {
      let param = {
        base64: data.imgData
      }
      this.imgType === '4' && (param.frontBase64 = data.imgData);
      this.imgType === '5' && (param.backBase64 = data.imgData);
      this.$emit('getImgCallBack', param);
    },
    compatibilityThs () { // 通过引起重绘兼容同花顺 拍摄身份证 返回时不能滑动问题
      if (document.visibilityState === 'visible') {
        document.body.style.border = '1px';
        setTimeout(function () {
          document.body.style.border = '0px';
        }, 200);
      }
    }
  },
  activated () {
    document.addEventListener('visibilitychange', this.compatibilityThs);
  },
  deactivated () {
    document.removeEventListener('visibilitychange', this.compatibilityThs);
  }
};
</script>
