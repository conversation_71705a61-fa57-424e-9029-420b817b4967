<!--
    属性:
    isShow 显示隐藏组件
    事件:

-->
<template>
    <div>
      <div class="ztginfo_content" v-for="item in dataList">
        <div>
          <div class="tit">转托管账户</div>
          <div>{{item.szAccount}}</div>
        </div>
        <div>
          <div class="tit">转入券商</div>
          <div>{{item.zqName}}</div>
        </div>
        <div>
          <div class="tit">券商席位号</div>
          <div>{{item.seatNo}}</div>
        </div>
        <div>
          <div class="tit">证券名称</div>
          <div class="tit">转入数量</div>
        </div>
        <div>
          <div>{{item.stockName}}</div>
          <div>{{item.stockCount}}</div>
        </div>
      </div>
    </div>
</template>
<script>
export default {
  name: 'ztgInfoConfirm',
  model: {
    prop: 'isShow',
    event: 'change',
  },
  props: {
    isShow: {
      type: Boolean,
      default: false,
    },
    dataList: {
      type: Array,
      default () {
        return []
      },
    },
  },
  watch: {

  },
  mounted () {

  },
  methods: {

  },
}
</script>
<style scoped>
    .ztginfo_content > div{
        width: 90%;
        display: flex;
        margin:auto;
        text-align: center;
        border: 1px solid #cccccc;
    }
    .ztginfo_content > div > div{
        flex: 6px;
    }
    .ztginfo_content > div > div.tit{
        background: #e4e4e4;
    }
    .ztginfo_content > div > div:first-child{
        border-right: 1px solid #cccccc;
    }

    .ztginfo_content > div:nth-child(even){
        border-top:none;
        borer-bottom:none;
        border-color:#cccccc;
    }
    .ztginfo_content > div:nth-child(odd){
        border-top:none;
        borer-bottom:none;
        border-color:#cccccc;
    }
    .ztginfo_content > div:first-child{
        border-top:1px solid #cccccc;
        /*background:Red;*/
    }
</style>
