<template>
  <div v-cloak class="question_page">
    <headComponent headerTitle="问卷回访"></headComponent>
    <div v-show="showQuestion">
      <div class="test_main">
        <div
            class="test_box"
            v-for="(item,index) in questionList"
            :key="index"
            v-show="index===questionIndex"
        >
          <h5
              :class="{redCls:item.error}"
          >{{item.questionContent}}{{item.questionKind==1?'(多选)': item.questionKind==2 ? "（输入）" : ''}}</h5>
          <ul v-if="item.questionKind != 2" class="input_list multiple">
            <li
                v-for="(ansItem,index2) in item.answerArray"
                :key="index2"
                v-show="ansItem.relExtCode =='' || ansItem.relExtCode==undefined"
                :class="{relItem : ansItem.relExtCode !='' && ansItem.relExtCode!=undefined}"
            >
              <a
                  :class="{icon_radio:(item.questionKind!=1&&(ansItem.relExtCode==''|| ansItem.relExtCode==undefined)),icon_check:(ansItem.relExtCode!=''&& ansItem.relExtCode!=undefined) || item.questionKind==1,checked: ansItem.checked}"
                  @click.stop="riskSelEvent(item, ansItem,index,index2, true,$event)"
              >{{ansItem.answerContent}}</a>
            </li>
          </ul>
          <div class="ui field text" v-else>
            <multLineInput
                class="teare01 needsclick"
                v-model.trim="inputObj[index+'_'+questionNow.questionNo]"
                :maxlength="300"
            ></multLineInput>
          </div>
        </div>
      </div>
      <div class="ce_btn mt20">
        <a
            v-throttle
            class="ui button block rounded"
            v-if="questionIndex+1 === questionList.length && (questionNow.isChooseed || questionNow.questionKind == 2)"
            @click.stop="submitAns"
        >提交</a>
      </div>
      <div class="test_infobox">
        <div class="item">
          <b :style="{width: percent +'%'}"></b>
        </div>
        <p>
          <span>{{questionIndex+1}}</span>/
          <span>{{questionList.length}}</span>
        </p>
        <a class="prev" v-show="questionIndex>0" @click.stop="prevQuestion">上一题</a>
        <a
            class="next"
            v-show="((questionNow.questionKind === '1' || relAnswerNo[questionIndex] != '' || questionNow.defaultChecked) && questionNow.isChooseed) || questionNow.questionKind === '2'"
            @click.stop="nextQuestion"
        >下一题</a>
      </div>
    </div>
  </div>
</template>

<script>
import headComponent from '@/components/headComponent' // 头部
import { submitXsbKowledge, submitKowledge } from "@/service/comServiceNew";
import multLineInput from '@/components/multLineInput'

export default {
  props: ["pageParam"],
  name: "questionList",
  components: {
    headComponent,
    multLineInput
  },
  inject: ["reload"],
  data() {
    return {
      // serivalId: this.$parent.publicParam.serivalId,
      percent: 0, // 已完成题目的百分比
      questionIndex: 0, // 当前题目下标
      questionList: [], // 问题数组
      answerList: [], // 答案数组
      questionNow: {}, // 当前测评题目
      relAnswerNo: [], // 保存关联选项的answerNo
      handleResult: true,
      quesitionResult: [], // 测评结果
      showQuestion: true, // 测评题目
      showResult: false, // 控制展示测评结果组件
      currentStep: "", // 当前测评的步骤名称
      age: "", // 客户年龄
      education: $h.getSession("ygtUserInfo", { decrypt: false }).education, // 教育程度
      inputObj:{} // 文本输入的内容
    };
  },
  watch: {
    // 监听题号改变时重新计算进度条，重新设置当前题目
    questionIndex(val) {
      this.percent =
          ((this.questionIndex + 1) / this.questionList.length) * 100;
      this.questionNow = this.questionList[this.questionIndex];
      // 设置文本题默认答案
      if (this.questionNow.questionKind === '2') {
        this.$set(this.answerList, this.questionIndex, this.questionNow.questionNo + '_' + this.questionNow.questionNo + '_code')
      }
    }
  },
  created() {
    this.$store.commit("updateIsScroll", true);
    this.initPage();
  },
  destroyed () {
    this.$store.commit("updateIsScroll", false);
  },
  methods: {
    /** ********************************************子组件公共方法定义*****start******************************* */
    pageBack() {
      this.$emit('back')
    },
    /**
     * 请求参数打包
     */
    reqParamPackage() {
      // 业务请求参数封装
      let params = {
        paperAnswer: this.answerList.join("|"),
        paperType: this.questionNow.paperTypes
      };
      return params;
    },
    putFormData() {
      let formData = {
        questionSubmit: {
          paperAnswer: this.answerList.join("|"),
          paperType: this.questionNow.paperTypes,
          agreeId: "",
          // serivalId: this.serivalId,
          paperScore: this.quesitionResult.paperScore
        }
      };
      return formData;
    },
    /** ********************************************子组件公共方法定义***end************************************* */
    initPage() {
      this.education = $h.getSession("ygtUserInfo", {
        decrypt: false
      }).education; // 教育程度
      // 隐藏下一步按钮显示
      this.$store.commit("updateBusinessNextBtnStatus", false);
      this.questionIndex = 0;
      this.percent = 0;
      this.questionNow = {};
      this.answerList = [];
      this.relAnswerNo = [];
      this.questionList = this.pageParam;
      this.showResult = false;
      this.showQuestion = true;

      this.handleResult = false; //  投资者教育和风险测评不显示结果页

      this.setQuestionList(this.questionList);
    },
    // 提交后对结果进行处理的方法
    handleResultFunc(res) {
      this.quesitionResult = res.custTypeCheck
          ? res.custTypeCheck[0]
          : res.questionResult[0];
      this.quesitionResult.paperScore = this.quesitionResult.score
          ? this.quesitionResult.score
          : this.quesitionResult.paperScore;
      this.showResultFunc();
    },
    showResultFunc() {
      // 隐藏下一步按钮显示
      this.$store.commit("updateBusinessNextBtnStatus", false);
      this.$store.commit("updateIsShowHead", false); // 隐藏头部
      this.showResult = true;
      this.showQuestion = false;
      this.handleResult = false;
    },
    // 组装问题列表
    setQuestionList(results) {
      this.questionList = results;
      results.forEach((item, index) => {
        // 重新组装问题列表
        // 注：往数组中添加数据项要用vm.$set()来触发响应式
        // 增加checked属性来判定是否选中当前选项

        // 默认选中16、18题(年龄和学历），并设置一个默认选中的标识 defulatChecked 用于控制下一题按钮的显示
        if (item.questionContent.indexOf('年龄') > -1  && !!this.age && item.paperTypes === '1') {
          item.isChooseed = true;
          item.defaultChecked = true;
        } else if (item.questionContent.indexOf('学历') > -1  && !!this.education && item.paperTypes === '1') {
          item.isChooseed = true;
          item.defaultChecked = true;
        } else {
          item.isChooseed = false;
          item.defaultChecked = false;
        }

        item.error = false;
        item.answerArray = JSON.parse(item.answerArray);
        item.answerArray.forEach((item2, index2) => {
          // 16、18题年龄学历默认选中
          if (
              (item.questionContent.indexOf('年龄') > -1 &&
                  !!this.age &&
                  index2 === this.filterAge(this.age)) &&
              item.paperTypes === '1' ||
              (item.questionContent.indexOf('学历') > -1 && index2 === this.filterEdu(this.education) && item.paperTypes === '1')
          ) {
            item2.checked = true;
            let ansStr = item2.optionOrder + "_";
            ansStr += item2.answerNo + "_" + item2.questionScore;
            this.$set(this.answerList, index, ansStr);
          } else {
            item2.checked = false;
          }
          // 保存每一题的关联选项
          this.$set(this.relAnswerNo, index, item2.relExtCode || "");
        });
        this.$set(this.questionList, index, item);
      });
      this.questionIndex = 0;
    },
    // 勾选选项
    riskSelEvent(quesItem, ansItem, quesIdex, ansIndex, flag, currentEvent) {
      // 当前题目默认填充则不允许修改答案
      if (quesItem.defaultChecked) return;

      // 遍历改变当前问题的选中项
      let ansAtr = [];
      let ansStr = quesItem.questionNo + "_"; // 多选题答案串特殊处理
      let chooseed = false;
      let currentElement = event.currentTarget; // 当前点击的选项元素
      this.questionList[this.questionIndex].answerArray.forEach((que, i) => {
        if (quesItem.questionKind === "1") {
          // 多选处理
          if (que.answerNo === ansItem.answerNo) {
            que.checked = !que.checked;
            // 用vm.$set()来触发响应式
            this.$set(this.questionList[quesIdex].answerArray, ansIndex, que);
          }
          if (que.checked) {
            ansStr += que.answerNo + "_" + que.questionScore + "&";
            chooseed = true;
          }
        } else {
          // 单选处理
          if (que.answerNo === ansItem.answerNo) {
            if (currentElement.parentNode.classList.contains("relItem")) {
              ansItem.checked = !ansItem.checked;
            } else {
              ansItem.checked = true;
            }
            // 用vm.$set()来触发响应式
            this.$set(this.questionList[quesIdex].answerArray, ansIndex, que);
            quesItem.error = false;

            console.log(this.questionList)

            ansStr += que.answerNo + "_" + que.questionScore + "&";
            // 如果该选项下面有关联题，不跳转下一步，展示关联题选项 需要展示关联题选项
            if (
                this.relAnswerNo[this.questionIndex] != "" &&
                ansItem.answerNo == this.relAnswerNo[this.questionIndex]
            ) {
              // 展示关联答案
              this.showRelAnswerItem(currentElement.parentNode); // 显示关联选项
              return;
            } else if (
                this.relAnswerNo[this.questionIndex] != "" &&
                !currentElement.parentNode.classList.contains("relItem")
            ) {
              // 隐藏关联答案
              this.hideRelAnswerItem(currentElement.parentNode); // 隐藏关联选项
            }
            // 该题目有关联选项时，不能直接跳转到下一题
            if (this.relAnswerNo[this.questionIndex] != "") {
              chooseed = true;
              quesItem.isChooseed = true;
              // 选择后更改答案列表
              ansAtr.push(ansStr.substring(0, ansStr.length - 1));
              this.$set(this.answerList, quesIdex, ansAtr.join("|"));
              return;
            }
            // 已选择的单选题则跳到下一题
            if (
                ansItem.checked &&
                flag &&
                this.questionIndex + 1 < this.questionList.length
            ) {
              setTimeout(() => {
                this.questionIndex++;
              }, 100);
            }
            chooseed = true;
          } else {
            // 如果当前点击的是关联选项 不清除已经选中的选项
            if (!currentElement.parentNode.classList.contains("relItem")) {
              que.checked = false;
            }
          }
        }
      });
      if (chooseed) {
        quesItem.isChooseed = true;
      } else {
        quesItem.isChooseed = false;
      }
      // 选择后更改答案列表
      ansAtr.push(ansStr.substring(0, ansStr.length - 1));
      this.$set(this.answerList, quesIdex, ansAtr.join("|"));
    },
    showRelAnswerItem(el) {
      var childrens = el.parentNode.children; // 获取父级的所有子节点
      for (var i = 0; i < childrens.length; i++) {
        // 循环
        // 展示兄弟元素中有className为relItem的元素
        if (childrens[i].classList.contains("relItem")) {
          // 展示
          childrens[i].style.display = "block";
        }
      }
    },
    hideRelAnswerItem(el) {
      var childrens = el.parentNode.children; // 获取父级的所有子节点
      for (var i = 0; i < childrens.length; i++) {
        // 循环
        // 展示兄弟元素中有className为relItem的元素
        if (childrens[i].classList.contains("relItem")) {
          // 展示
          childrens[i].style.display = "none";
        }
      }
    },
    nextQuestion() {
      // 下一题，多选题单选不能为空，文本输入可以为空
      if (!this.questionNow.isChooseed && this.questionNow.questionKind !== '2') return;
      this.questionIndex++;
    },
    // 上一题
    prevQuestion() {
      this.questionIndex--;
    },
    submitAns() {
      // 知识测评提交之前由前端来做，现改为pageflow中做控制，前端直接提交就OK了
      // if (this.questionNow.paperTypes === '1') {

      // 文本输入的答案处理 题号_内容_code
      let keys = Object.keys(this.inputObj);
      for (let key of keys) {
        let keyArr = key.split('_'),
            idx = keyArr[0],
            no = keyArr[1]
        this.answerList[idx] = no + (this.inputObj[key] ? ('_' + this.inputObj[key]) : '_' + no) + '_code'
      }

      this.$emit('submitAns', {
        paperAnswer: this.answerList.join("|"),
        paperType: this.questionNow.paperTypes
      })

    },
    submitResultCall(res) {
      if (res.error_no === "0") {
        // 展示结果页
        if (!this.handleResult) {
          // 不需要处理结果，直接到下一步
          this.$parent.emitNextEvent();
          return;
        }
        this.quesitionResult = res.questionResult[0];
        this.quesitionResult.paperScore = this.quesitionResult.score
            ? this.quesitionResult.score
            : this.quesitionResult.paperScore;
        this.showResultFunc();
      } else {
        _hvueAlert({
          title: "提示",
          mes: res.error_info
        });
      }
    },
    // 过滤年龄
    filterAge(age) {
      if (age >= 18 && age <= 30) {
        // 18-30
        return 0;
      } else if (age >= 31 && age <= 40) {
        // 31-40
        return 1;
      } else if (age >= 41 && age <= 50) {
        // 41-50
        return 2;
      } else if (age >= 51 && age <= 60) {
        // 51-60
        return 3;
      } else if (age > 60) {
        // 超过60
        return 4;
      }
      //  else {
      //   // 小于18
      //   return 5
      // }
    },
    // 过滤教育程度
    filterEdu(education) {
      if (education) {
        switch (education) {
          case "1": // 博士
          case "2": // 硕士
            return 3;
            break;
          case "3": // 本科
            return 2;
            break;
          case "4": // 大专
          case "5": // 中专
            return 1;
            break;
          default:
            // 高中或以下
            return 0;
            break;
        }
      } else {
        return "";
      }
    }
  }
};
</script>
<style scoped>
.prev {
  position: relative;
}
.icon_check:before {
  display: block;
  content: "";
  width: 0.16rem;
  height: 0.16rem;
  position: absolute;
  top: 0.14rem;
  left: 0;
  border: 1px solid #ccc;
  border-radius: 25%;
}
.question_page {
  padding-bottom: 0.95rem;
  position: relative;
}
.question_page .icon_check {
  color: #666;
  display: block;
  padding: 0.11rem 0.15rem 0.11rem 0.32rem;
  font-size: 0.16rem;
  min-height: 0.24rem;
  line-height: 0.24rem;
  position: relative;
}
.question_page .teare01{
  width: 90%;
  padding-left: 0.1rem!important;
  margin: 0 auto;
  border: 1px solid #d8d5d5;
}
.test_infobox {
  position: fixed;
  width: 100%;
  bottom: 0;
}

.test_infobox a.next {
  right: 0;
  position: relative;
  float: right;
}
.redCls {
  color: red;
}
</style>
