<template>
  <div></div>
</template>

<script>
export default {
  name: 'witness_ths',
  data() {
    return {
      userInfo: $h.getSession('ygtUserInfo', { decrypt: false }),
    };
  },
  methods: {
    videoCallBack(data) {
      let anychatRsp = data.anychatRsp; // 原生返回js视频通话结果
      let msg = decodeURI(anychatRsp);
      msg = msg.replace(/\\&quot;/g, '"'); // 将返回信息中的引用还原
      msg = msg.replace(/&quot;/g, '"'); // 将返回json中多余的引号去掉

      //H5不再接管同花顺的返回
      callNativeHandler(
        "JSWangTingEvent",
        {
          action: "SetH5DealBackEvent",
          param: {"isDealBackEvent": false}
        },
        function (callback) {}
      );
      window.videoCallBack(msg)
    },
    start(roomInfoArr) {
      let videodata = {
        action: 'VideoWitness',
        reqId: 'VideoWitness',
        param: {
          loginName: 'user' + this.userInfo.userId,
          loginPwd: '123456',
          roomId: roomInfoArr[2],
          roomPwd: '',
          anychatIp: roomInfoArr[0],
          anychatPort: roomInfoArr[1],
          showTips: "0"
        }
      };

      callNativeHandler('JSWangTingEvent', videodata, data => {
        this.videoCallBack(data.param);
      });
    }
  },
  activated() {
  },
  created() {
  }
};
</script>
