<template>
    <div class="record_box" v-if="isShow">
      <ul>
        <li
            v-for="(d, index) in dataList"
            @click.stop="selClick(d, index)"
            :key="index"
        >
          <span class="icon_radio" :class="{ 'checked': selMap[index] }">{{d.value}}</span>
        </li>
      </ul>
    </div>
</template>

<script>
import { queryDictionary } from '@/common/util'
export default {
  name: 'selRadio<PERSON>ox',
  components: {
  },
  model: {
    prop: 'isShow',
    event: 'change',
  },
  props: {
    isShow: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: '请选择',
    },
    category: {
      type: String,
    },
    type: {
      type: String,
    },
    initData: {
      type: Array,
      default: () => [],
    },
    defaultStr: {
      type: String,
      default: '',
    },
    mult: {
      // 是否多选
      type: Boolean,
      default: false,
    },
    selectAble: {
      // 是否可选择
      type: Boolean,
      default: false,
    },
    idString: {
      // 指定id的key
      type: String,
      default: 'key',
    }
  },
  data () {
    return {
      dataList: [],
      selMap: {},
    }
  },
  mounted () {
    window.phoneBackBtnCallBack = this.pageBack
  },
  watch: {
    'defaultStr': {
      handler (val) {
        this.initDefault()
      },
      immediate: true,
    }
  },
  methods: {
    pageBack () {
      this.$emit('change', false)
    },
    selClick (item, index) {
      if (!this.selectAble) {
        return false;
      }
      if (this.mult) {
        this.$set(this.selMap, index, !this.selMap[index])
      } else {
        this.selMap = {};
        this.$set(this.selMap, index, !this.selMap[index])
        this.$emit('selRadioCallback', { type: this.type, data: item, index: index })
      }
    },
    confirmClick () {
      let sel = []
      for (let index in this.selMap) {
        sel.push(this.dataList[index])
      }
      if (sel.length === 0) {
        return
      }
      this.$emit('selCallback', sel)
      this.pageBack()
    },
    initDefault () {
      let that = this
      that.dataList.forEach((a, b) => {
        that.defaultStr.split(';').forEach(c => {
          if (c === a[that.idString]) {
            this.$set(that.selMap, b, true)
          }
        })
      })
    }
  },
  created () {
    if (this.category) {
      queryDictionary({ type: this.category }, data => {
        this.dataList = this.initData.concat(data)
        this.initDefault()
      })
    } else {
      this.dataList = this.initData
      this.initDefault()
    }
  },
  destroyed () {}
}
</script>
