<template>
  <div class="page_main">
    <headComponent v-if="isShow && !showSelBox" headerTitle="紧急联系人"></headComponent>
    <article v-if="isShow && !showSelBox" class="content">
      <div class="user_main">
        <div class="input_form">
          <div class="ui field text">
            <label class="ui label">姓名</label>
            <input
              type="text"
              class="ui input"
              placeholder="输入姓名"
              v-model.trim="name"
              maxlength="20"
            />
            <a class="txt_close" @click.stop="name=''" v-show="name!=''"></a>
          </div>
          <div class="ui field text">
            <label class="ui label">关系</label>
            <div class="ui dropdown" @click.stop="showSelBoxClick">
              <strong>{{relation}}</strong>
            </div>
          </div>
          <div class="ui field text">
            <label class="ui label">手机号码</label>
            <input
              type="text"
              class="ui input"
              placeholder="输入手机号"
              v-model.trim="mobile"
              maxlength="11"
            />
            <a class="txt_close" @click.stop="mobile=''" v-show="mobile!=''"></a>
          </div>
        </div>
        <div class="ce_btn mt20">
          <a class="ui button block rounded" @click.stop="verification">确认修改</a>
        </div>
      </div>
    </article>
    <selBox
      v-if="showSelBox"
      v-model="showSelBox"
      title="关系"
      category="ismp.relation"
      :defaultStr="relation"
      @selCallback="selCallback"
    ></selBox>
  </div>
</template>

<script>
import headComponent from '@/components/headComponent' // 头部
import selBox from '@/components/selBox' // 选择器
export default {
  name: 'secRelation',
  components: {
    headComponent,
    selBox
  },
  model: {
    prop: 'isShow',
    event: 'change'
  },
  props: {
    isShow: {
      type: Boolean,
      default: false
    },
    name: {
      type: String,
      default: ''
    },
    relation: {
      type: String,
      default: ''
    },
    relationKey: {
      type: String,
      default: ''
    },
    mobile: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      showSelBox: false
    }
  },
  mounted () {
    window.phoneBackBtnCallBack = this.pageBack
  },
  methods: {
    pageBack () {
      this.$emit('change', false)
    },
    // 校验格式
    verification () {
      if ($h.isEmptyString(this.name)) {
        _hvueToast({ mes: '请输入姓名' })
        return
      }
      if (!/^[\u4E00-\u9FA5]{2,14}([·•]?[\u4E00-\u9FA5]+)*$/.test(this.name)) {
        _hvueToast({ mes: '姓名格式不正确' })
        return
      }
      if ($h.isEmptyString(this.relation)) {
        _hvueToast({ mes: '请选择关系' })
        return
      }
      if ($h.isEmptyString(this.mobile)) {
        _hvueToast({ mes: '请输入手机号码' })
        return
      }
      if (!/^1\d{10}$/.test(this.mobile)) {
        _hvueToast({ mes: '手机号码格式不正确' })
        return
      }
      if (this.name === $h.getSession('ygtUserInfo', {decrypt: false}).name) {
        _hvueToast({ mes: '姓名不能是本人姓名' })
        return false
      }
      if (this.mobile === $h.getSession('ygtUserInfo', {decrypt: false}).mobile) {
        _hvueToast({ mes: '手机号码不能是本人手机号码' })
        return false
      }

      this.$emit('secRelationCallback', {
        name: this.name,
        relationKey: this.relationKey,
        relation: this.relation,
        mobile: this.mobile
      })
      this.pageBack()
    },
    showSelBoxClick () {
      this.showSelBox = true
    },
    selCallback (a) {
      this.relationKey = a.data.key
      this.relation = a.data.value
    }
  }
}
</script>

<style>
</style>
