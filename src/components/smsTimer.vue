<!--短信验证码倒计时
    使用：
        参数：initDesc 倒计时结束后显示的文字
              startFlag 开始倒计时的标志。注意：父子组件数据双向绑定  使用时v-model传入
              time:倒计时的时间 必传
        事件： sendSms  点击发送短信验证码后的回调方法。

-->
<template>
  <div>
    <a v-if="sendMsgDisabled" class="code_btn time">{{disabledDesc}}</a>
    <a v-if="!sendMsgDisabled" class="code_btn" @click.stop.prevent="getVerifyCode">{{abledDesc}}</a>
  </div>
</template>
<script>
export default {
  props: {
    initDesc: {
      type: String,
      default: '获取验证码'
    },
    startFlag: {
      type: Boolean,
      default: false
    },
    time: {
      type: Number,
      default: 120
    }
  },
  model: {
    prop: 'startFlag',
    event: 'startOrStop'
  },
  data () {
    return {
      initTime: this.time,
      sendMsgDisabled: false,
      disabledDesc: this.time + 's后重新发送',
      abledDesc: '获取验证码', // 可点击状态的按钮文字
      runFlag: this.startFlag,
      interval: null // 短信验证码倒计时定时器
    }
  },
  watch: {
    // 监听是否开始倒计时的标志 倒计时需要在发送短信验证码成功后才能开始
    startFlag (newValue, oldValue) {
      this.sendMsgDisabled = this.startFlag
      if (this.sendMsgDisabled) {
        // 开始倒计时
        this.start()
      }
    }
  },
  created () {
    if (this.initDesc) {
      this.abledDesc = this.initDesc
    }
  },
  methods: {
    start () {
      this.sendMsgDisabled = true
      let me = this
      me.interval = window.setInterval(() => {
        me.initTime--
        if (me.initTime <= 0) {
          me.initTime = me.time
          me.sendMsgDisabled = false
          me.disabledDesc = me.initTime + 's后重新发送'
          me.$emit('startOrStop', false) // 更改startFlag的值
          window.clearInterval(this.interval)
        } else {
          me.disabledDesc = me.initTime + 's后重新发送'
        }
      }, 1000)
    },
    clearCountDown () {
      if (this.interval) {
        clearInterval(this.interval) || (this.interval = null)
        this.initTime = this.time
        this.sendMsgDisabled = false
        this.disabledDesc = this.time + 's后重新发送'
        this.$emit('startOrStop', false) // 更改startFlag的值
      }
    },
    getVerifyCode () {
      this.$emit('sendSms')
    }
  },
  destroyed () {
    this.clearCountDown()
  }
}
</script>
