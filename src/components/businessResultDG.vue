<!-- 结果页组件 -->
<template>
  <div v-show="pageParam.length">
    <headComponent headerTitle="结果页"></headComponent>
    <template>
      <div class="result_main">
        <div
            :class="{icon_error:(businessFlowInfo.handleStatus=='4' || businessFlowInfo.handleStatus=='2')}"
        ></div>
        <h5>{{showDealDesc}}</h5>
        <p
            class="center"
        >提交时间：{{businessFlowInfo.updateDate}}</p>
      </div>
      <div class="result_sfinfo">
        <ul>
          <li v-for="(item,index) in pageParam" :key="index">
            <span  class="tit">{{trdaccount}}</span>
            <p>
              <span
                :class="item.dealBusinessResultState|fillStateStyle"
                class="status"
              >{{item.dealBusinessResultState|fillStateDesc}}</span>
            </p>
            <div v-if="item.dealBusinessResultState ==2" class="fail_reason">
              <span>失败原因</span>
              <p
                @click.stop="showErrorInfo(item.dealBusinessDesc)"
              >{{item.dealBusinessDesc.substring(0,8)}}...</p>
            </div>
          </li>
        </ul>
      </div>
      <!--  非账户类业务，办理失败时展示的失败原因界面  -->
      <div class="ce_btn mt20">
        <a v-throttle v-if="businessFlowInfo.formStatus === '4'" class="ui button block rounded" @click.stop="reject">重新申请</a>
        <a v-throttle class="ui button block rounded mt10" @click.stop="backIndex">返回首页</a>
      </div>
    </template>
  </div>
</template>

<script>
import headComponent from '@/components/headComponent'
import { queryFailResults } from '@/service/comServiceNew'
export default {
  name: 'businessResult',
  components: {
    headComponent,
  },
  props: {
    // pageParam: {
    //   type: Array
    // }
  },
  data () {
    return {
      pageParam: [],
      businessFlowInfo: [],
      trdaccount: '',
      serivalId: '',
      showDealDesc: '',
      busiType: 1, // 1账户类业务 2其他业务
      formStatus: '', // 表单状态
    }
  },
  methods: {
    getDealDesc (handleStatus) {
      if (handleStatus === '0') {
        return '成功提交，审核中'
      }
      if (handleStatus === '1') {
        return '办理成功'
      }
      if (handleStatus === '2') {
        return '审核失败'
      }
      // return '成功提交，处理中'
    },
    giveup () {
      // 放弃办理
      this.$bus.emit('giveUp') // 通过vuebus调用businessFlowInfo组件giveUp事件
    },
    showErrorInfo (info) {
      _hvueAlert({
        title: '失败原因',
        mes: info,
      })
    },
    // 初始化流程对象
    async queryFailResults( serivalId = '' ) {
      let results = await queryFailResults({
        serivalId: serivalId,
        businessCode: this.$route.query.type,
        userId: $h.getSession('ygtUserInfo', {decrypt: false}).userId,
      });
      this.$nextTick(() => {
        this.pageParam = this.pageParam.concat(results.businessResult);
        this.businessFlowInfo = Object.assign(this.businessFlowInfo, results.businessFlowInfo[0]);
      })
    },
    pageBack() {
      this.$bus.emit('isActiveTabkey', this.$route.query.key) // 通过vuebus调用
      this.$router.go(-1)
    },
    backIndex () {
      this.$router.push({
        name: 'index',
      })
    },
  },
  created () {
    this.serivalId = this.$route.query.serivalId;
    this.trdaccount = `${this.$route.query.market === '00' ? '深A： ' : '沪A：'}${this.$route.query.trdaccount.split('|')[0]}`;
  },
  watch: {
    'serivalId': {
      handler (newVal) {
        if (newVal) {
          this.queryFailResults(newVal);
        }
      },
      immediate: true,
    },
    'pageParam': {
      handler (newVal) {
        if (newVal.length) {
          this.showDealDesc = `${this.$route.query.type === 'dzjy' ? '大宗交易申请' : '固收平台申请'}` + this.getDealDesc(newVal[0].dealBusinessResultState)
        }
      },
      deep: true
    }
  },
}
</script>
