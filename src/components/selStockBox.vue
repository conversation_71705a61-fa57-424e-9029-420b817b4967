<template>
  <div class="page_main" v-if="isShow">
    <headComponent :headerTitle="title"></headComponent>
    <article class="content">
      <slot>
        <ul class="select_list">
          <li
            v-for="(d, index) in dataList"
            @click.stop="selClick(d, index)"
            :class="{ active: selMap[index] }"
            :key="index"
          >
            <span v-text="`${isMarket ? (d.stkbdName + ':' + d.stockAccount) : d.stockAccount }`"></span>
          </li>
        </ul>
      </slot>
    </article>
  </div>
</template>

<script>
import headComponent from '@/components/headComponent' // 头部
import { queryStockList } from '@/service/comServiceNew'
export default {
  name: 'selStockBox',
  components: {
    headComponent
  },
  model: {
    prop: 'isShow',
    event: 'change'
  },
  props: {
    isShow: {
      type: <PERSON>olean,
      default: false
    },
    title: {
      type: String,
      default: '请选择'
    },
    category: {
      type: String
    },
    initData: {
      type: Array,
      default: () => []
    },
    defaultStr: {
      type: String,
      default: ''
    },
    mult: {
      // 是否多选
      type: Boolean,
      default: false
    },
    isMarket: {
      // 是否包含市场
      type: Boolean,
      default: false
    },
    idString: {
      // 指定id的key
      type: String,
      default: 'key'
    }
  },
  data () {
    return {
      dataList: [],
      selMap: {}
    }
  },
  mounted () {
    window.phoneBackBtnCallBack = this.pageBack
  },
  methods: {
    pageBack () {
      this.$emit('change', false)
    },
    selClick (item, index) {
      if (this.mult) {
        this.$set(this.selMap, index, !this.selMap[index])
      } else {
        this.selMap = {};
        this.$set(this.selMap, index, !this.selMap[index])
        this.$emit('selCallback', { data: item, index: index })
        this.pageBack()
      }
    },
    confirmClick () {
      let sel = []
      for (let index in this.selMap) {
        sel.push(this.dataList[index])
      }
      if (sel.length === 0) {
        return
      }
      this.$emit('selCallback', sel)
      this.pageBack()
    },
    initDefault () {
      let that = this
      that.dataList.forEach((a, b) => {
        that.defaultStr.split(';').forEach(c => {
          if (c.includes(a[that.idString])) {
            that.selMap[b] = true
          }
        })
      })
    }
  },
  created () {
    if (this.category && this.category == 'trdaccount') {
      queryStockList({
        userId: $h.getSession('ygtUserInfo', {decrypt: false}).userId,
        isQueryZD: '0',
        flow_name: this.$route.query.type
      }).then(
          res => {
            if (res.error_no === '0') {
              let _results = res.results ? res.results : res.DataSet;
              this.dataList = this.initData.concat(_results)
              this.initDefault()
            } else {
              _hvueToast({
                icon: 'error',
                mes: res.error_info
              })
            }
          },
          err => {
            console.log(err)
          }
      )
    } else {
      this.dataList = this.initData
      this.initDefault()
    }
  },
  destroyed () {}
}
</script>
