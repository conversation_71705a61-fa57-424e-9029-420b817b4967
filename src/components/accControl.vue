<template>
  <div class="page_main">
    <headComponent headerTitle="修改账户实际控制人与受益人"></headComponent>
    <article class="content">
      <div class="wtclass_main">
        <ul class="wtclass_list">
          <li>
            <span>账户实际控制人是本人</span>
            <div class="ui switch">
              <input type="checkbox" checked="checked" />
              <div class="ui switch-inner">
                <div class="ui switch-arrow"></div>
              </div>
            </div>
          </li>
          <li>
            <span>账户实际受益人是本人</span>
            <div class="ui switch">
              <input type="checkbox" />
              <div class="ui switch-inner">
                <div class="ui switch-arrow"></div>
              </div>
            </div>
          </li>
        </ul>
      </div>
      <p class="bot_tips">根据监管要求，账户实际控制人和实际受益人必须为客户本人，无法线上修改。</p>
      <div class="ce_btn mt20">
        <a class="ui button rounded block" @click.stop="verification">确定</a>
      </div>
    </article>
  </div>
</template>

<script>
import headComponent from '@/components/headComponent' // 头部
export default {
  name: 'accControl',
  components: {
    headComponent
  },
  model: {
    prop: 'isShow',
    event: 'change'
  },
  props: {
    isShow: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {}
  },
  mounted () {
    window.phoneBackBtnCallBack = this.pageBack
  },
  methods: {
    pageBack () {
      this.$emit('change', false)
    },
    // 校验格式
    verification () {
      this.pageBack()
    }
  }
}
</script>

<style>
</style>
