<!--

-->
<template>
    <div class="tab_nav" v-if="isShow">
      <ul>
        <li :class="{'active': key === activeKey}"
            :key="key" v-for="(item, key) in tabList"
            @click.prevent="_onChange(item, key)"
        >
          <a href="javascript:void(0)"><span>{{item.name}}</span></a>
        </li>
      </ul>
    </div>
</template>
<script>
export default {
  name: 'tab-change',
  model: {
    prop: 'isShow',
    event: 'change'
  },
  props: {
    isShow: {
      type: Boolean,
      default: false,
    },
    activeKey: {
      type: Number,
      default: -1,
    },
    tabList: {
      type: Array,
      default: () => [],
    }
  },
  mounted () {
  },
  methods: {
    _onChange () {// tab切换
      this.$emit('changeTab', ...arguments)
    },
  }
}
</script>
<style>
</style>
