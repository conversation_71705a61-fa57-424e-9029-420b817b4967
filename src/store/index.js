import Vue from 'vue'
import Vuex from 'vuex'
import mutations from './mutations'
import actions from './actions'
import router from './modules/router'

Vue.use(Vuex)

const state = {
  currentBusinessCode: '',
  isShowLoading: false,
  tips: '加载中',
  toPage: null,
  businessNextBtnShow: false,
  nextBtnText: '下一步',
  isWhite: false, // 页面整体背景颜色是否为白色
  isShowHead: true, // 是否显示头部
  agreementIsFixed: false, // 协议勾选是否固定在页面底部
  allFundCheckClickFlag: false, // 基金开户是否全选
  nextBtnDisabled: false, // 下一步按钮是否是灰色
  nextBtnCss: false, // 下一步按钮是否为返回样式
  selectedAccountsLength: 0, // 已勾选账户的数量
  cachePath: [''], // 需要被缓存的路由
  handleStatus: '', // 业务办理状态
  formStatus: '', // 表单状态
  isScroll: false // 是否允许滚动
}

export default new Vuex.Store({
  state,
  mutations,
  actions,
  modules: {
    router
  }
})
