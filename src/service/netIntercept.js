import router from 'router'
export default {
  // 请求是否显示loading，(统一设置:单个请求的入参会覆盖这里，下同),默认 true
  loading: false,
  // 请求参数签名(统一设置), 默认false
  sign: false,
  // 请求方法, 默认 get
  method: 'get',
  // 请求超时时间(统一设置), 默认 30000(ms)
  timeout: 30000,
  // 跨域是否带cookie验证，默认为true
  withCredentials: true,
  // 参数是否utf-8编码, 默认true
  encode: true,
  // 后端接口版本(统一设置)，默认1.0，目前是中台产品线需要用2.0，统一封装支持。
  servletVersion: '1.0',
  // 是否原生代理请求(统一设置), 默认false
  nativeProxy: false,
  // 原生代理 请求加密模式，对应50118插件号的入参:mode, 默认0(正常不加密)
  nativeProxyMode: 0,
  // 请求头，默认{}
  headers: {},
  // 请求前统一拦截处理方法
  interceptRequest: function (config) {
    // console.log(config)
  },
  // 请求响应统一拦截处理方法, 返回false则中断promise链
  interceptResponse: function (res) {
    if (res.error_no !== '0') {
      // 请求错误时,关闭loading层
      _hvueLoading.closeLoading()
    }
    // 登录拦截统一处理
    if (res && res.error_no == '-999') {
      _hvueToast({
        icon: 'error',
        mes: '请先登录(-999)'
      })
      // h5访问时判断登录情况
      if ($hvue.platform === '0' && !$h.getSession('fromTHSGB')) {
        router.push({
          path: '/login'
        })
      } else {
        if ($h.getSession('loginType') === '1') {
          $h.clearSession('isLogin')
        } else {
          $h.clearSession('isLoginXy')
        }
        $h.clearSession('ygtUserInfo')
        router.push({
          path: '/login'
        })
      }
      return false
    }
    // 低速网络环境下,前后端节点不一致导致的报错
    if (res && res.error_no === '-4000015') {
      _hvueAlert({
        mes: res.error_info,
        callback: () => {
          router.push({
            path: '/index'
          })
        }
      })
      return false
    }
    if (res && res.error_no == '-4000007') {
      router.push({
        path: '/index'
      })
      return false
    }
    if (res && res.error_no == '-7400320') {
      _hvueToast({
        icon: 'error',
        mes: res.error_info,
        callback: () => {
          router.push({
            path: '/index'
          })
        }
      })
      return false
    }
    // add20210325 柜台错误号150974 当日问卷次数超过上限，不允许提交答案
    if (res && res.error_no == '150974') {
      _hvueAlert({
        title: '提示',
        mes: res.error_info,
        callback: () => {
          router.push({
            path: '/index'
          })
        }
      })
      return false
    }
  },
  // 请求服务器报错时处理方法(即没有正常返回响应结果时进入)
  handleError: handleError
}

function handleError (err) {
  // 请求错误时,关闭loading层
  _hvueLoading.closeLoading()
  if (err && err.response) {
    switch (err.response.status) {
      case 401:
        _hvueToast({
          icon: 'error',
          mes: '访问未授权(401)',
          callback: () => {
            errorCallback()
          }
        })
        break
      case 403:
        _hvueToast({
          icon: 'error',
          mes: '访问被拒绝(403)',
          callback: () => {
            errorCallback()
          }
        })
        break
      case 404:
        _hvueToast({
          icon: 'error',
          mes: '请求地址或接口不存在(404)',
          callback: () => {
            errorCallback()
          }
        })
        break
      case 500:
        _hvueToast({
          icon: 'error',
          mes: '服务器内部服务错误(500)',
          callback: () => {
            errorCallback()
          }
        })
        break
      case 501:
        _hvueToast({
          icon: 'error',
          mes: '请求服务未实现(501)',
          callback: () => {
            errorCallback()
          }
        })
        break
      case 502:
        _hvueToast({
          icon: 'error',
          mes: 'Web服务器故障(502)',
          callback: () => {
            errorCallback()
          }
        })
        break
      case 503:
        _hvueToast({
          icon: 'error',
          mes: '服务不可用(503)',
          callback: () => {
            errorCallback()
          }
        })
        break
      case 504:
        _hvueToast({
          icon: 'error',
          mes: '网关超时(504)',
          callback: () => {
            errorCallback()
          }
        })
        break
      case 505:
        _hvueToast({
          icon: 'error',
          mes: '服务器不支持</br>请求HTTP版本(505)',
          callback: () => {
            errorCallback()
          }
        })
        break
      default:
        _hvueToast({
          icon: 'error',
          mes: `ajax请求出错(${err.response.status})`,
          callback: () => {
            errorCallback()
          }
        })
        break
    }
  } else {
    if (err.message.indexOf('Network Error') !== -1) {
      _hvueToast({
        icon: 'error',
        mes: '网络异常或中断',
        callback: () => {
          errorCallback()
        }

      })
    } else if (err.message.indexOf('timeout of') !== -1) {
      _hvueAlert({
        title: '提示',
        mes: '请求超时(timeout)',
        callback: () => {
          router.push({
            path: '/index'
          })
        }
      })
    } else {
      _hvueToast({
        icon: 'error',
        mes: `请求错误: ${err.message}`,
        callback: () => {
          errorCallback()
        }
      })
    }
  }
  // 返回false则中断promise
  return false
}

function errorCallback () {
  // 确定之后的回调,当页面空白时，直接跳回首页
  let sectionDom = document.querySelectorAll('section.main')
  let viewDom = sectionDom[0].getElementsByTagName('div')
  if (viewDom.length == 1) {
    window.$this.$router.push({
      name: 'index'
    })
  }
}
