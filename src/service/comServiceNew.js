// 请求模块暴露三个方法：
// request： ajax请求唯一方法；
// cancelRequest: 取消所有正在进行的请求，一般在切换路由或关闭页面时调用；
// cancelRequestUrl： 取消单个正在进行的请求.
import { request } from 'thinkive-hvue'

/**
 * 所有请求的公用方法 可以添加公用参数 或者其他公用操作
 * @param {String} funcNo 功能号
 * @param {*} params 接口请求参数
 * @param {*} options 请求控制参数 如 请求方法 是否加签， 具体定义见netIntercept.js
 */
export function commonService (funcNo, params, options = {}) {
  // 默认控制参数
  let _options = {
    loading: false, // 是否显示等待层
    sign: false, // 是否加签名
    signModule: 'ssoSignNew', //  签名参数模块(对象)
    protocol: 'http', // 请求协议，http or websocket. 默认http
    url: SERVER_URL.YGT_NEW_SERVER, // 请求地址
    method: 'post', // 请求方式, 默认 get
    headers: {}, // 请求头
    timeout: 60000, // 超时时间
    withCredentials: true, // 是否跨域，默认为true
    encode: true, // 参数是否utf-8编码, 默认true
    global: false, // 是否全局请求，是则页面切换不会被取消
    servletVersion: '1.0', // 后端接口版本
    nativeProxy: false, // 是否走原生代理请求, 默认false
    nativeProxyMode: 0 // 原生代理 请求模式，对应50118插件号的入参:mode
  }
  let opSource = $hvue.platform
  let opStation = 'h5'
  let deviceMAC = ''
  let deviceIP = ''
  if ($hvue.platform == '0') {
    if ($h.getSession('fromTHSGB')) {
      if ($hvue.iBrowser.android) {
        opSource = '6' // android手机
      } else if ($hvue.iBrowser.ios) {
        opSource = '7' // ios手机
      }
      opStation = $h.getSession('op_station')
      opStation = opStation.replace(new RegExp('undefined', 'gm'), 'NA')
    } else {
      opSource = '1' // h5
      _options.sign = false
    }
  } else {
    if ($hvue.platform == '1') {
      opSource = '3' // android手机
    } else if ($hvue.platform == '2') {
      opSource = '4' // ios手机
    }
    // 调用sdk查询设备相关信息

    let params = {
      funcNo: '50043',
      key: 'activePhone',
      moduleName: 'open' // 必须为open
    }
    let re = $h.callMessageNative(params)
    let phone = re.results[0].value

    let param = {
      funcNo: '50001',
      moduleName: 'open' // 必须为open
    }
    let res = $h.callMessageNative(param)
    let result = res.results[0]
    deviceMAC = result.deviceMAC
    deviceIP = result.deviceIP
    let _platform = $hvue.platform == '1' ? 'MA;' : 'MI;'
    let MAC = $hvue.platform == '1' ? ';MAC=' + result.deviceMAC : ';MAC=NA'
    MAC = MAC.replace(new RegExp(':', 'gm'), '')
    let IMEI = $hvue.platform == '1' ? ';IMEI=' + result.deviceIMEI : ';IDFV=' + result.deviceMAC
    IMEI = IMEI.replace(new RegExp(':', 'gm'), '')
    let OSV = $hvue.platform == '1' ? 'Android' : 'IOS'
    opStation = _platform + 'IIP=' + result.deviceIP + ';IPORT=NA;LIP=' + result.deviceLIP + MAC + IMEI + ';RMPN=' + phone + ';UMPN=' + result.deviceUMPN + ';ICCID=' + result.deviceICCID + ';OSV=' + OSV + result.deviceSysVersion + ';IMSI=' + result.deviceIMSI + ';@THINKIVE_WT;' + result.nativeVersion
    opStation = opStation.replace(new RegExp('undefined', 'gm'), 'NA')
    $h.setSession('softIdentifier', result.softIdentifier)// 软件包名
  }
  // 接口公共入参
  let _params = {
    funcNo: funcNo,
    opSource: opSource,
    opStation: opStation,
    ip: deviceIP,
    mac: deviceMAC
  }
  return new Promise((resolve, reject) => {
    // 如果完全默认控制参数，options, _options 不必传, 如：
    // request({_params, params})
    // options处理
    options = options || {}
    let loading = options.loading == null ? true : options.loading // 默认显示等待层
    if (document.getElementsByClassName('hui-dialog-white-mask').length === 0) {
      if(options.isShowWait !== '0'){
        _hvueLoading.openLoading('加载中')
      }
      // _hvueLoading.open("加载中");
    }
    options.loading = false // 底层请求不显示等待层，等待层的处理在此处自己处理。底层没有是否最后一次请求的控制，等待层会加载多次
    request({
      _params,
      params,
      options,
      _options
    }).then(res => {
      let hideLoading = options.isLastReq == null ? true : options.isLastReq // 是否隐藏加载层。
      if (hideLoading) _hvueLoading.closeLoading()
      resolve(res)
    }, err => {
      reject(err)
    })
  })
}
/**
 * @desc 获取业务列表
 * @param {Object} params 业务参数
 * {
 * source 渠道 1pc 2h5 3vtm
 * }
 * @param {Object} options 接口请求相关设置 参考第一个接口的说明
 * {
 *    "cache"     :{Boolean}  是否缓存请求结果 N 默认 false
 *    "url"       :{String}   接口请求地址 N 默认为webpack配置注入SERVER_URL
 *    "sign"      :{Boolean}  请求接口默认是否加签 N 默认false
 *    "encode"    :{Boolean}  请求参数是否url编码 N 默认为true
 *    "method"    :{String}   请求方法 N 默认为POST
 *    "loading"   :{Boolean}  是否显示loading N 默认为true
 * }
 */
export function businessList (params, options) {
  return commonService('1005319', params, options)
}

/**
 * 功能：登录
 * funcNo:1005300
 * {
 *    account; // 登录账号
 *    password; // 密码
 *    accountType || ""; // 账户类型
 * }
 * @param {请求参数} params
 * }
 */
export function userLogin (params, options) {
  return commonService('1005300', params, options)
}

/**
 * 功能：查询用户当前业务的表单状态-formStatus
 * funcNo:1005312
 * @param {请求参数} params
 * @param {控制参数} option 参考1005319说明
 */
export function queryUserFlowStatus (params, options) {
  return commonService('1005312', params, options)
}

/**
 * 功能：查询用户业务流水
 * funcNo:1005312
 * @param {请求参数} params
 * @param {控制参数} option
 */
export function queryBusinessRecords (params, options) {
  return commonService('1005436', params, options)
}

/**
 * 功能：调用ocr识别并上传图片
 * funcNo:1005378
 * @param {请求参数} params
 * @param {控制参数} option
 */
export function uploadImg (params, options) {
  return commonService('1005378', params, options)
}

/**
 * 功能：查询数据字典
 * funcNo:1005321
 * @param {请求参数} params
 * @param {控制参数} option
 */
export function getDict (params, option) {
  return commonService('1005321', params, option)
}

/**
 * 功能：查询业务协议
 * funcNo:1005006
 * @param {请求参数} params
 * @param {控制参数} option
 */
export function getProtocolByType (params, option) {
  let ygtUserInfo = $h.getSession('ygtUserInfo', {decrypt: false})
  if (params.userId && ygtUserInfo) {
    params.clientId = ygtUserInfo.clientId
  }
  return commonService('1005006', params, option)
}

/**
 * 功能：查询协议详情
 * funcNo:1005007
 * @param {请求参数} params
 * @param {控制参数} option
 */
export function getProtocolById (params, option) {
  return commonService('1005007', params, option)
}

/**
 * 功能：业务流水写入redis
 * funcNo:1005381
 * @param {请求参数} params
 * @param {控制参数} option
 */
export function setSidInRedis (params, option) {
  return commonService('1005381', params, option)
}

/**
 * 功能：查询业务办理结果
 * funcNo:1005392
 * @param {请求参数} params
 * @param {控制参数} option
 */
export function queryBusinessResults (params, option) {
  return commonService('1005392', params, option)
}

/**
 * 功能：查询驳回原因
 * funcNo:1005412
 * @param {请求参数} params
 *      serivalId
 * @param {控制参数} option
 */
export function queryRejectReason (params, option) {
  return commonService('1005412', params, option)
}

/**
 * 功能：获取图片验证码
 * funcNo:1005415
 * @param {请求参数} params
 * @param {控制参数} option
 */
export function getImgCode (params, option) {
  return commonService('1005415', params, option)
}

/**
 * 功能：校验图片验证码
 * funcNo:1005416
 * @param {请求参数} params
 * @param {控制参数} option
 */
export function verifyImgCode (params, option) {
  return commonService('1005416', params, option)
}

/**
 * 功能：用户主动放弃流程
 * funcNo:1005421
 * @param {请求参数} params
 * @param {控制参数} option
 */
export function closeFlow (params, option) {
  return commonService('1005421', params, option)
}

/**
 * 功能：查询可绑定的银行列表
 * funcNo:1005423
 * @param {请求参数} params
 * @param {控制参数} option
 */
export function queryBankList (params, option) {
  return commonService('1005423', params, option)
}

/**
 * 功能：发送短信验证码
 * funcNo:1005367
 * @param {请求参数} params
 * @param {控制参数} option
 */
export function sendMobileMsg (params, option) {
  return commonService('1005367', params, option)
}

/**
 * 功能：校验短信验证码
 * funcNo:1005368
 * @param {请求参数} params
 * @param {控制参数} option
 */
export function checkMobileMsg (params, option) {
  return commonService('1005368', params, option)
}

/**
 * 功能：结息
 * funcNo:1005424
 * @param {请求参数} params
 * @param {控制参数} option
 */
export function interestSettlement (params, option) {
  return commonService('1005424', params, option)
}

/**
 * 功能：查询资金信息
 * funcNo:1005434
 * @param {请求参数} params：clientId  客户号   fundAccount：资金账户   moneyType：币种 默认人民币
 * 出参：enableBalance  可用资金   fetchBalance：可取资金   marketValue：股票市值   frozenBalance：冻结资金   asset：总资产
 * @param {控制参数} option
 */
export function queryUserMoney (params, option) {
  return commonService('1005434', params, option)
}

/**
 * 功能：查询股票持仓
 * funcNo:1005435
 * @param {请求参数} params 入参：clientId  客户号   fundAccount：资金账户   branchNo：营业部编号
         出参：stockName：股票名称    stockCode：股票代码    holdAmount：股票数量
 * @param {控制参数} option
 */
export function queryStockPositions (params, option) {
  return commonService('1005435', params, option)
}

/**
 * 功能：查询转账流水
 * funcNo:1005438
 * @param {请求参数} params
 *      入参：clientId 客户号   fundAccount 资金帐号 这两个必传
 *          非 必传  bankNo 银行代码  entrustNo 委托编号  pageIndex 当前页数  pageSize 页面条数
 *      出参：stockName：股票名称    stockCode：股票代码    holdAmount：股票数量
 * @param {控制参数} option
 */
export function queryTransferList (params, option) {
  return commonService('1005438', params, option)
}

/**
 * 功能：提交知识测评答案（除新三板业务）
 * funcNo:1005438
 * @param {请求参数} params
 *      入参：clientId 客户号   fundAccount 资金帐号 这两个必传
 *      出参：
 * @param {控制参数} option
 */
export function submitKowledge (params, option) {
  return commonService('1005439', params, option)
}

/**
 * 功能：提交新三板知识测评答案
 * funcNo:1005438
 * @param {请求参数} params
 *      入参：必传 clientId 客户号   fundAccount 资金帐号
 *      出参：
 * @param {控制参数} option
 */
export function submitXsbKowledge (params, option) {
  return commonService('1005450', params, option)
}

/**
 * 功能：电子合同列表查询
 * @param {请求参数} params
 * @param {控制参数} option
 */
export function queryDzhtByProduct (params, option) {
  return commonService('1005160', params, option)
}

/**
 * 功能：电子合同详情查询
 * @param {请求参数} params
 * @param {控制参数} option
 */
export function queryDzhtDetail (params, option) {
  return commonService('1005161', params, option)
}

/**
 * 功能：电子合同产品查询
 * @param {请求参数} params
 * @param {控制参数} option
 */
export function queryProduct (params, option) {
  return commonService('1005164', params, option)
}
/**
 * 功能：预销户存管账户
 * @param {请求参数} params
 * @param {控制参数} option
 */
export function preCancelAcc (params, option) {
  return commonService('1005136', params, option)
}
/**
 * 功能：查询大宗交易办理数据
 * @param {请求参数} params
 * @param {控制参数} option
 */
export function queryRecordList (params, option) {
  return commonService('1005224', params, option)
}
/**
 * 功能：查询大宗交易股东账号(改造后1005015)
 * @param {请求参数} params
 * @param {控制参数} option
 */
export function queryStockList (params, option) {
  return commonService('1005085', params, option)
}

/**
 * 功能：每次返回新的流水
 * funcNo:1005555
 * @param {请求参数} params
 * @param {控制参数} option 参考1005319说明
 */
export function queryNewUserFlowStatus (params, options) {
  return commonService('1005555', params, options)
}

/**
 * 功能：每次返回失败结果
 * funcNo:1005555
 * @param {请求参数} params
 * @param {控制参数} option 1005430
 */
export function queryFailResults (params, options) {
  return commonService('1005430', params, options)
}

/**
 * 功能：银行卡ocr识别
 * funcNo:1005555
 * @param {请求参数} params
 * @param {控制参数} option 1005556
 */
export function bankOcr (params, options) {
  return commonService('1005556', params, options)
}

/* start 问卷回访 */
/**
 * 功能：问卷回访查询列表
 * @param {请求参数} params
 * @param {控制参数} option
 */
export function queryWjhfList (params, option) {
  return commonService('1005563', params, option)
}
/**
 * 功能：查询问卷内容
 * @param {请求参数} params
 * @param {控制参数} option
 */
export function queryWjhfContent (params, option) {
  return commonService('1005565', params, option)
}
/**
 * 功能：提交问卷
 * @param {请求参数} params
 * @param {控制参数} option
 */
export function submitWjhf (params, option) {
  return commonService('1005564', params, option)
}
/**
 * 功能：图片上传的个数
 * @param {请求参数} params
 * @param {控制参数} option
 */
export function pictureAmount (params, option) {
  return commonService('1005009', params, option)
}
/* end 问卷回访 */
/**
 * 功能：验证身份信息并获取资金账户
 * @param {请求参数} params
 * @param {控制参数} option
 */
export function checkUserInfo (params, option) {
  return commonService('1005249', params, option)
}
/**
 * 功能：验证身份信息并获取资金账户
 * @param {请求参数} params
 * @param {控制参数} option
 */
export function sendMobileCode (params, option) {
  return commonService('1005602', params, option)
}
/**
 * 功能：查询佣金率数据
 * @param {请求参数} params
 * @param {控制参数} option
 */
export function queryCommission (params, option) {
  return commonService('1005252', params, option)
}
/**
 * 功能：排队
 * @param {请求参数} params
 * @param {控制参数} option
 */
export function videoQueue (params, option) {
  return commonService('501301', params, option)
}
/**
 * 功能：取消排队
 * @param {请求参数} params
 * @param {控制参数} option
 */
export function cancelQueue (params, option) {
  return commonService('501302', params, option)
}
/**
 * 功能：解密资金账号
 * @param {请求参数} params
 * @param {控制参数} option
 */
export function decodeRsa (params, option) {
  return commonService('1005253', params, option)
}


/**
 * 功能：清除表单数据
 * @param {请求参数} params
 * @param {控制参数} option
 */
export function clearFormData (params, option) {
  return commonService('1005267', params, option)
}

/**
 * 功能：查询销户结果
 * @param {请求参数} params
 * @param {控制参数} option
 */
export function getCloseAccountResult(params, option) {
  return commonService('1005510', params, option);
}

/**
 * 功能：查询销户预约结果
 * @param {请求参数} params
 * @param {控制参数} option
 */
export function getXhyyResult(params, option) {
  return commonService('1005604', params, option);
}

/**
 * 功能：检查用户类型    #result[custTypeCheck][0][accountType] == '1'   code="-8800003" message="机构用户不能办理此业务，请临柜办理"
 * @param {请求参数} params
 * @param {控制参数} option
 */
export function queryUserType(params, option) {
  return commonService('1005330', params, option);
}

/**
 * 功能：前置条件检查
 * @param {请求参数} params
 * @param {控制参数} option
 */
export function queryPreConditions(params, option) {
  return commonService('1005302', params, option);
}

/**
 * 功能：前置条件检查-销户账号
 * @param {请求参数} params
 * @param {控制参数} option
 */
export function queryPreAccountConditions(params, option) {
  return commonService('1005509', params, option);
}

/**
 * 功能：销户- 信息确认-查询信息
 * @param {请求参数} params
 * @param {控制参数} option
 */
export function yyxhGetConfirmInfo(params, option) {
  return commonService('1005323', params, option);
}

/**
 * 功能：销户- 信息确认-提交
 * @param {请求参数} params
 * @param {控制参数} option
 */
export function yyxhSubmitConfirmInfo(params, option) {
  return commonService('1005513', params, option);
}

/**
 * 功能：销户- 选择账号-账号查询
 * @param {请求参数} params
 * @param {控制参数} option
 */
export function yyxhGetAccount(params, option) {
  return commonService('1005511', params, option);
}

/**
 * 功能：销户- 选择账户-提交
 * @param {请求参数} params
 * @param {控制参数} option
 */
export function yyxhSubmitAccount(params, option) {
  return commonService('1005517', params, option);
}

/**
 * 功能：销户预约 - 结束销户预约流水
 * @param {请求参数} params mobile  手机号码
 * @param {控制参数} option
 */
export function yyxhEnd(params, option) {
  return commonService('1005518', params, option);
}

/**
 * 功能：销户 - 重新提交
 * @param {请求参数} params
 * @param {控制参数} option
 */
export function xhResubmit(params, option) {
  return commonService('1005519', params, option);
}

/**
 * 功能：销户 - 撤销预约销户单
 * @param {请求参数} params
 * @param {控制参数} option
 */
export function yyxhTaskCannel(params, option) {
  return commonService('1005274', params, option);
}

/**
 * 功能: 修复驳回信息(选择账户节点)
 */
export function rejectChooseAcc(params, option) {
  return commonService('1005278', params, option);
}
