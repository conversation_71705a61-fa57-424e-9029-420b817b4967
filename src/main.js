// The Vue build version to load with the `import` command
// (runtime-only or standalone) has been set in webpack.base.conf with an alias.
// 此部分为必须引用的
// must import -- start
import 'babel-polyfill'
import Vue from 'vue'
import App from './App'
import router from './router'
import store from './store'
import 'config'
// fastclick为改过源码版本
// import FastClick from 'fastclick-hvue'
// FastClick.attach(document.body)
// must import -- end
// 项目css文件
import './assets/css/common.css'
import './assets/css/animate.css'
import './assets/css/style.css'

import hvue from 'thinkive-hvue'
// let hvue = require('hvue').default
import {
  md5Encrypt,
  setCookie,
  getCookie,
  checkCookie,
  getSignParam,
  signEncrypt,
  sha1Encrypt,
  cancelRequest,
  compareVersion
} from 'thinkive-hvue'
import * as filters from './common/filter'
// 引入echarts组件
import echarts from 'echarts'
import vueBus from 'vue-bus'

// import rsaEncrypt from 'thinkive-hvue/plugin/rsa'
// console.log(rsaEncrypt('124526077649357480816469254849412191781594324527017396024739185810123852249817109685218462195329986793454658482640359680792422010850119204614548131172841791876757646890197915200605100097814135660040922082834141140370693865403196953890446922539880429238449610021745046770250475373851758995862771139688005589831', '65537', 'this is rsa encrypt!'))

// import sm2 from 'thinkive-hvue/plugin/sm/sm2'
// // import {encrypt, decrypt} from 'thinkive-hvue/plugin/sm/sm2'
// console.log('sm2:', sm2)
// // console.log(encrypt, decrypt)
// console.log('sm2.encrypt:', sm2.encrypt('1048509B0B1728949F7FBC3E0E6E625B508C4240719EC052A61AC28572085394', '74850DE7B318FD6F33BCB889AAA5326482DF2113A3A2217CDF828DC27DBBB967', 'this is hvue check sm2! 演示。@$*#%&'))
// console.log('sm2.decrypt:', sm2.decrypt('00FA2D4EE4F0296948798390C51881A51632EB34185B346CE9E1F02DCB837A4736', '0468df7d2f292347c26d4b1b05bb29c1c372fbe9947099cb32aeb6588c845b49e3eaf7cb49f8005c6a5e650cf4f505de03602c169de674721fa242f2a4b86bf0cd7a036a337e45579a901ccb4f65d6b5a669ed6838e695a9ddfecdcffe05a4a92621d8f46c0db973e24b371fc4c648cd39d2b68d9b128d94c37efa97c17d1a556d0cd7750610a350'))

// import sm3 from 'thinkive-hvue/plugin/sm/sm3'
// import { encrypt } from 'thinkive-hvue/plugin/sm/sm3'
// console.log('sm3:', sm3)
// console.log(encrypt)
// console.log('sm3.encrypt:', sm3.encrypt('this is hvue check sm3! 演示。'))

// import sm4 from 'thinkive-hvue/plugin/sm/sm4'
// import {ecbEncrypt, ecbDecrypt, cbcEncrypt, cbcDecrypt} from 'thinkive-hvue/plugin/sm/sm4'
// console.log('sm4:', sm4)
// console.log(ecbEncrypt, ecbDecrypt, cbcEncrypt, cbcDecrypt)
// console.log('sm4.ecbEncrypt:', ecbEncrypt('thinkive-hvue test sm4', 'this is hvue check sm4! 演示。@$*#%&'))
// console.log('sm4.ecbDecrypt:', ecbDecrypt('thinkive-hvue test sm4', '4ZiirILExw2v0iD0NwIMjCPn4WCHWeGYJVDcpxpD51TjMVvxLCCQ/Ubk49t6OO7L'))
// console.log('sm4.cbcEncrypt:', cbcEncrypt('thinkive-hvue test sm4', 'hvue', 'this is hvue check sm4.cbc! 演示。@$*#%&'))
// console.log('sm4.cbcDecrypt:', cbcDecrypt('thinkive-hvue test sm4', 'hvue', '5+XEkApAEs6UtTdNS7ofY/t/qHcIee+Kkm92Ln4HMvfOAPw5e2FbZGq6/ymnLkL7'))

import 'thinkive-hui/lib/theme-default/hui.css' // 引入样式库
// 引入全部组件库
// import hui from 'thinkive-hui' // 引入组件库
// Vue.use(hui)

// 按需引入组件库，分别挂载到vue原型链上
// 这里要特别引起重视是的window._hvueToast/window._hvueLoading两个全局变量
// 因为底层库hvue中用window._hvueToast/window._hvueLoading来调用Toast/Loading这两组件，
// 所以，必须在这里赋值它们。这样做是为让ui库与hvue库解藕，大家可以根据业务用其他ui库，只要把这两组件赋值给底层调用即可.
import {
  Toast,
  // Loading,
  Alert,
  Confirm,
  DateTime,
  Keypanel
} from 'thinkive-hui'

// 同时如果新的loading组件的打开和关闭两个方法名不是: open() and close()
// 还需要复写底层用到的loading组件的两个api方法: open() and close()
// 例如用vux的loading组件来更换，修改如下：
// _hvueLoading.open = Vue.$vux.loading.show
// _hvueLoading.close = Vue.$vux.loading.hide
// 或者
// window._hvueLoading = {
// 	open: Vue.$vux.loading.show,
// 	close: Vue.$vux.loading.hide
// }
// 而 _hvueToast 组件 底层主要用来提示请求错误，这模块已经暴露在业务层`netIntercept.js`，
// 如果新Toast组件的api方法或入参不一样的，大家可以直接去修改，或跟loading一样把api方法做复写.


/** 引入开发调试工具vConsole，打包生产前要注释掉
**/
import VConsole from 'vconsole' // 引入样式库
if (process.env.NODE_ENV !== 'production') {
  let _vConsole = new VConsole()
  console.log(_vConsole)
}

Vue.use(vueBus)
Vue.use(Keypanel);

// keepAliveCheck : 跳转路由后，需要缓存business.vue的路由名称配置，在此配置后，从business.vue跳转的路由，返回后business.vue会保留页面状态
let keepAliveFilter = ['agreementDetail', 'accountDetail', 'recycleList', 'blockTrade', 'blockTradeInstitution', 'businessResultDG']
Vue.mixin({
  beforeRouteLeave: function (to, from, next) {
    // 判断是否需要缓存business.vue
    console.log(this)
    if (from.path == '/business' && keepAliveFilter.join(',').indexOf(to.path.replace(/\//, '')) > -1) {
      // 保存business.vue
      this.$store.commit('updateCachePath', ['business'])
    } else if (from.path == '/business' && to.path == '/index') {
      // this.$destroy()
      this.$store.commit('updateCachePath', [])
    } else if (from.path == '/index' && to.path == '/business') {
      // this.$destroy()
      this.$store.commit('updateCachePath', [])
    } else {
      // this.$destroy()
    }
    // 必须使用setTimeout 解决第二次进入业务 需要缓存business.vue时  不生效的问题
    setTimeout(() => {
      next()
    })
  }
})
// 引入echarts组件
Vue.prototype.$echarts = echarts

Object.keys(filters).forEach(key => {
  Vue.filter(key, filters[key])
})

Vue.directive('throttle', {
  // 添加指令用于对点击事件节流
  bind: function (el, binding) {
    let time = binding.value || 1000
    let preTime = new Date().getTime()
    el.addEventListener('touchstart', e => {
      const nowTime = new Date().getTime()
      if (preTime && nowTime - preTime < time) {
        e.preventDefault()
        e.stopImmediatePropagation()
      } else {
        preTime = nowTime
      }
    })
  }
})

// console.log(Vue.prototype)
// console.log(hvue)
Vue.prototype.$h = window.$h = hvue
// console.log(md5Encrypt)

// console.log(getSignParam({
// 	funcNo: '4234324',
// 	name: 'fang',
// 	sex: 'man'
// }, 'ssoSignNew'))
// console.log(signEncrypt('4daceda913784b528cbbbe4e75c29f53'))
// console.log('sign2:', signEncrypt('243D73C8D635A0A3DB3C0686D9C10D1A', '2.0'))
// console.log(decodeBase64('dGhpcyBpcyB0aGlua2l2ZS1odnVlIGRlbW8='))
// console.log(sha1Encrypt('liu fang de/is test!'))
// console.log(desDecrypt('fang', 'l553whQ2t6CNA2ACaP8qij8W+ZqhtIo/'))
// console.log(window.$hvue)
console.log(compareVersion('1.34.53', '2.4'))
window._hvueToast = Toast
// window._hvueLoading = Loading
window._hvueAlert = Alert
window._hvueConfirm = Confirm
// 重写加载层
window._hvueLoading = {
  closeLoading () {
    store.commit('hideLoading')
  },
  openLoading (tips) {
    store.commit('showLoading', tips)
  },
  close () {

  }
}

Vue.use(DateTime)
Vue.config.productionTip = false
// 解决ios下css: active伪类无效果问题
document.addEventListener('touchstart', function () {}, {
  passive: false
})
/* eslint-disable no-new */


if ($hvue.platform != 0) {
  // 通知原生已经加载完毕模块
  $h.callMessageNative({
    'funcNo': '50100',
    'moduleName': 'ygt'
  })
  console.log('模块加载完成...')

  let param = {
    'funcNo': '50024'
  }
  let result = $h.callMessageNative(param)
  if (result.error_no == '0' || result.error_no == '1') {
    $h.setSession('mac', result.results[0].mac) // 将 mac 保存到 sessionStorage 里面
  }
}

// 注册Vue实例
new Vue({
  el: '#app',
  router,
  store,
  render: h => h(App)
  // components: { App },
  // template: '<App/>'
})
/**
 * 删除数组中某一个元素
 */
Array.prototype.remove = function (val) {
  let index = this.indexOf(val)
  if (index > -1) {
    this.splice(index, 1)
  }
}
