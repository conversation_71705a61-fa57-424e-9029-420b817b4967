const config = {
  // 请求签名所需参数对象
  ssoSign: {
    merchant_id: 'financeweb', // 商户id
    // 后端给的加签key再用signEncrypt方法加密后的key
    sign_key: 'OdDd+M4OHraSurI5/yRW3i1DohAZihnuvYOlohQ3r1DjyFimJqJy1w==',
    corp_id: '101000000001', // 统一账户公司编号
    app_id: '102000000001', // 统一账户应用编号
    encrypt_mode: '', // 请求参数加密模式: aes, des, base64. 默认base64.
    encrypt_key: '',
    sign_version: '1.0' // 加签名方法的版本，可与后端确定版本号。version: '1.0' or '2.0'
  },
  ssoSignNew: {
    sign_key: 'm53TzVkpa0OB4+zAawoUDxf+gYmPfy77/5HYbJ6oKHEKcZ264wPVjw==',
    company_id: 'THINKIVE', // 公司id
    system_id: 'TAMP', // 系统id
    encrypt_mode: 'none', // 请求参数加密模式: aes, des, base64, none. 默认base64
    encrypt_key: '',
    system_version: '1.0.2', // 系统版本, 这个值固定 '1.0.2'
    sign_version: '2.0' // 加签名方法的版本，可与后端确定版本号。version: '1.0' or '2.0'
  },
  sm4Key: 'f0f91d79220c5ec9f3c013a5b4499cb2', // 加密秘钥
  // session数据保存到app内存中, true/false, 默认false
  sessionSaveToApp: false,
  // 开启session数据保存到app内存中选项后，需要配置此项来过滤存储的key,
  // 配置在数组中key才会存在app内存中
  sessionKeyToApp: [],

  useTYSP: '1', // 是否使用统一视频，1是0否
  videoType: '0', // 视频类型 0tchat 1anychat
  ocrQuality: '1', // 是否开启拍照质检

  // 测试视频服务配置
  serviceType: '1', // 运营商类型
  serviceTypeUrl: 'h5sdsp.dtsbc.com.cn:8906', // 视频服务器url
  imgUrl: 'https://h5sdkh.dtsbc.com.cn:22203/picServlet?upload_id=' // 开户接入获取银行图标地址

    // 生产移动视频服务配置
  // serviceType: '0',
  // serviceTypeUrl: 'spyd.dtsbc.com.cn:8906',
  // imgUrl: 'https://khyd.dtsbc.com.cn:22203/picServlet?upload_id=' // 开户接入获取银行图标地址--移动

  // 生产电信视频服务配置
  // serviceType: '1',
  // serviceTypeUrl: 'spdx.dtsbc.com.cn:8906',
  // imgUrl: 'https://khdx.dtsbc.com.cn:22203/picServlet?upload_id=' // 开户接入获取银行图标地址--电信

  // 生产联通视频服务配置
  // serviceType: '2',
  // serviceTypeUrl: 'splt.dtsbc.com.cn:8906',
  // imgUrl: 'https://khlt.dtsbc.com.cn:22203/picServlet?upload_id=' // 开户接入获取银行图标地址--联通

}

window.$hvue = {
  config
}
export default config
